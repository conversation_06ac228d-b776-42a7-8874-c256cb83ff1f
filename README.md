# Inventory project
This project is used to generate our inventory in AWX server. It works with Device42 inventory via a modified Device42 
inventory collection and with Leaseweb inventory via a lsw.py script.

## (OPTIONAL) How to install virtualenv
This step is optional. For python projects it's **recommended** to work with virtual environments and have each project 
in its own virutalenv.

### Install
* [virtualenv](https://virtualenv.pypa.io/en/latest/installation.html)
* [virtualenvwrapper](https://virtualenvwrapper.readthedocs.io/en/latest/install.html)

with
- <PERSON><PERSON>
```shell
brew install virtualenv
brew install virtualenvwrapper
```

- Generic
```shell
pip3 install virtualenv
pip3 install virtualenvwrapper
```

### Add the following lines to your .bashrc or .zshrc
- If installed thru brew
```
export WORKON_HOME=~/virtualenv
/opt/homebrew/bin/virtualenvwrapper.sh
```
- Generic
```
export WORKON_HOME=~/virtualenv
source /usr/local/bin/virtualenvwrapper.sh
```

and reload it with `source ~/.bashrc` or `source ~/.zshrc`

### Create new virtualenv with
```shell
mkvirtualenv sms-ansible-inventory
```

### Activation
* `workon sms-ansible-inventory` will switch your env to venv ansible
* `deactivate` will switch your env to default

## Install requirements
```
pip install -r requirements.txt
```

## Development
- Update collections with
```
ansible-galaxy collection install -r collections/requirements.yml -p collections
```

## Credentials
Both Device42 and Leaseweb inventory scripts need credentials to work. Here is how you can pass them:
- Device42: Credentials must be passed as D42_USER and D42_PWD environment variables
- Leaseweb: Credentials must be passed as LEASEWEB_TOKEN environment variable. You will get different inventory based 
on which token you passed. Each datacenter has its own token.

As an example check [credential types](https://awx-dog.outbrain.com/#/credential_types) and 
[credentials](https://awx-dog.outbrain.com/#/credentials) in our current AWX.
