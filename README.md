## Install repo tool:
[repo](https://gerrit.googlesource.com/git-repo)

## Install dependencies

<i>It is highly recommended that you work in a virtual environment.</i>

You can install them with the following command:

```pip install -r requirements.txt```

## Usage:
```
mkdir -p PATH_TO_PROJECTS/sms-ansible-repo 
cd PATH_TO_PROJECTS/sms-ansible-repo  
repo init -u ssh://<EMAIL>/outbrain/sms-ansible-repo.git
```

You have initialized `sms-ansible-repo` which means that you can sync it with `repo sync`. 

You should always sync before starting to work on this collection of repos. After that you can work with each repo individually (std git repo)
