# Ansible project

## (OPTIONAL) How to install virtualenv
This step is optional. For python projects it's **recommended** to work with virtual environments and have each project in it's own virutalenv.

### Install 
* [virtualenv](https://virtualenv.pypa.io/en/latest/installation.html)
* [virtualenvwrapper](https://virtualenvwrapper.readthedocs.io/en/latest/install.html)

with
- MacOS
```shell
brew install virtualenv
brew install virtualenvwrapper
```

- Generic
```shell
pip3 install virtualenv
pip3 install virtualenvwrapper
```

### Add the following lines to your .bashrc or .zshrc
- If installed thru brew
```
export WORKON_HOME=~/virtualenv
/opt/homebrew/bin/virtualenvwrapper.sh
```
- Generic
```
export WORKON_HOME=~/virtualenv
source /usr/local/bin/virtualenvwrapper.sh
```

and reload it with `source ~/.bashrc` or `source ~/.zshrc`

### Create new virtualenv with
```shell
mkvirtualenv sms-ansible
```

### Activation
* `workon sms-ansible` will switch your env to venv ansible
* `deactivate` will switch your env to default

## Install requirements
```
pip install -r requirements.txt
```

## Development
- Update collections with
```
ansible-galaxy collection install -r collections/requirements.yml -p collections
```
- Setup your inventory by adding hosts to the 'test_hosts' group in the inventory/hosts file
```
[test_hosts]
    dog-10001-prod-nydc1.nydc1.outbrain.com
```
- Run your tasks by setting the 'sms_tasks' extra var as a list
```
ansible-playbook -i inventory/hosts playbooks/test.yml -e "sms_tasks=[{'task':'TASK','vars':{'VAR_NAME':'VAR_VALUE'}}]"
```
- Run you tasks with options
```
ansible-playbook -i inventory/hosts playbooks/test.yml \
  -e "sms_tasks=[{'task':'TASK1','vars':{'VAR_NAME':'VAR_VALUE'}}, {'task':'TASK2','vars':{'VAR_NAME':'VAR_VALUE'}}]"
  -e "option_parallelism=INT" \
  -e "option_any_errors_fatal=BOOL" \
  -e "option_max_fail_percentage=INT" \
  -e "option_dry_run=BOOL"
```
Examples
```
ansible-playbook -i inventory/hosts playbooks/test.yml -e "sms_tasks=[{'task':'hello','vars':{}}]"
ansible-playbook -i inventory/hosts playbooks/test.yml -e "sms_tasks=[{'task':'wait','vars':{'wait_seconds':30}}]"
ansible-playbook -i inventory/hosts playbooks/test.yml \
  -e "sms_tasks=[{'task':'wait','vars':{'wait_seconds':30}}]" \
  -e "option_parallelism=4" \
  -e "option_any_errors_fatal=true" \
  -e "option_max_fail_percentage=15" \
  -e "option_dry_run=true"
```
Notice that ansible will execute the tasks as **your user** on the remote servers!
 
! if you running tests from mac, you may need to comment out ssh_common_args in [ansible.cfg](ansible.cfg) for ssh to work 

### (NOT ADVISED) Running ansible against production servers
- Install the `requirements_dev.yml` as well with 
```
ansible-galaxy collection install -f -r collections/requirements_dev.yml -p collections
```
- Add username and password to `outbrain.d42.yml` file. **Pay attention NOT to commit this file!**
- Example command
```
ansible-playbook -i outbrain.d42.yml playbooks/tasks_playbook_serial.yml -e "debug_ansible_ssh_user=$USER" -e '{"inventory_filters": ["name:dog-10001.*"], "sms_tasks":[{"task":"hello","vars":{}}]}' --diff
```
add -vvv for more debug output

### Filter plugins
Filter plugins are custom plugins that you can develop and include into ansible. 

- Inventory filter
Inventory filter has dependency on the `library/path_required.py` module and in order to run it locally you need add that 
module to PYTHONPATH.
`export PYTHONPATH=$PYTHONPATH:../`

It is used to filter the inventory on the fly since it is the fastest way to do it. An alternative would be to create
Smart inventories in AWX but that would create a lot of bloat on it. AWX jobs are always triggered on a subset of 
servers and since we have 1 inventory of all servers in AWX those must be filtered on the fly. When the playbook is
started the whole inventory is passed to it and then with `inventory_plugin.py` we filter only the servers that we want.

### Tests
Testing is done in molecule. From the molecule folder you can run
```
molecule test
```

| Scenario | Command |
|----------|---------|
| consul_check_node | `molecule test -s consul_check_node` |

### Internal tasks
Internal tasks are automatically appended to the task list by SMS BE. They are used to control the execution and send 
notification.
