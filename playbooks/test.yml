---
- name: Test playbook
  hosts: test_hosts
  gather_facts: false
  user: "{{ lookup('env', 'USER') }}"
  serial: "{{ option_parallelism | default(1) }}"
  any_errors_fatal: "{{ option_any_errors_fatal | default('false') | bool }}"
  max_fail_percentage: "{{ option_max_fail_percentage | default(10) }}"
  check_mode: "{{ option_dry_run | default(False) | bool }}"
  become: true
  pre_tasks:
    - setup:
        gather_subset:
          - "!hardware"
          - "network"
        gather_timeout: 60
      async: 300
      poll: 10
      check_mode: false
  tasks:
    - name: Run Hadoop master checks
      include_tasks: "../tasks/hadoop_nn_failover.yml"
      tags: [hadoop, checks]

#    - name: Include tasks
#      include_tasks:
#        file: "../tasks/{{ sms_task['task'] }}.yml"
#      vars:
#        sms_vars: "{{ sms_task['vars'] }}"
#      loop: "{{ sms_tasks }}"
#      loop_control:
#        loop_var: sms_task
