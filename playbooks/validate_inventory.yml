# Example usage: ansible-playbook -i outbrain.d42.yml playbooks/validate_inventory.yml -e '{"inventory_filters": ["name:zemb1agent-10001.*"]}'
---
- name: Print inventory
  hosts: localhost
  connection: local
  gather_facts: false
  tasks:
    - name: Set facts
      set_fact:
        filtered_inventory: "{{ hostvars | inventory_filter(inventory_filters) }}"

    - name: Print inventory
      debug:
        msg: "inventory_servers={{ filtered_inventory | to_json }}"
