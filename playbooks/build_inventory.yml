# This playbook builds the inventory. It is intended to use in combination with other playbooks
---
- name: Build inventory
  hosts: localhost
  connection: local
  gather_facts: false
  tasks:
    - name: Build hostgroup
      add_host:
        name: "{{ item }}"
        ansible_ssh_user: "{{ debug_ansible_ssh_user | default('root') }}"
        ansible_ssh_host: "{{ item }}"
        ansible_ssh_port: "{{ debug_ansible_ssh_port | default(22) }}"
        ansible_connection: "{{ debug_ansible_connection | default('ssh') }}"
        groups: filtered_inventory
      loop: "{{ hostvars | inventory_filter(inventory_filters, debug_skip_filter | default(False) | bool) }}"
      changed_when: false
