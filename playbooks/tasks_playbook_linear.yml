# Usage:
# ansible-playbook -i PATH_TO_INVENTORY PATH_TO_PLAYBOOK --extra-vars '{"sms_tasks":[{"task":"TASK1","vars":{}},{"task":"TASK2","vars":{}}], "inventory_filters":["field1:value1", "field2:value2"]}'
#
# Prod usage example:
# ansible-playbook -i outbrain.d42.yml playbooks/tasks_playbook_linear.yml -e '{"inventory_filters": ["name:zemb1agent-10001.*"], "sms_tasks":[{"task":"hello","vars":{}}]}'
#
# Test with local inventory:
# ansible-playbook -i inventory/hosts playbooks/tasks_playbook_linear.yml -e '{"inventory_filters": ["name:dog-10001.*"], "sms_tasks":[{"task":"hello","vars":{}}]}' -e debug_ansible_ssh_user=$USER -e debug_skip_filter=True --diff
---
- name: Build inventory
  import_playbook: build_inventory.yml

- name: Standard playbook
  hosts: "{{ groups['filtered_inventory'][:option_max_hosts | int] if option_max_hosts is defined else groups['filtered_inventory'] }}"
  check_mode: "{{ option_dry_run | default(False) }}"
  gather_facts: false
  user: awx
  become: true
  vars:
    sms_check_mode: "{{ option_dry_run | default(False) }}" # ansible_check_mode doesn't work so always use sms_check_mode
  vars_files:
    - ../vars/all.yml
  pre_tasks:
    - setup:
        gather_subset:
          - "!hardware"
          - "network"
        gather_timeout: 60
      async: 300
      poll: 10
      check_mode: false
  tasks:
    - name: Include tasks
      include_tasks:
        file: "../tasks/{{ sms_task['task'] }}.yml"
      vars:
        sms_vars: "{{ sms_task['vars'] }}"
      loop: "{{ sms_tasks }}"
      loop_control:
        loop_var: sms_task
