import os
import re
import sys

from ansible.errors import AnsibleError
from ansible.vars.hostvars import HostVars, HostVarsVars

# add library to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'library'))
import patch_required

HOSTNAME_FIELD = "name"
LAST_PATCH_FIELD = "patch_last_updated"
ARRAY_FIELDS = ["consul_tags"]

FILTER_KEY_PATCHED = "patched"


def _validate_vars(hostvars, inventory_filters):
    if type(hostvars) is not HostVars:
        raise AnsibleError('hostvars should be a dict.')

    if not hostvars:
        raise AnsibleError('hostvars should not be empty.')

    if type(inventory_filters) is not list:
        raise AnsibleError('inventory_filters should be a list.')

    if len(inventory_filters) == 0:
        raise AnsibleError('inventory_filters should not be empty')


# if negative and found then filter it out
# if not negative and not found then filter it out
def _should_filter(result, negative):
    return result if negative else not result


def _filter_array(filter_value, server_value, negative):
    found = False
    items = server_value.split(",")
    for item in items:
        if re.match(filter_value, item):
            found = True
            break

    return _should_filter(found, negative)

def _filter_last_patch(filter_value, server_value, valid_quarters):
    negative = filter_value in ["False", "false"]
    return _should_filter(server_value in valid_quarters, negative)


def _filter_string(filter_value, server_value, negative):
    if type(server_value) is not str:
        server_value = str(server_value)

    re_res = re.match(filter_value, str(server_value))
    return _should_filter(re_res, negative)


def inventory_filter(hostvars, inventory_filters, debug_skip_filter=False, ignore_missing_fields=True):
    _validate_vars(hostvars, inventory_filters)
    valid_quarters = patch_required.valid_quarters()

    filtered_hostvars = []
    for hostname in hostvars:
        server_vars: HostVarsVars = hostvars[hostname]

        if debug_skip_filter:
            filtered_hostvars.append(server_vars['inventory_hostname'])
            continue

        # go thru all filters for server
        for inventory_filter in inventory_filters:
            negative = False
            if inventory_filter.startswith("!"):
                negative = True
                inventory_filter = inventory_filter[1:]

            idx = inventory_filter.find(":")
            if idx < 0:
                raise AnsibleError(
                    f'Inventory filter {inventory_filter} is not in the correct format. Expected format: FIELD_NAME:REGEX_VALUE')

            filter_key = inventory_filter[:idx]
            if filter_key == FILTER_KEY_PATCHED:
                # remap "patched" to "last_patch_updated"
                filter_key = LAST_PATCH_FIELD
            filter_value = inventory_filter[idx + 1:]

            # validate key is available
            if filter_key not in server_vars:
                if ignore_missing_fields:
                    break
                else:
                    raise AnsibleError('{filter_key} not present in host vars. Available keys are: {available_keys}'.format(
                        filter_key=filter_key, available_keys=", ".join(server_vars.keys())))

            if filter_key in ARRAY_FIELDS:  # find at least 1 match in the array
                if _filter_array(filter_value, server_vars[filter_key], negative):
                    break
            elif filter_key == LAST_PATCH_FIELD:
                if _filter_last_patch(filter_value, server_vars[filter_key], valid_quarters):
                    break
            else:
                if _filter_string(filter_value, server_vars[filter_key], negative):
                    break

        else:  # when break is not called in the loop
            filtered_hostvars.append(server_vars[HOSTNAME_FIELD])

    return sorted(filtered_hostvars)


class FilterModule(object):
    def filters(self):
        return {
            'inventory_filter': inventory_filter,
        }
