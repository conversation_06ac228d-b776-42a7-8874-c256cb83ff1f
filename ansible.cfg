# https://docs.ansible.com/ansible/latest/reference_appendices/config.html
# https://github.com/ansible/ansible/blob/devel/examples/ansible.cfg
[defaults]
gathering = smart
fact_caching = jsonfile
fact_caching_connection = tmp/ansible_cache
fact_caching_timeout = 3600
roles_path = roles
collections_path = collections
playbook_dir = playbooks
filter_plugins = filter_plugins
library = library
remote_tmp = /tmp/ansible-${USER}

ansible_managed = Ansible managed
forks = 5
host_key_checking = False
interpreter_python = auto
timeout = 60

[ssh_connection]
pipelining = True
# if you running tests from mac, you may need to comment out next line for ssh to work
ssh_common_args = "-o StrictHostKeyChecking=no -o ControlMaster=auto -o ControlPersist=60s -o UserKnownHostsFile=/dev/null"

# this is due to https://github.com/ansible/ansible/issues/12144
# if you don't have access to /tmp change accordingly
control_path = /tmp/%%h-%%p-%%r
retries = 10
