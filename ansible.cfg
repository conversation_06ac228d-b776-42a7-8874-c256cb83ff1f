[defaults]
gathering = smart
fact_caching = jsonfile
fact_caching_connection = tmp/ansible_cache
fact_caching_timeout = 3600
collections_path = collections

ansible_managed = Ansible managed
forks = 5
host_key_checking = False
interpreter_python = auto

[ssh_connection]
pipelining = True

# this is due to https://github.com/ansible/ansible/issues/12144
# if you don't have access to /tmp change accordingly
control_path = /tmp/%%h-%%p-%%r
