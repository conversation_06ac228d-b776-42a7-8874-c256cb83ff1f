---
plugin: dog.sms.azure_rm
cache: true
cache_timeout: 300

hostnames:
  - computer_name
datacenters:
  - nldc1
  - nldc2
  - wndc1
  - sngdc1
include_host_filters:
  - computer_name.endswith('outbrain.com')

# define custom fields mapping - adjusted to d42 structure
hostvar_expressions:
  name: computer_name
  datacenter: tags.dc
  owner: tags.owner
  patch_last_updated: tags.patch_last_updated | default('n/a')
  environment: name.split('-')[2] | default('n/a')
  model: virtual_machine_size
  ip: private_ipv4_addresses[0]

# for local testing
#tenant:
#secret:
#client_id:
#subscription_id:
