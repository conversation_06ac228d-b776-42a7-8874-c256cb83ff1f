#!/usr/bin/env python
## THIS IS BUNDLED PYTHON SCRIPT
##
"""
Leaseweb REST Client
"""
import json
import logging
import os
from typing import Mapping

from requests import Session

logger = logging.getLogger(os.path.abspath(__file__))


class LeasewebClient:
    def __init__(self, auth_key: str, endpoint: str = "https://api.leaseweb.com"):
        self.rest_client = LeasewebSession(auth_key, endpoint)
        self.limit = 50

    def get_server(self, server_id: str):
        result = self.rest_client.get("/bareMetals/v2/servers/{}".format(server_id))
        return result.json()

    def get_servers(self):
        return self._get("/bareMetals/v2/servers", "servers")

    def _decode_result(self, response, result_field: str):
        metadata = ResponseMetadata(response.get("_metadata"))
        data = response[result_field]
        return data, metadata

    def _get(self, uri, result_field):
        result = []
        offset = 0
        while True:
            try:
                params = [("limit", self.limit), ("offset", offset)]
                response = self.rest_client.get(
                    url=uri,
                    params=params,
                )
                logger.debug(response)
                if response.status_code != 200:
                    raise APIErrorException

                data, metadata = self._decode_result(response.json(), result_field)
                result.extend(data)

                if len(data) < self.limit:
                    break
                offset += self.limit
            except Exception as exc:
                logger.warning(exc)
                raise

        return result


class APIErrorException(Exception):
    pass


class ResponseMetadata:
    def __init__(self, metadata: Mapping):
        self.limit = int(metadata.get("limit"))
        self.offset = int(metadata.get("offset"))
        self.totalCount = int(metadata.get("totalCount"))


class LeasewebSession(Session):
    def __init__(self, auth_key, endpoint="https://api.leaseweb.com"):
        self.endpoint = endpoint
        self.auth_key = auth_key
        super(LeasewebSession, self).__init__()
        self.headers.update({"Content-Type": "application/json", "X-Lsw-Auth": auth_key})

    def request(self, method, url, data=None, headers={}, **kwargs):
        full_url = "{}/{}".format(self.endpoint, url.lstrip("/"))
        logger.debug("Full URL: %s" % full_url)
        return super(LeasewebSession, self).request(
            method, full_url, headers=self.headers, data=data, **kwargs
        )

###############################

"""
Leaseweb Inventory Class
"""

from collections import defaultdict
from json.decoder import JSONDecodeError
from typing import List, Mapping

DATACENTER_MAPPING = {"AMS-01": "eudc1", "SIN-01": "sindc1"}


class LeasewebGroup:
    def __init__(self, group, dc):
        self.name = to_safe("{}_{}".format(group, dc))

        _ancestors = list()
        _ancestors.append(to_safe("{}".format(group)))
        _ancestors.append(to_safe("{}".format(dc)))
        self.ancestors = _ancestors

    def get(self):
        return self.name

    def get_ancestors(self):
        return self.ancestors


class LeasewebNode:
    ansible_facts = None

    def __init__(
            self,
            node,
            groups: list = [
                "location_site",
                "location_suite",
                "groups",
            ],
    ):
        self.facts = node
        self.reference = self.get_reference()
        self.name = self._get_name()
        self.id = self._id()
        self.ip = self._ip()
        self.mac = self._mac_address()
        self.datacenter = self._datacenter()
        self.facts["datacenter"] = self.datacenter
        self.ansible_facts = self.get_facts()
        self.groups = self.group_by(groups)

    def get(self):
        return self.name

    def group_by(self, groups):
        _groups = list()
        for g in groups:
            _group = self.ansible_facts.get(g)
            if isinstance(_group, list):
                for _g in _group:
                    # skip giving prefix to 'groups'
                    if g == "groups":
                        _groups.append(LeasewebGroup(_g, self._datacenter()))
                    else:
                        _groups.append(to_safe("{}_{}_{}".format(g, _g, self._datacenter())))
            else:
                _groups.append(to_safe("{}_{}".format(g, _group)))
        return _groups

    def get_reference(self):
        try:
            # reference field is in CSV format
            reference_field = self.facts["contract"]["reference"]
            items = reference_field.split(",")

            reference = {}
            for item in items:
                key, val = item.split(":")

                # if value is a list, convert it to a list
                if val.startswith("[") and val.endswith("]"):
                    val = val[1:-1].split(",")

                reference[key] = val
        except Exception:
            reference = {}
        return reference

    def get_facts(self):
        host = None
        facts = self.facts

        reference = self.get_reference()
        if isinstance(reference, dict):
            facts.update(reference)
            host = reference.get("ip", None)

        ansible_facts = self._flatten(facts.copy())

        ansible_facts["ansible_host"] = host or self.ip
        ansible_facts["name"] = self.name
        ansible_facts["environment"] = "prod"
        ansible_facts["model"] = ansible_facts["specs_chassis"]
        ansible_facts["ram"] = ansible_facts["specs_ram_size"]
        ansible_facts["ip"] = host or self.ip
        ansible_facts["hid"] = ansible_facts["id"]
        ansible_facts["cpucount"] = ansible_facts["specs_cpu_quantity"]
        ansible_facts["patch_last_updated"] = "unknown"

        return sorted_dict(ansible_facts)

    def _get_key_from_interface(self, key):
        try:
            public_ifc = self.facts["networkInterfaces"]["public"]
            if public_ifc and public_ifc["ip"]:
                return self.facts["networkInterfaces"]["public"][key]
        except KeyError:
            pass

        try:
            private_ifc = self.facts["networkInterfaces"]["private"]
            if private_ifc and private_ifc["ip"]:
                return self.facts["networkInterfaces"]["private"][key]
        except KeyError:
            pass

        return ""

    def _ip(self):
        try:
            return self.reference["ip"]
        except (KeyError, TypeError, JSONDecodeError):
            pass

        ip = self._get_key_from_interface("ip")
        if not ip:
            return ""

        return ip.split("/")[0]

    def _mac_address(self):
        return self._get_key_from_interface("mac")

    def _id(self) -> str:
        return self.facts["id"]

    def _datacenter(self) -> str:
        ref_fqdn = self.reference.get("node", "")
        if len(ref_fqdn.split('.')) == 5:
            ref_dc = ref_fqdn.split('.')[1]
            return ref_dc
        return DATACENTER_MAPPING.get(self.facts["location"]["site"], "Unknown")

    def _get_name(self) -> str:
        try:
            return self.reference["node"]
        except (KeyError, TypeError, JSONDecodeError):
            return self._ip()

    def _flatten(self, in_dict, dict_out=None, parent_key=None, separator="_") -> Mapping:
        if dict_out is None:
            dict_out = {}

        for k, v in in_dict.items():
            k = f"{parent_key}{separator}{k}" if parent_key else k
            if isinstance(v, dict):
                self._flatten(in_dict=v, dict_out=dict_out, parent_key=k)
                continue
            dict_out[k] = v

        return dict_out

    def __repr__(self) -> str:
        return "%s(%s)" % (self.__class__, self._get_name())


def to_safe(word: str) -> str:
    """Converts 'bad' characters in a string to underscores so they can be used as Ansible groups"""
    BAD_CHARS = ".-/:;"
    for c in BAD_CHARS:
        word = word.replace(c, "_")
    return word


def sorted_dict(d: Mapping) -> Mapping:
    return dict(sorted(d.items(), key=lambda item: item[0]))


class LeasewebInventory:
    def __init__(self, inventory: Mapping, skip_unprovisioned: bool):
        self.nodes = list()
        self.groups = defaultdict(lambda: defaultdict(list))
        for n in inventory:
            node = LeasewebNode(n)

            if skip_unprovisioned and "groups_None" in node.groups:
                continue
            self.nodes.append(node)

        for node in self.all():
            for group in node.groups:
                if isinstance(group, LeasewebGroup):
                    self.groups[group.get()]["hosts"].append(node.get())

                    for parent in group.get_ancestors():
                        if group.get() not in self.groups[parent]["children"]:
                            self.groups[parent]["children"].append(group.get())
                else:
                    self.groups[group]["hosts"].append(node.get())

    def all(self) -> List[LeasewebNode]:
        return sorted(self.nodes, key=lambda x: x.name)

    def get_host_obj(self, search: str) -> LeasewebNode:
        for node in self.nodes:
            if search in [node.name, node.id]:
                return node

    def get_host(self, host) -> Mapping:
        obj = self.get_host_obj(host)
        return self.get()["_meta"]["hostvars"].get(obj.name, dict())

    def get(self) -> Mapping:
        inv = self.groups
        inv["_meta"] = dict()
        inv["_meta"]["hostvars"] = sorted_dict({node.get(): node.ansible_facts for node in self.nodes})
        return inv

    def to_dict(self):
        return [node.facts for node in self.all()]

###############################

import json
import os
import time
from hashlib import md5

CACHE_FILE = "/tmp/.ansible-inventory-lsw-{}.cache"
CACHE_TTL = int(os.environ.get("LEASEWEB_CACHE_TTL", False) or 3600)


class NoTokenException(Exception):
    pass


def print_json(dict_obj):
    try:
        output = json.dumps(dict_obj, indent=2)
    except json.JSONDecodeError:
        output = dict_obj
    print(output)


def get_client() -> LeasewebClient:
    auth_key = os.environ.get("LEASEWEB_TOKEN", None)
    if not auth_key:
        raise NoTokenException("Please provide LEASEWEB_TOKEN as environment variable")
    return LeasewebClient(auth_key=auth_key)


def get_cache_filename(api_client):
    return CACHE_FILE.format(md5(api_client.rest_client.auth_key.encode()).hexdigest()[0:8])


def cache_outdated(filename):
    try:
        fstat = os.stat(filename)
        if int(time.time()) - fstat.st_mtime > CACHE_TTL:
            return True
    except FileNotFoundError:
        return True
    return False


def invalidate_cache(api_client):
    try:
        filename = get_cache_filename(api_client)
        os.remove(filename)
    except FileNotFoundError:
        pass


def get_inventory(refresh_cache: bool = False, skip_unprovisioned: bool = False) -> LeasewebInventory:
    api_client = get_client()

    filename = get_cache_filename(api_client)

    if refresh_cache or cache_outdated(filename):
        data = api_client.get_servers()
        fp = open(filename, "w")
        json.dump(data, fp)
    else:
        data = json.load(open(filename, "r"))

    return LeasewebInventory(data, skip_unprovisioned)


def str_list(s: str) -> list:
    return s.replace(" ", "").split(",")


"""
Leaseweb Ansible inventory script
"""
######################
from argparse import ArgumentParser


def main():
    parser = ArgumentParser(description="Produce an Ansible Inventory file based on Leaseweb API")
    parser.add_argument(
        "--list",
        action="store_true",
        default=True,
        help="List instances (default: True)",
    )
    parser.add_argument("--host", action="store", help="Get all the variables about a specific instance")
    parser.add_argument(
        "--refresh-cache",
        action="store_true",
        default=False,
        help="Force refresh of cache by making API requests to Leaseweb (default: False - use cache files)",
    )

    args = parser.parse_args()
    inventory = get_inventory(refresh_cache=args.refresh_cache, skip_unprovisioned=True)

    if args.host:
        print_json(inventory.get_host(host=args.host))
    elif args.list:
        print_json(inventory.get())
    else:
        # by default output empty JSON dict
        print_json(dict())


if __name__ == "__main__":
    main()
