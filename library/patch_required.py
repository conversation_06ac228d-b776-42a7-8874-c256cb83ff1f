#!/usr/bin/python
import json
from datetime import timezone, datetime

from ansible.module_utils.basic import AnsibleModule

LAST_UPDATE_PATH = "/var/log/unattended-upgrades/last_update"


def run_module():
    module_args = dict(
        os_patch_after_days=dict(type='int', required=False)
    )

    result = dict(
        changed=False,
        original_message='',
        message='',
    )

    module = AnsibleModule(
        argument_spec=module_args,
        supports_check_mode=True
    )

    os_patch_after_days = module.params['os_patch_after_days']

    # get last patch file
    try:
        with open(LAST_UPDATE_PATH, 'r') as file:
            content = file.read().strip()
    except Exception as e:
        # file doesn't exists
        result["patch_required"] = True
        result["message"] = "File doesn't exists, patch required"
        module.exit_json(**result)

    # parse json
    try:
        last_patch_dict = json.loads(content)
        message, patch_required = check_patch_required(last_patch_dict, os_patch_after_days)
    except Exception as e:
        # handle old format
        if os_patch_after_days is not None:
            # no date info, can't check so return patch required
            patch_required = True
            message = "No date info, can't check if patch is required. Patching"
        else:
            last_patch_dict = {
                "quarter": content
            }
            message, patch_required = check_patch_required(last_patch_dict, None)

    result["patch_required"] = patch_required
    result["message"] = message
    module.exit_json(**result)


def check_patch_required(last_patch_dict, os_patch_after_days):
    # if os_patch_after_days then do days diff check
    if os_patch_after_days:
        patch_timestamp = last_patch_dict["epoch"]

        # calc days diff
        patch_datetime = datetime.fromtimestamp(int(patch_timestamp), tz=timezone.utc)
        current_datetime = datetime.now(timezone.utc)
        time_difference = current_datetime - patch_datetime
        days_passed = time_difference.days
        message = f"{days_passed} days passed since last patch. {'Patch is required' if days_passed >= os_patch_after_days else 'Patch is not required'}"
        return message, days_passed >= os_patch_after_days

    # check if patch in valid quarters
    valid_q = valid_quarters()
    patch_q = last_patch_dict.get("quarter", "")
    message = f"Last patched in {patch_q}. {'Patch is required' if patch_q not in valid_q else 'Patch is not required'}"
    return message, patch_q not in valid_q


def valid_quarters():
    result = []

    ts = datetime.now(timezone.utc)
    for i in range(0, 2):
        ts = _quarters_back(ts, i)
        result.append(f"{ts.year}q{(ts.month - 1) // 3 + 1}")

    return result


def _quarters_back(t1, n):
    t = t1
    for _ in range(n):
        t = _quarter_back(t)
    return t


def _quarter_back(t1):
    t1_year = t1.year
    t1_month = t1.month

    t2_month = t1_month - 3
    if t2_month <= 0:
        t2_month += 12
    t2_year = t1_year - 1 if t2_month > t1_month else t1_year

    t2 = t1.replace(year=t2_year, month=t2_month, day=1)
    return t2


def main():
    run_module()


if __name__ == '__main__':
    main()
