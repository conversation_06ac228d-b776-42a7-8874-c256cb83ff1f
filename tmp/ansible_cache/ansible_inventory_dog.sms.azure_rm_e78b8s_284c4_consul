{"aux-20010-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-consul-service-metrics", "owner-opsinfra", "aux-20010-prod-nldc2.nldc2", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus", "servicetype-dns", "prod", "scrapeport-9119", "servicetype-ldap", "scrapeport-9142", "servicetype-ntp"], "aux-20011-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-consul-service-metrics", "owner-opsinfra", "aux-20011-prod-nldc2.nldc2", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus", "servicetype-dns", "prod", "scrapeport-9119", "servicetype-ldap", "scrapeport-9142", "servicetype-ntp"], "aux-30010-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service-metrics", "owner-opsinfra", "aux-30010-prod-nldc1.nldc1", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus", "servicetype-dns", "prod", "scrapeport-9119", "servicetype-ldap", "scrapeport-9142", "servicetype-ntp"], "aux-30011-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service-metrics", "owner-opsinfra", "aux-30011-prod-nldc1.nldc1", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus", "servicetype-dns", "prod", "scrapeport-9119", "servicetype-ldap", "scrapeport-9142", "servicetype-ntp"], "aux-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service-metrics", "owner-opsinfra", "aux-50001-prod-wndc1.wndc1", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus", "servicetype-dns", "prod", "scrapeport-9119", "servicetype-ldap", "scrapeport-9142", "servicetype-ntp"], "aux-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service-metrics", "owner-opsinfra", "aux-50002-prod-wndc1.wndc1", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus", "servicetype-dns", "prod", "scrapeport-9119", "servicetype-ldap", "scrapeport-9142", "servicetype-ntp"], "aux-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service-metrics", "owner-opsinfra", "aux-80001-prod-sngdc1.sngdc1", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus", "servicetype-dns", "prod", "scrapeport-9119", "servicetype-ldap", "scrapeport-9142", "servicetype-ntp"], "aux-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service-metrics", "owner-opsinfra", "aux-80002-prod-sngdc1.sngdc1", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus", "servicetype-dns", "prod", "scrapeport-9119", "servicetype-ldap", "scrapeport-9142", "servicetype-ntp"], "billingdb-30014-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "billingdb-30014-prod-nldc1.nldc1", "prod", "managedfor-opsdatastores", "scrapeport-9306"], "billingdb-30015-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "billingdb-30015-prod-nldc1.nldc1", "prod", "managedfor-opsdatastores", "scrapeport-9306"], "billingdb-30016-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "billingdb-30016-prod-nldc1.nldc1", "prod", "managedfor-opsdatastores", "scrapeport-9306"], "billingdb-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "billingdb-50002-prod-wndc1.wndc1", "prod", "managedfor-opsdatastores", "scrapeport-9306"], "billingdb-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "billingdb-50003-prod-wndc1.wndc1", "prod", "managedfor-opsdatastores", "scrapeport-9306"], "billingdb-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "billingdb-50004-prod-wndc1.wndc1", "prod", "managedfor-opsdatastores", "scrapeport-9306"], "billingdb-80014-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "billingdb-80014-prod-sngdc1.sngdc1", "prod", "managedfor-opsdatastores", "scrapeport-9306"], "billingdb-80015-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "billingdb-80015-prod-sngdc1.sngdc1", "prod", "managedfor-opsdatastores", "scrapeport-9306"], "billingdb-80016-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "billingdb-80016-prod-sngdc1.sngdc1", "prod", "managedfor-opsdatastores", "scrapeport-9306"], "cass14-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50001-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50002-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50003-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50004-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50005-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50005-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50006-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50006-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50007-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50007-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50008-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50008-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50009-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50009-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50010-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50010-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50011-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50011-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50012-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50012-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50013-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50013-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50014-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50014-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50015-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50015-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50016-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50016-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50017-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50017-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50018-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50018-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50019-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50019-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50020-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50020-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50021-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50021-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50022-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50022-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50023-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50023-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass14-50024-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass14-50024-prod-wndc1.wndc1", "who", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cass18-30021-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass18-30021-prod-nldc1.nldc1", "DocArticles", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass18-30022-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass18-30022-prod-nldc1.nldc1", "DocArticles", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass18-30023-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass18-30023-prod-nldc1.nldc1", "DocArticles", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass18-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass18-50001-prod-wndc1.wndc1", "DocArticles", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass18-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass18-50002-prod-wndc1.wndc1", "DocArticles", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass18-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass18-50003-prod-wndc1.wndc1", "DocArticles", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass18-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass18-50004-prod-wndc1.wndc1", "DocArticles", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass18-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass18-80001-prod-sngdc1.sngdc1", "DocArticles", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass18-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass18-80002-prod-sngdc1.sngdc1", "DocArticles", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass18-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass18-80003-prod-sngdc1.sngdc1", "DocArticles", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass29-30021-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass29-30021-prod-nldc1.nldc1", "Images", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass29-30022-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass29-30022-prod-nldc1.nldc1", "Images", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass29-30023-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass29-30023-prod-nldc1.nldc1", "Images", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass29-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass29-50001-prod-wndc1.wndc1", "Images", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass29-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass29-50002-prod-wndc1.wndc1", "Images", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass29-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass29-50003-prod-wndc1.wndc1", "Images", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass29-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass29-80001-prod-sngdc1.sngdc1", "Images", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass29-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass29-80002-prod-sngdc1.sngdc1", "Images", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass29-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass29-80003-prod-sngdc1.sngdc1", "Images", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cass37-30021-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass37-30021-prod-nldc1.nldc1", "offlineOrganic", "cassandra", "managedfor-organicrecs", "scrapeport-5560"], "cass37-30022-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass37-30022-prod-nldc1.nldc1", "offlineOrganic", "cassandra", "managedfor-organicrecs", "scrapeport-5560"], "cass37-30023-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass37-30023-prod-nldc1.nldc1", "offlineOrganic", "cassandra", "managedfor-organicrecs", "scrapeport-5560"], "cass37-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass37-50001-prod-wndc1.wndc1", "offlineOrganic", "cassandra", "managedfor-organicrecs", "scrapeport-5560"], "cass37-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass37-50002-prod-wndc1.wndc1", "offlineOrganic", "cassandra", "managedfor-organicrecs", "scrapeport-5560"], "cass37-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-ops-data-stores", "owner-opsdatastores", "cass37-50003-prod-wndc1.wndc1"], "cass37-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass37-80001-prod-sngdc1.sngdc1", "offlineOrganic", "cassandra", "managedfor-organicrecs", "scrapeport-5560"], "cass37-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass37-80002-prod-sngdc1.sngdc1", "offlineOrganic", "cassandra", "managedfor-organicrecs", "scrapeport-5560"], "cass37-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cass37-80003-prod-sngdc1.sngdc1", "offlineOrganic", "cassandra", "managedfor-organicrecs", "scrapeport-5560"], "cassdoc-30021-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-30021-prod-nldc1.nldc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-30022-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-30022-prod-nldc1.nldc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-30023-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-30023-prod-nldc1.nldc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-30024-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-30024-prod-nldc1.nldc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-30025-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-30025-prod-nldc1.nldc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-30026-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-30026-prod-nldc1.nldc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-50001-prod-wndc1.wndc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-50002-prod-wndc1.wndc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-50003-prod-wndc1.wndc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-50004-prod-wndc1.wndc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-50005-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-50005-prod-wndc1.wndc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-50006-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-50006-prod-wndc1.wndc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-80001-prod-sngdc1.sngdc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-80002-prod-sngdc1.sngdc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-80003-prod-sngdc1.sngdc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-80004-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-80004-prod-sngdc1.sngdc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-80005-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-80005-prod-sngdc1.sngdc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdoc-80006-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdoc-80006-prod-sngdc1.sngdc1", "cassandra-docfeatures", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-30021-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-30021-prod-nldc1.nldc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-30022-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-30022-prod-nldc1.nldc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-30023-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-30023-prod-nldc1.nldc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-30024-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-30024-prod-nldc1.nldc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-30025-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-30025-prod-nldc1.nldc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-30026-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-30026-prod-nldc1.nldc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-50001-prod-wndc1.wndc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-50002-prod-wndc1.wndc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-50003-prod-wndc1.wndc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-50004-prod-wndc1.wndc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-50005-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-50005-prod-wndc1.wndc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-50006-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-50006-prod-wndc1.wndc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-80001-prod-sngdc1.sngdc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-80002-prod-sngdc1.sngdc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-80003-prod-sngdc1.sngdc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-80004-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-80004-prod-sngdc1.sngdc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-80005-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-80005-prod-sngdc1.sngdc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassdocmap-80006-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassdocmap-80006-prod-sngdc1.sngdc1", "cassandra-docma<PERSON>s", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassimagestemplate-30021-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassimagestemplate-30021-prod-nldc1.nldc1", "cassandra3-imagesTemplate", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassimagestemplate-30022-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassimagestemplate-30022-prod-nldc1.nldc1", "cassandra3-imagesTemplate", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassimagestemplate-30023-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassimagestemplate-30023-prod-nldc1.nldc1", "cassandra3-imagesTemplate", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassimagestemplate-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassimagestemplate-50002-prod-wndc1.wndc1", "cassandra3-imagesTemplate", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassimagestemplate-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassimagestemplate-50003-prod-wndc1.wndc1", "cassandra3-imagesTemplate", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassimagestemplate-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassimagestemplate-50004-prod-wndc1.wndc1", "cassandra3-imagesTemplate", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassimagestemplate-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassimagestemplate-80001-prod-sngdc1.sngdc1", "cassandra3-imagesTemplate", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassimagestemplate-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassimagestemplate-80002-prod-sngdc1.sngdc1", "cassandra3-imagesTemplate", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassimagestemplate-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassimagestemplate-80003-prod-sngdc1.sngdc1", "cassandra3-imagesTemplate", "cassandra", "managedfor-appservices", "scrapeport-5560"], "cassopausermappings-30031-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-30031-prod-nldc1.nldc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-30032-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-ops-data-stores", "owner-opsdatastores", "cassopausermappings-30032-prod-nldc1.nldc1"], "cassopausermappings-30033-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-30033-prod-nldc1.nldc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-30034-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-30034-prod-nldc1.nldc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-30035-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-30035-prod-nldc1.nldc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-30036-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-30036-prod-nldc1.nldc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-30037-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-30037-prod-nldc1.nldc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-30038-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-30038-prod-nldc1.nldc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-30039-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-30039-prod-nldc1.nldc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-30040-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-30040-prod-nldc1.nldc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-30041-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-30041-prod-nldc1.nldc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-30042-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-30042-prod-nldc1.nldc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-30043-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-30043-prod-nldc1.nldc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-30044-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-30044-prod-nldc1.nldc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-30045-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-30045-prod-nldc1.nldc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50001-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50002-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50003-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50004-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50005-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50005-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50006-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50006-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50007-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50007-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50008-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50008-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50009-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50009-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50010-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50010-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50011-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50011-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50012-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50012-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50013-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50013-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50014-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50014-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-50015-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-50015-prod-wndc1.wndc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80001-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80002-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80003-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80004-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80004-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80005-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80005-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80006-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80006-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80007-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80007-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80008-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80008-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80009-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80009-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80010-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80010-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80011-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80011-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80012-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80012-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80013-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80013-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80014-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80014-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "cassopausermappings-80015-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassopausermappings-80015-prod-sngdc1.sngdc1", "cassandra3-opausermappings", "cassandra", "managedfor-edgeservices", "scrapeport-5560"], "casssegments-30031-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-30031-prod-nldc1.nldc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-30032-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-30032-prod-nldc1.nldc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-30033-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-30033-prod-nldc1.nldc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-30034-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-30034-prod-nldc1.nldc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-30035-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-30035-prod-nldc1.nldc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-30036-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-30036-prod-nldc1.nldc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-30037-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-30037-prod-nldc1.nldc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-30038-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-30038-prod-nldc1.nldc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-30039-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-30039-prod-nldc1.nldc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-30040-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-30040-prod-nldc1.nldc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-50001-prod-wndc1.wndc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-50002-prod-wndc1.wndc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-50003-prod-wndc1.wndc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-50004-prod-wndc1.wndc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-50005-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-50005-prod-wndc1.wndc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-50006-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-50006-prod-wndc1.wndc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-50007-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-50007-prod-wndc1.wndc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-50008-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-50008-prod-wndc1.wndc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-50009-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-50009-prod-wndc1.wndc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-50010-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-50010-prod-wndc1.wndc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-80001-prod-sngdc1.sngdc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-80002-prod-sngdc1.sngdc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-80003-prod-sngdc1.sngdc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-80004-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-80004-prod-sngdc1.sngdc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-80005-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-80005-prod-sngdc1.sngdc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-80006-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-80006-prod-sngdc1.sngdc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-80007-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-80007-prod-sngdc1.sngdc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-80008-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-80008-prod-sngdc1.sngdc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-80009-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-80009-prod-sngdc1.sngdc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casssegments-80010-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casssegments-80010-prod-sngdc1.sngdc1", "cassandra-segments", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagnldc-20012-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagnldc-20012-prod-nldc2.nldc2", "cassandra-upag-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagnldc-20013-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagnldc-20013-prod-nldc2.nldc2", "cassandra-upag-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagnldc-20014-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagnldc-20014-prod-nldc2.nldc2", "cassandra-upag-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagnldc-20015-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagnldc-20015-prod-nldc2.nldc2", "cassandra-upag-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagnldc-20016-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagnldc-20016-prod-nldc2.nldc2", "cassandra-upag-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagnldc-20021-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagnldc-20021-prod-nldc2.nldc2", "cassandra-upag-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagnldc-30021-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagnldc-30021-prod-nldc1.nldc1", "cassandra-upag-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagnldc-30022-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagnldc-30022-prod-nldc1.nldc1", "cassandra-upag-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagnldc-30023-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagnldc-30023-prod-nldc1.nldc1", "cassandra-upag-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagnldc-30024-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagnldc-30024-prod-nldc1.nldc1", "cassandra-upag-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagnldc-30025-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagnldc-30025-prod-nldc1.nldc1", "cassandra-upag-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagsng-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagsng-80001-prod-sngdc1.sngdc1", "cassandra-upag-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagsng-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagsng-80002-prod-sngdc1.sngdc1", "cassandra-upag-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagsng-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagsng-80003-prod-sngdc1.sngdc1", "cassandra-upag-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagsng-80004-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagsng-80004-prod-sngdc1.sngdc1", "cassandra-upag-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagsng-80005-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagsng-80005-prod-sngdc1.sngdc1", "cassandra-upag-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "cassupagsng-80006-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "cassupagsng-80006-prod-sngdc1.sngdc1", "cassandra-upag-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoami-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoami-50001-prod-wndc1.wndc1", "cassandra3-whoami", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoami-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoami-50002-prod-wndc1.wndc1", "cassandra3-whoami", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoami-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoami-50003-prod-wndc1.wndc1", "cassandra3-whoami", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoami-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoami-50004-prod-wndc1.wndc1", "cassandra3-whoami", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoami-50005-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoami-50005-prod-wndc1.wndc1", "cassandra3-whoami", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoami-50006-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoami-50006-prod-wndc1.wndc1", "cassandra3-whoami", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoaminldc-20011-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoaminldc-20011-prod-nldc2.nldc2", "cassandra-whoami-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoaminldc-20012-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoaminldc-20012-prod-nldc2.nldc2", "cassandra-whoami-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoaminldc-20013-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoaminldc-20013-prod-nldc2.nldc2", "cassandra-whoami-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoaminldc-20014-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-ops-data-stores", "owner-opsdatastores", "casswhoaminldc-20014-prod-nldc2.nldc2"], "casswhoaminldc-20015-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoaminldc-20015-prod-nldc2.nldc2", "cassandra-whoami-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoaminldc-30021-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoaminldc-30021-prod-nldc1.nldc1", "cassandra-whoami-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoaminldc-30022-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoaminldc-30022-prod-nldc1.nldc1", "cassandra-whoami-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoaminldc-30023-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoaminldc-30023-prod-nldc1.nldc1", "cassandra-whoami-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoaminldc-30024-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoaminldc-30024-prod-nldc1.nldc1", "cassandra-whoami-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoaminldc-30025-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoaminldc-30025-prod-nldc1.nldc1", "cassandra-whoami-eu", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoamisng-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoamisng-80001-prod-sngdc1.sngdc1", "cassandra-whoami-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoamisng-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoamisng-80002-prod-sngdc1.sngdc1", "cassandra-whoami-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoamisng-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoamisng-80003-prod-sngdc1.sngdc1", "cassandra-whoami-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoamisng-80004-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoamisng-80004-prod-sngdc1.sngdc1", "cassandra-whoami-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoamisng-80005-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoamisng-80005-prod-sngdc1.sngdc1", "cassandra-whoami-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhoamisng-80006-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhoamisng-80006-prod-sngdc1.sngdc1", "cassandra-whoami-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhonldc-20011-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhonldc-20011-prod-nldc2.nldc2", "cassandra-who-dub", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhonldc-20012-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhonldc-20012-prod-nldc2.nldc2", "cassandra-who-dub", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhonldc-20013-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhonldc-20013-prod-nldc2.nldc2", "cassandra-who-dub", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhonldc-20014-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhonldc-20014-prod-nldc2.nldc2", "cassandra-who-dub", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhonldc-30021-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhonldc-30021-prod-nldc1.nldc1", "cassandra-who-dub", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhonldc-30022-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhonldc-30022-prod-nldc1.nldc1", "cassandra-who-dub", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhonldc-30023-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhonldc-30023-prod-nldc1.nldc1", "cassandra-who-dub", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhonldc-30024-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhonldc-30024-prod-nldc1.nldc1", "cassandra-who-dub", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhonldc-30025-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhonldc-30025-prod-nldc1.nldc1", "cassandra-who-dub", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhonldc-30026-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhonldc-30026-prod-nldc1.nldc1", "cassandra-who-dub", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhosng-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhosng-80001-prod-sngdc1.sngdc1", "cassandra-who-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhosng-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhosng-80002-prod-sngdc1.sngdc1", "cassandra-who-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhosng-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhosng-80003-prod-sngdc1.sngdc1", "cassandra-who-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhosng-80004-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhosng-80004-prod-sngdc1.sngdc1", "cassandra-who-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhosng-80005-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhosng-80005-prod-sngdc1.sngdc1", "cassandra-who-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "casswhosng-80006-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-cassandra", "owner-opsdatastores", "casswhosng-80006-prod-sngdc1.sngdc1", "cassandra-who-sng", "cassandra", "managedfor-recsinfra", "scrapeport-5560"], "consul-20004-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-consul-service", "owner-opsinfra", "consul-20004-prod-nldc2.nldc2", "scrapeport-9107", "servicetype-consul-service-metrics", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus"], "consul-20005-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-consul-service", "owner-opsinfra", "consul-20005-prod-nldc2.nldc2", "scrapeport-9107", "servicetype-consul-service-metrics", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus"], "consul-20006-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-consul-service", "owner-opsinfra", "consul-20006-prod-nldc2.nldc2", "scrapeport-9107", "servicetype-consul-service-metrics", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus"], "consul-30004-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service", "owner-opsinfra", "consul-30004-prod-nldc1.nldc1", "scrapeport-9107", "servicetype-consul-service-metrics", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus"], "consul-30005-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service", "owner-opsinfra", "consul-30005-prod-nldc1.nldc1", "scrapeport-9107", "servicetype-consul-service-metrics", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus"], "consul-30006-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service", "owner-opsinfra", "consul-30006-prod-nldc1.nldc1", "scrapeport-9107", "servicetype-consul-service-metrics", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus"], "consul-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service", "owner-opsinfra", "consul-50001-prod-wndc1.wndc1", "scrapeport-9107", "servicetype-consul-service-metrics", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus"], "consul-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service", "owner-opsinfra", "consul-50002-prod-wndc1.wndc1", "scrapeport-9107", "servicetype-consul-service-metrics", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus"], "consul-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service", "owner-opsinfra", "consul-50003-prod-wndc1.wndc1", "scrapeport-9107", "servicetype-consul-service-metrics", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus"], "consul-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service", "owner-opsinfra", "consul-50004-prod-wndc1.wndc1", "scrapeport-9107", "servicetype-consul-service-metrics", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus"], "consul-50005-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service", "owner-opsinfra", "consul-50005-prod-wndc1.wndc1", "scrapeport-9107", "servicetype-consul-service-metrics", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus"], "consul-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service", "owner-opsinfra", "consul-80001-prod-sngdc1.sngdc1", "scrapeport-9107", "servicetype-consul-service-metrics", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus"], "consul-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service", "owner-opsinfra", "consul-80002-prod-sngdc1.sngdc1", "scrapeport-9107", "servicetype-consul-service-metrics", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus"], "consul-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-consul-service", "owner-opsinfra", "consul-80003-prod-sngdc1.sngdc1", "scrapeport-9107", "servicetype-consul-service-metrics", "scrapeport-8500", "scrapepath-/v1/agent/metrics?format=prometheus"], "gw-30021-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30021-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-30022-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30022-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-30023-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30023-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-30024-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30024-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-30025-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30025-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-30026-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30026-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-30027-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30027-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-30028-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30028-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-30029-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30029-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-30030-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30030-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-30031-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30031-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-30032-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30032-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-30101-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30101-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-30102-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30102-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-30103-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-30103-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "gw-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50001-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50002-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50003-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50004-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50005-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50005-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50006-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50006-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50007-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50007-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50008-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50008-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50009-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50009-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50010-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50010-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50011-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50011-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50012-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50012-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50013-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50013-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50014-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50014-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50015-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50015-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50016-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50016-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50017-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50017-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50018-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50018-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50019-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50019-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50020-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50020-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50021-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50021-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50022-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50022-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50023-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50023-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50024-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50024-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50025-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50025-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50026-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50026-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50027-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50027-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50028-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50028-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50029-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50029-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50030-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50030-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50101-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50101-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50102-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50102-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-50103-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-50103-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "gw-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-80001-prod-sngdc1.sngdc1", "prod", "scrapeport-9101"], "gw-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-80002-prod-sngdc1.sngdc1", "prod", "scrapeport-9101"], "gw-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-80003-prod-sngdc1.sngdc1", "prod", "scrapeport-9101"], "gw-80004-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-80004-prod-sngdc1.sngdc1", "prod", "scrapeport-9101"], "gw-80005-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-80005-prod-sngdc1.sngdc1", "prod", "scrapeport-9101"], "gw-80101-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-80101-prod-sngdc1.sngdc1", "prod", "scrapeport-9101"], "gw-80102-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "gw-80102-prod-sngdc1.sngdc1", "prod", "scrapeport-9101"], "kafka10-30001-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka10-30001-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka10-30002-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka10-30002-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka10-30003-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka10-30003-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka10-30004-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka10-30004-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafka10-30005-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka10-30005-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafka10-30006-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka10-30006-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafka10-30007-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka10-30007-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafka10-30008-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka10-30008-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafka20-30001-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka20-30001-prod-nldc1.nldc1", "managedfor-appservices", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka20-30002-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka20-30002-prod-nldc1.nldc1", "managedfor-appservices", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka20-30003-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka20-30003-prod-nldc1.nldc1", "managedfor-appservices", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka20-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka20-50001-prod-wndc1.wndc1", "managedfor-appservices", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka20-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka20-50002-prod-wndc1.wndc1", "managedfor-appservices", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka20-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka20-50003-prod-wndc1.wndc1", "managedfor-appservices", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka20-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka20-80001-prod-sngdc1.sngdc1", "managedfor-appservices", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka20-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka20-80002-prod-sngdc1.sngdc1", "managedfor-appservices", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka20-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka20-80003-prod-sngdc1.sngdc1", "managedfor-appservices", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka26-30001-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-30001-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka26-30002-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-30002-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka26-30003-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-30003-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka26-30101-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-30101-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafka26-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-50001-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka26-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-50002-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka26-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-50003-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka26-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-50004-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafka26-50005-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-50005-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafka26-50006-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-50006-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafka26-50007-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-50007-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafka26-50008-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-50008-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafka26-50009-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-50009-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafka26-50010-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-50010-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafka26-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-80001-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka26-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-80002-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka26-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-80003-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka26-80004-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka26-80004-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafka4-20001-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka4-20001-prod-nldc2.nldc2", "managedfor-opsvisibility", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka4-20002-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka4-20002-prod-nldc2.nldc2", "managedfor-opsvisibility", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka4-20003-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka4-20003-prod-nldc2.nldc2", "managedfor-opsvisibility", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka4-30001-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka4-30001-prod-nldc1.nldc1", "managedfor-opsvisibility", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka4-30002-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka4-30002-prod-nldc1.nldc1", "managedfor-opsvisibility", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka4-30003-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka4-30003-prod-nldc1.nldc1", "managedfor-opsvisibility", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka4-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka4-50001-prod-wndc1.wndc1", "managedfor-opsvisibility", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka4-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka4-50002-prod-wndc1.wndc1", "managedfor-opsvisibility", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka4-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka4-50003-prod-wndc1.wndc1", "managedfor-opsvisibility", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka4-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka4-80001-prod-sngdc1.sngdc1", "managedfor-opsvisibility", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka4-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka4-80002-prod-sngdc1.sngdc1", "managedfor-opsvisibility", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafka4-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafka4-80003-prod-sngdc1.sngdc1", "managedfor-opsvisibility", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkageneral-30001-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkageneral-30001-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkageneral-30002-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkageneral-30002-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkageneral-30003-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkageneral-30003-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkageneral-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkageneral-50001-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkageneral-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkageneral-50002-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkageneral-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkageneral-50003-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkageneral-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkageneral-80001-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkageneral-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkageneral-80002-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkageneral-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkageneral-80003-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkardl-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkardl-50001-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkardl-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkardl-50002-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkardl-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkardl-50003-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkardl-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkardl-50004-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafkardl-50005-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkardl-50005-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafkardl-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkardl-80001-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkardl-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkardl-80002-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkardl-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkardl-80003-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5533", "servicetype-zookeeper", "scrapeport-5582"], "kafkardl-80004-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkardl-80004-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5533"], "kafkardl-80005-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-kafka", "owner-datainfrastructure", "kafkardl-80005-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5533"], "lb-20011-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "lb-20011-prod-nldc2.nldc2", "prod", "scrapeport-9101"], "lb-20012-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "lb-20012-prod-nldc2.nldc2", "prod", "scrapeport-9101"], "lb-30011-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "lb-30011-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "lb-30012-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "lb-30012-prod-nldc1.nldc1", "prod", "scrapeport-9101"], "lb-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "lb-50001-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "lb-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "lb-50002-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "lb-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "lb-50003-prod-wndc1.wndc1", "prod", "scrapeport-9101"], "lb-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "lb-80001-prod-sngdc1.sngdc1", "prod", "scrapeport-9101"], "lb-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-haproxy", "owner-opsnetwork", "lb-80002-prod-sngdc1.sngdc1", "prod", "scrapeport-9101"], "mfschunk-50001-prod-wndc1.wndc1.outbrain.com": [], "mfschunk-50002-prod-wndc1.wndc1.outbrain.com": [], "mfschunk-50003-prod-wndc1.wndc1.outbrain.com": [], "mfsmaster-50001-prod-wndc1.wndc1.outbrain.com": [], "mysql-30006-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "mysql-30006-prod-nldc1.nldc1", "prod", "mysql-prod-online", "managedfor-opsdatastores", "scrapeport-9306"], "mysql-30007-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "mysql-30007-prod-nldc1.nldc1", "prod", "mysql-prod-online", "managedfor-opsdatastores", "scrapeport-9306"], "mysql-30008-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "mysql-30008-prod-nldc1.nldc1", "prod", "mysql-prod-online", "managedfor-opsdatastores", "scrapeport-9306"], "mysql-50006-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "mysql-50006-prod-wndc1.wndc1", "prod", "mysql-prod-online", "managedfor-opsdatastores", "scrapeport-9306"], "mysql-50007-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "mysql-50007-prod-wndc1.wndc1", "prod", "mysql-prod-online", "managedfor-opsdatastores", "scrapeport-9306"], "mysql-50008-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "mysql-50008-prod-wndc1.wndc1", "prod", "mysql-prod-online", "managedfor-opsdatastores", "scrapeport-9306"], "mysql-80006-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "mysql-80006-prod-sngdc1.sngdc1", "prod", "mysql-prod-online", "managedfor-opsdatastores", "scrapeport-9306"], "mysql-80007-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "mysql-80007-prod-sngdc1.sngdc1", "prod", "mysql-prod-online", "managedfor-opsdatastores", "scrapeport-9306"], "mysql-80008-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "mysql-80008-prod-sngdc1.sngdc1", "prod", "mysql-prod-online", "managedfor-opsdatastores", "scrapeport-9306"], "netmon-30001-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-blackbox-exporter", "owner-opsnetwork", "netmon-30001-prod-nldc1.nldc1", "servicetype-postfix", "owner-opsinfra", "servicetype-generic", "servicetype-snmp-exporter"], "netmon-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-blackbox-exporter", "owner-opsnetwork", "netmon-50001-prod-wndc1.wndc1", "servicetype-postfix", "owner-opsinfra", "servicetype-generic", "servicetype-snmp-exporter"], "netmon-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-blackbox-exporter", "owner-opsnetwork", "netmon-80001-prod-sngdc1.sngdc1", "servicetype-postfix", "owner-opsinfra", "servicetype-generic", "servicetype-snmp-exporter"], "nginxmlr-30001-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-nginx-webhdfs", "owner-epsilon", "nginxmlr-30001-prod-nldc1.nldc1", "prod", "scrapeport-9113", "managedfor-researchteam", "scrapeport-9102"], "nginxmlr-30002-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-nginx-webhdfs", "owner-epsilon", "nginxmlr-30002-prod-nldc1.nldc1", "prod", "scrapeport-9113", "managedfor-researchteam", "scrapeport-9102"], "nginxmlr-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-nginx-webhdfs", "owner-epsilon", "nginxmlr-50001-prod-wndc1.wndc1", "prod", "scrapeport-9113", "managedfor-researchteam", "scrapeport-9102"], "nginxmlr-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-nginx-webhdfs", "owner-epsilon", "nginxmlr-50002-prod-wndc1.wndc1", "prod", "scrapeport-9113", "managedfor-researchteam", "scrapeport-9102"], "nginxmlr-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-nginx-webhdfs", "owner-epsilon", "nginxmlr-80001-prod-sngdc1.sngdc1", "prod", "scrapeport-9113", "managedfor-researchteam", "scrapeport-9102"], "nginxmlr-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-nginx-webhdfs", "owner-epsilon", "nginxmlr-80002-prod-sngdc1.sngdc1", "prod", "scrapeport-9113", "managedfor-researchteam", "scrapeport-9102"], "proxysql-30011-prod-nldc1.nldc1.outbrain.com": [], "proxysql-30012-prod-nldc1.nldc1.outbrain.com": ["hostType-metal", "servicetype-generic", "owner-opsdatastores", "proxysql-30012-prod-nldc1.nldc1", "servicetype-proxysql", "managedfor-opsdatastores", "scrapeport-9307"], "proxysql-30015-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-generic", "owner-opsdatastores", "proxysql-30015-prod-nldc1.nldc1", "servicetype-proxysql", "managedfor-opsdatastores", "scrapeport-9307"], "proxysql-30016-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-generic", "owner-opsdatastores", "proxysql-30016-prod-nldc1.nldc1", "servicetype-proxysql", "managedfor-opsdatastores", "scrapeport-9307"], "proxysql-30017-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-generic", "owner-opsdatastores", "proxysql-30017-prod-nldc1.nldc1", "servicetype-proxysql", "managedfor-opsdatastores", "scrapeport-9307"], "proxysql-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-generic", "owner-opsdatastores", "proxysql-50002-prod-wndc1.wndc1", "servicetype-proxysql", "managedfor-opsdatastores", "scrapeport-9307"], "proxysql-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-generic", "owner-opsdatastores", "proxysql-50003-prod-wndc1.wndc1", "servicetype-proxysql", "managedfor-opsdatastores", "scrapeport-9307"], "proxysql-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-generic", "owner-opsdatastores", "proxysql-50004-prod-wndc1.wndc1", "servicetype-proxysql", "managedfor-opsdatastores", "scrapeport-9307"], "proxysql-80011-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-generic", "owner-opsdatastores", "proxysql-80011-prod-sngdc1.sngdc1", "servicetype-proxysql", "managedfor-opsdatastores", "scrapeport-9307"], "proxysql-80012-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-generic", "owner-opsdatastores", "proxysql-80012-prod-sngdc1.sngdc1", "servicetype-proxysql", "managedfor-opsdatastores", "scrapeport-9307"], "proxysql-80013-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-generic", "owner-opsdatastores", "proxysql-80013-prod-sngdc1.sngdc1", "servicetype-proxysql", "managedfor-opsdatastores", "scrapeport-9307"], "recsdb-30015-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "recsdb-30015-prod-nldc1.nldc1", "prod", "managedfor-recsinfra", "scrapeport-9306"], "recsdb-30016-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "recsdb-30016-prod-nldc1.nldc1", "prod", "managedfor-recsinfra", "scrapeport-9306"], "recsdb-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "recsdb-50002-prod-wndc1.wndc1", "prod", "managedfor-recsinfra", "scrapeport-9306"], "recsdb-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "recsdb-50003-prod-wndc1.wndc1", "prod", "managedfor-recsinfra", "scrapeport-9306"], "recsdb-80015-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "recsdb-80015-prod-sngdc1.sngdc1", "prod", "managedfor-recsinfra", "scrapeport-9306"], "recsdb-80016-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-mysql", "owner-opsdatastores", "recsdb-80016-prod-sngdc1.sngdc1", "prod", "managedfor-recsinfra", "scrapeport-9306"], "repo-20001-prod-nldc2.nldc2.outbrain.com": ["hostType-cloud", "servicetype-repo", "owner-opsinfra", "repo-20001-prod-nldc2.nldc2"], "repo-30002-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-repo", "owner-opsinfra", "repo-30002-prod-nldc1.nldc1"], "repo-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-repo", "owner-opsinfra", "repo-50002-prod-wndc1.wndc1"], "repo-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-repo", "owner-opsinfra", "repo-80001-prod-sngdc1.sngdc1"], "zemas-30001-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-30001-prod-nldc1.nldc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-30002-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-30002-prod-nldc1.nldc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-30007-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-30007-prod-nldc1.nldc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-30008-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-30008-prod-nldc1.nldc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-30009-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-30009-prod-nldc1.nldc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-30010-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-30010-prod-nldc1.nldc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-50001-prod-wndc1.wndc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-50002-prod-wndc1.wndc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-50003-prod-wndc1.wndc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-50004-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-50004-prod-wndc1.wndc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-50005-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-50005-prod-wndc1.wndc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-50006-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-50006-prod-wndc1.wndc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-50007-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-50007-prod-wndc1.wndc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-50008-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-50008-prod-wndc1.wndc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-50009-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-50009-prod-wndc1.wndc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-50010-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-50010-prod-wndc1.wndc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-80001-prod-sngdc1.sngdc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-80002-prod-sngdc1.sngdc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-80003-prod-sngdc1.sngdc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-80004-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-80004-prod-sngdc1.sngdc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-80005-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-80005-prod-sngdc1.sngdc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemas-80006-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-zemas", "owner-zinfra", "zemas-80006-prod-sngdc1.sngdc1", "scrapepath-/metrics", "managedfor-zembidder", "scrapeport-9145"], "zemb1lb-30001-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-zemb1lb", "owner-zinfra", "zemb1lb-30001-prod-nldc1.nldc1", "scrapeport-9102", "scrapepath-/metrics", "managedfor-zinfra"], "zemb1lb-30002-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-zemb1lb", "owner-zinfra", "zemb1lb-30002-prod-nldc1.nldc1", "scrapeport-9102", "scrapepath-/metrics", "managedfor-zinfra"], "zemb1lb-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zemb1lb", "owner-zinfra", "zemb1lb-50001-prod-wndc1.wndc1", "scrapeport-9102", "scrapepath-/metrics", "managedfor-zinfra"], "zemb1lb-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zemb1lb", "owner-zinfra", "zemb1lb-50002-prod-wndc1.wndc1", "scrapeport-9102", "scrapepath-/metrics", "managedfor-zinfra"], "zemb1lb-80006-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-zemb1lb", "owner-zinfra", "zemb1lb-80006-prod-sngdc1.sngdc1", "scrapeport-9102", "scrapepath-/metrics", "managedfor-zinfra"], "zemb1lb-80007-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-zemb1lb", "owner-zinfra", "zemb1lb-80007-prod-sngdc1.sngdc1", "scrapeport-9102", "scrapepath-/metrics", "managedfor-zinfra"], "zk2-30001-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk2-30001-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5582"], "zk2-30002-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk2-30002-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5582"], "zk2-30003-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk2-30003-prod-nldc1.nldc1", "managedfor-datainfrastructure", "scrapeport-5582"], "zk2-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk2-50001-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5582"], "zk2-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk2-50002-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5582"], "zk2-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk2-50003-prod-wndc1.wndc1", "managedfor-datainfrastructure", "scrapeport-5582"], "zk2-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-metal", "servicetype-zookeeper", "owner-datainfrastructure", "zk2-80001-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5582"], "zk2-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk2-80002-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5582"], "zk2-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk2-80003-prod-sngdc1.sngdc1", "managedfor-datainfrastructure", "scrapeport-5582"], "zk9-30001-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk9-30001-prod-nldc1.nldc1", "managedfor-appservices", "scrapeport-5582"], "zk9-30002-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk9-30002-prod-nldc1.nldc1", "managedfor-appservices", "scrapeport-5582"], "zk9-30003-prod-nldc1.nldc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk9-30003-prod-nldc1.nldc1", "managedfor-appservices", "scrapeport-5582"], "zk9-50001-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk9-50001-prod-wndc1.wndc1", "managedfor-appservices", "scrapeport-5582"], "zk9-50002-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk9-50002-prod-wndc1.wndc1", "managedfor-appservices", "scrapeport-5582"], "zk9-50003-prod-wndc1.wndc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk9-50003-prod-wndc1.wndc1", "managedfor-appservices", "scrapeport-5582"], "zk9-80001-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk9-80001-prod-sngdc1.sngdc1", "managedfor-appservices", "scrapeport-5582"], "zk9-80002-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk9-80002-prod-sngdc1.sngdc1", "managedfor-appservices", "scrapeport-5582"], "zk9-80003-prod-sngdc1.sngdc1.outbrain.com": ["hostType-cloud", "servicetype-zookeeper", "owner-datainfrastructure", "zk9-80003-prod-sngdc1.sngdc1", "managedfor-appservices", "scrapeport-5582"]}