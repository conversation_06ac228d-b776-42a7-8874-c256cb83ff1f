# DEPRECATED: check kuberne<PERSON>_check_pre
#
# This will end the run on a node if memcached is running on it
---
- name: Check if memcached is running
  ansible.builtin.shell: "docker ps | grep k8s_memcached | wc -l | awk '{print $1}'"
  register: _number_of_memcached

- debug:
    msg: "Skipping server as memcached is running on the server"
  when: _number_of_memcached.stdout | int  > 0

- name: End the job if memcached is running on the node
  meta: end_host
  when: _number_of_memcached.stdout | int  > 0
