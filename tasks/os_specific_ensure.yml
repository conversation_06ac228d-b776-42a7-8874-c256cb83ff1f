# This will fail the run of a job if the node it runs on doesn't have the specified OS installed
#
# Usage:
# os_name: ubuntu (OPTIONAL: defaults to 'ubuntu')
# os_version: 16.04
---
- name: Sanity check
  assert:
    that:
      - sms_vars['os_version'] is defined
    fail_msg: "Variable 'os_version' is not defined!"
    quiet: true

- name: Re-fetch the ansible_distribution* info to make sure we are using latest information
  setup: filter='ansible_distribution*'

- name: Ensure OS version
  assert:
    that:
      - sms_vars['os_name'] | default('ubuntu') | lower == ansible_distribution | lower
      - sms_vars['os_version'] | lower == ansible_distribution_version | lower
    fail_msg: "Server runs {{ ansible_distribution }} {{ ansible_distribution_version }}, which is different from the desired {{ sms_vars['os_name'] | default('ubuntu') }} {{ sms_vars['os_version'] }}"
    quiet: true