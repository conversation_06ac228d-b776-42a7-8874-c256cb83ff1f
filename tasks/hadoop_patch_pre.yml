# This task will prepare the hadoop node for patching by stopping all the services and putting the node in maintenance
# mode

# Usage:
# hadoop_namenode_consul_service: hadoop-namenode2 (OPTIONAL: defaults to hadoop-namenode)
---
# validate
- name: Validate hadoop_namenode_consul_service (optional)
  assert:
    that:
      - sms_vars['hadoop_namenode_consul_service'] | length > 0
    fail_msg: "Variable 'hadoop_namenode_consul_service' is defined but empty!"
    quiet: true
  when: sms_vars['hadoop_namenode_consul_service'] is defined

# set values
- name: Set default values
  set_fact:
    _hadoop_nn_svc: "{{ sms_vars['hadoop_namenode_consul_service'] | default('hadoop-namenode') }}"

# get active namenode
- name: Get healthy {{ _hadoop_nn_svc }} instances from Consul
  uri:
    url: "http://localhost:8500/v1/health/service/{{ _hadoop_nn_svc }}?passing=true"
    method: GET
    status_code: 200
  register: _hadoop_consul_response
  retries: 5
  delay: 10
  until: _hadoop_consul_response is not failed
  check_mode: false

- name: Validate that at least one healthy namenode was found
  assert:
    that:
      - _hadoop_consul_response.json | length > 0
    fail_msg: "No healthy {{ _hadoop_nn_svc }} instances found in Consul"
    quiet: true

- name: Extract namenode
  set_fact:
    _hadoop_namenode_hostname: "{{ _hadoop_consul_response.json | first | json_query('Node.Node') }}"

# prepare the node for patching
- name: Silence alerts
  shell: "/outbrain/prometheus/tools/silence_host_alerts 1"

- name: Set no chef run
  shell: "manage_no_chef_run.sh add -u sms -r os_patch"

- name: Set yarn into maintenance mode
  shell: "/outbrain/ops/scripts/apache-hadoop-set-nodemanager-state.py --hosts {{ inventory_hostname }} --state IN_MAINTENANCE"
  delegate_to: "{{ _hadoop_namenode_hostname }}"

- name: Refresh yarn state
  shell: "yarn rmadmin -refreshNodes"
  environment:
    PATH: "{{ ansible_env.PATH }}:/opt/hadoop/bin"
    HADOOP_USER_NAME: "hdfs"
    JAVA_HOME: "/usr/lib/jvm/default-java"
  delegate_to: "{{ _hadoop_namenode_hostname }}"
  become: true
  become_user: "yarn"

- name: Set datanode into maintenance mode
  shell: "/outbrain/ops/scripts/apache-hadoop-set-datanode-state.py --hosts {{ inventory_hostname }} --state IN_MAINTENANCE"
  delegate_to: "{{ _hadoop_namenode_hostname }}"

- name: Refresh hdfs state
  shell: "hdfs dfsadmin -refreshNodes"
  environment:
    PATH: "{{ ansible_env.PATH }}:/opt/hadoop/bin"
    HADOOP_USER_NAME: "hdfs"
    JAVA_HOME: "/usr/lib/jvm/default-java"
  delegate_to: "{{ _hadoop_namenode_hostname }}"
  become: true
  become_user: "hdfs"

- name: Stop hadoop services
  service:
    name: "{{ item }}"
    state: "stopped"
  loop:
    - yarn-nodemanager
    - hadoop-datanode

- name: Remove YARN NodeManager state files
  shell: "rm -rf /opt/hadoop/tmp/yarn-nm-recovery/yarn-nm-state/*"

- name: Remove YARN NodeManager registered executors files
  shell: "rm -rf /opt/hadoop/tmp/yarn-nm-recovery/nm-aux-services/spark_shuffle/registeredExecutors.ldb/*"
