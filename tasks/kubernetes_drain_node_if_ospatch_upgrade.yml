# Performs drain operation of a kubernetes node only after running dry run ospatch and ensuring there are packages that needs to be upgraded
#
# Usage:
# kubernetes_master_hostname: kube3master.nydc1.outbrain.com
# skip_node_before_drain: [true|false] (optional, defaults to false)
---
- name: Sanity check
  assert:
    that:
      - sms_vars['kubernetes_master_hostname'] is defined
    fail_msg: "Variable 'kubernetes_master_hostname' must be defined!"
    quiet: true

- name: Validate skip_node_before_drain
  assert:
    that:
      - (sms_vars['skip_node_before_drain']) | lower in ['true', 'false']
    fail_msg: "Value {{ sms_vars['skip_node_before_drain'] }} for skip_node_before_drain variable is invalid. It must be true or false"
    quiet: true
  when: sms_vars['skip_node_before_drain'] is defined

- name: Set default values
  set_fact:
    _skip_node_before_drain: "{{ (sms_vars['skip_node_before_drain']) | default(false) | bool }}"

- name: Execute dry run OS patch to get packages details
  shell: |
    export KERNEL_UPGRADE=true; export REBOOT=false; /outbrain/ops/scripts/os_patch.sh dry_run
  register: _os_patch_dry_run
  failed_when: _os_patch_dry_run.rc > 1

- name: Get log file
  stat:
    path: "/var/log/unattended-upgrades/unattended-upgrades.log"
  register: _os_patch_check_log

- name: Check that logfile exists
  assert:
    that:
      - _os_patch_check_log.stat.exists
    fail_msg: "The unattended-upgrades.log file doens't exists. This substep relies on running dry-run OS patch beforehand."
    quiet: true

- name: Check which packages will be upgraded
  shell: |
    grep "Packages that will be upgraded" /var/log/unattended-upgrades/unattended-upgrades.log | awk -F "Packages that will be upgraded: " '{print $2}'
  args:
    executable: /bin/bash
  register: _packages_to_upgrade

- debug:
    msg: "The node sholud be drained, but skip_node_before_drain set to true - skipping node"
  when:
    - _packages_to_upgrade.stdout | length > 0
    - _skip_node_before_drain

- name: End the current run on the node if the ospatch sholud upgrade packages
  meta: end_host
  when:
    - _packages_to_upgrade.stdout | length > 0
    - _skip_node_before_drain

- name: Drain {{ inventory_hostname }}
  ansible.builtin.shell: "kubectl drain {{ inventory_hostname }} --ignore-daemonsets --delete-local-data --force --disable-eviction --grace-period=60"
  delegate_to: "{{ sms_vars['kubernetes_master_hostname'] }}"
  when:
    - _packages_to_upgrade.stdout | length > 0
