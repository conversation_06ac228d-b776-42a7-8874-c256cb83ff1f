# DEPRECATED: check kubernetes_check_pre
#
# This will end the job if there are too many unavailable kubernetes nodes
#
# Usage:
# kubernetes_master_hostname: kube3master.nydc1.outbrain.com
# kubernetes_max_unavailable_nodes: 10
---
- name: Variables check
  assert:
    that:
      - sms_vars['kubernetes_master_hostname'] is defined
      - sms_vars['kubernetes_max_unavailable_nodes'] is defined
    fail_msg: "Variables 'kubernetes_master_hostname' and kubernetes_max_unavailable_nodes must be defined!"

- name: Count unavailable nodes
  ansible.builtin.shell: "kubectl get nodes | grep -E 'NotReady|SchedulingDisabled' | wc -l | awk '{print $1}'"
  delegate_to: "{{ sms_vars['kubernetes_master_hostname'] }}"
  register: _unavailable_nodes

- debug:
    msg: "Skipping server as there are more than max unavailable nodes"
  when: 
    - not ansible_check_mode
    - _unavailable_nodes.stdout | int  > (sms_vars['kubernetes_max_unavailable_nodes']) | int


- name: End the job if there are more than max unavailable nodes
  meta: end_play
  when:
    - not ansible_check_mode
    - _unavailable_nodes.stdout | int  > (sms_vars['kubernetes_max_unavailable_nodes']) | int
