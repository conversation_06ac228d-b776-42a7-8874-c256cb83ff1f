# This task will wait for the desired amout of seconds. Defaults to 60s.
#
# Usage:
# wait_seconds: 60
---
- name: <PERSON>ser<PERSON> wait_seconds is defined
  assert:
    that:
      - sms_vars['wait_seconds'] is defined
    fail_msg: "Variable 'wait_seconds' is not defined!"
    quiet: true

- name: "Wait for {{ sms_vars['wait_seconds'] }} seconds"
  wait_for:
    timeout: "{{ sms_vars['wait_seconds'] }}"
