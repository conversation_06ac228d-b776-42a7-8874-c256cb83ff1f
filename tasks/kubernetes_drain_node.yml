# Performs drain operation of a kubernetes node
#
# Usage:
# kubernetes_master_hostname: kube3master.nydc1.outbrain.com
# kubernetes_grace_period: 120 (OPTIONAL: defaults to 300)
---
- name: Sanity check
  assert:
    that:
      - sms_vars['kubernetes_master_hostname'] is defined
    fail_msg: "Variable 'kubernetes_master_hostname' must be defined!"
    quiet: true

- name: Set default values
  set_fact:
    _k8s_grace_period: "{{ (sms_vars['kubernetes_grace_period']) | default(300) }}"

- name: Send Drain Event
  include_tasks: ../tasks/internal/raven_event.yml
  vars:
    raven_fields:
      node_name: "{{ inventory_hostname }}"
      action: "DRAIN"
      username: "awx"
      service: "awx-dog"
      obmessage: "SMS PATCH"
      kubeCluster: "{{ _consul_service_name.split('-')[0] }}"
    raven_message: "SMS PATCH"

- name: Drain {{ inventory_hostname }}
  ansible.builtin.shell: "kubectl drain {{ inventory_hostname }} --ignore-daemonsets --delete-local-data --force --disable-eviction --grace-period={{ _k8s_grace_period }}"
  delegate_to: "{{ sms_vars['kubernetes_master_hostname'] }}"
