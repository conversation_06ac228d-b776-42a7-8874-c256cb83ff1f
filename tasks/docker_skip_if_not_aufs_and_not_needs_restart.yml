---
- name: Check if docker needs restart
  ansible.builtin.shell: "curl -v --silent localhost:9100/metrics 2>&1 | grep docker_needs_restart_status{servicename | cut -d' ' -f2-"
  register: _docker_needs_restart_status

# Change  docker info --format '{{json .}}'| jq -r  '.Driver'
- name: Get docker storage driver name
  ansible.builtin.shell: "docker info --format '{{ '{{' }} json . {{ '}}' }}' | jq -r  '.Driver'"
  register: _docker_storage_driver_name

- debug:
    msg: "Skipping server as docker restart is not required or docker storage driver is not aufs"
  when:
    - _docker_needs_restart_status.stdout | int == 0
    - _docker_storage_driver_name.stdout != 'aufs'

- name: End the job in case docker restart is not required or docker storage driver is not aufs
  meta: end_host
  when: 
    - _docker_needs_restart_status.stdout | int == 0
    - _docker_storage_driver_name.stdout != 'aufs'
