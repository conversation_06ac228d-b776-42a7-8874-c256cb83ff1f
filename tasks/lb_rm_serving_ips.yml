# This task will remove serving ips from the node
---
- name: Gather loopback interface IPs
  set_fact:
    _lb_loopback_ips: "{{ ansible_lo.ipv4_secondaries | default([]) | select('defined') | map(attribute='address') | list }}"

- name: Get serving ips from the list
  set_fact:
    _lb_networks: "{{ _lb_loopback_ips[2:] | product(['/32']) | map('join') | list }}"

- name: Get ASN
  shell: "vtysh -c 'show running-config' | grep 'router bgp' | awk '{print $3}'"
  changed_when: false
  register: _lb_asn_result
  when: _lb_networks | length > 0

- name: Stop ip from being advertised
  shell: |
    vtysh -c "configure terminal" -c "router bgp {{ _lb_asn_result.stdout }}" -c "no network {{ item }}"
  loop: "{{ _lb_networks }}"
  when: _lb_networks | length > 0

- name: "Wait for traffic to stop"
  wait_for:
    timeout: 10
  when: _lb_networks | length > 0
