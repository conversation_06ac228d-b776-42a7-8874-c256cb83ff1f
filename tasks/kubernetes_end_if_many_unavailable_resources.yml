# DEPRECATED: check kubernetes_check_pre
#
# This will end the job if there are too many unavailable kubernetes nodes
#
# Usage:
# kubernetes_master_hostname: kube3master.nydc1.outbrain.com
# kubernetes_max_unavailable_cores: 1000
# kubernetes_max_unavailable_mem_gb: 500
---
- name: Variables check
  assert:
    that:
      - sms_vars['kubernetes_master_hostname'] is defined
      - sms_vars['kubernetes_max_unavailable_cores'] is defined
      - sms_vars['kubernetes_max_unavailable_mem_gb'] is defined
    fail_msg: "Variables 'kubernetes_master_hostname', 'kubernetes_max_unavailable_cores' and 'kubernetes_max_unavailable_mem_gb' must be defined!"

- name: Get unavailable cores
  ansible.builtin.shell: "kubectl get nodes | grep -E 'NotReady|SchedulingDisabled' | awk '{print \"kubectl get node \"$1\" -o json | jq -r .status.allocatable.cpu\"}'  | bash | awk '{cpu+=$1} END {print cpu}'"
  delegate_to: "{{ sms_vars['kubernetes_master_hostname'] }}"
  register: _kubernetes_unavailable_cores

- name: Get unavailable mem GB
  ansible.builtin.shell: "kubectl get nodes | grep -E 'NotReady|SchedulingDisabled' | awk '{print \"kubectl get node \"$1\" -o json | jq -r .status.allocatable.memory\"}' | bash | awk -F\"Ki\" '{cpu+=$1} END {print cpu}'"
  delegate_to: "{{ sms_vars['kubernetes_master_hostname'] }}"
  register: _kubernetes_unavailable_mem_gb

- debug:
    msg: "Skipping server as there are more than max unavailable cores"
  when: 
    - not ansible_check_mode
    - _kubernetes_unavailable_cores.stdout | int  > (sms_vars['kubernetes_max_unavailable_cores']) | int

- debug:
    msg: "Skipping server as there are more than max unavailable memory"
  when:
    - not ansible_check_mode
    - _kubernetes_unavailable_mem_gb.stdout | int * 0.000001  > (sms_vars['kubernetes_max_unavailable_mem_gb']) | int

- name: End the job if there are more than max unavailable cores
  meta: end_play
  when:
    - not ansible_check_mode
    - _kubernetes_unavailable_cores.stdout | int  > (sms_vars['kubernetes_max_unavailable_cores']) | int

- name: End the job if there are more than max unavailable memory
  meta: end_play
  when:
    - not ansible_check_mode
    - _kubernetes_unavailable_mem_gb.stdout | int * 0.000001  > (sms_vars['kubernetes_max_unavailable_mem_gb']) | int
