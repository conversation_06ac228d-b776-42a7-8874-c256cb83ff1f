# This task will fail if there is a 'Chain FORWARD (policy DROP)' configured in iptables
---
- name: Check iptables
  ansible.builtin.shell: "iptables -L | grep 'Chain FORWARD (policy DROP)' | wc -l"
  register: _number_of_drops

- name: Fail if drop policy exists
  assert:
    that:
      - _number_of_drops.stdout | int == 0
    fail_msg: "There is a Chain FORWARD (policy DROP) iptables policy configured. This might cause issues - failing the step"
