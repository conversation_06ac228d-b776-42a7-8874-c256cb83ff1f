# This task can start, stop or restart a specified supervisor service(s). It can be a single service or
# a comma-separeted list
#
# Usage:
# supervisor_name: kafka
# supervisor_state: [started|stopped|restarted]
---
- name: Validate variables
  assert:
    that:
      - sms_vars['supervisor_name'] is defined
      - sms_vars['supervisor_state'] is defined
    fail_msg: "Variable 'supervisor_name' or 'supervisor_status' not defined!"
    quiet: true

- name: Validate supervisor_status
  assert:
    that:
      - sms_vars['supervisor_state'] in ['started', 'stopped', 'restarted']
    fail_msg: "Invalid value {{ sms_vars['supervisor_state'] }}. Supported states are: started, stopped, restarted"
    quiet: true

- name: "{{ sms_vars['supervisor_name'] }} was {{ sms_vars['supervisor_state'] }} via supervisor"
  supervisorctl:
    name: "{{ item }}"
    state: "{{ sms_vars['supervisor_state'] }}"
  loop: "{{ sms_vars['supervisor_name'].split(',') }}"
