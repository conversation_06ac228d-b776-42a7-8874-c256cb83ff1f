# DEPRECATED: check kuberne<PERSON>_check_pre
#
# This will end the run on a node if critica memcached is running on it - memcachedmerchant, memcachedstatsdyploma, memcachedpaidservingdyploma
---
- name: Check if critical memcached is running
  ansible.builtin.shell: "docker ps | grep -E 'memcachedmerchant|memcachedstatsdyploma|memcachedpaidservingdyploma' | wc -l | awk '{print $1}'"
  register: _number_of_critical_memcached

- debug:
    msg: "Skipping server as critical memcached is running on the server"
  when: _number_of_critical_memcached.stdout | int  > 0

- name: End the job if critical memcached is running on the node
  meta: end_host
  when: _number_of_critical_memcached.stdout | int  > 0
