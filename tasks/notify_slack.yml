# This task by default will send a slack notification with the following content: "Job {{ sms_job }} has finished
# running on {{ inventory_hostname }}." It means that by default is intended to send a notification when the job
# successfully completed on each node. However, it allows you to change the message and send any notification that you
# want.
#
# Usage:
#   notify_slack_channel: #evt-team_name
#   notify_slack_message: This is my custom message (optional)
#   notify_slack_type: [beginning|end] (optional, defaults to end)
---
# validate
- name: Validate that notify_slack_channel is defined
  assert:
    that:
      - sms_vars['notify_slack_channel'] is defined
    fail_msg: "Variable 'notify_slack_channel' is not defined!"
    quiet: true

# set values
- name: Set custom slack message
  set_fact:
    _notify_slack_message: '{{ sms_vars["notify_slack_message"] }}'
  when: sms_vars['notify_slack_message'] is defined

- name: Set default end slack message
  set_fact:
    _notify_slack_message: "Job {{ sms_job }} finished running on {{ inventory_hostname }}."
  when:
    - sms_vars['notify_slack_message'] is not defined
    - (sms_vars['notify_slack_type'] | default('end')) != 'beginning'

- name: Set default beginning slack message
  set_fact:
    _notify_slack_message: "Job {{ sms_job }} started running on {{ inventory_hostname }}."
  when:
    - sms_vars['notify_slack_message'] is not defined
    - (sms_vars['notify_slack_type'] | default('end')) == 'beginning'

- name: Send slack notification
  community.general.slack:
    token: "{{ slack_token }}"
    channel: "{{ sms_vars['notify_slack_channel'] }}"
    msg: "{{ _notify_slack_message }}"
    thread_id: "{{ sms_slack_thread_id }}"
  changed_when: false
