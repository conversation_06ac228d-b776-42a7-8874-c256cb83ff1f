# Execute an arbitrary shell command. Optionally you can execute it on another node than the one from inventory
#
# Usage:
# shell_command: bash /path/to/my/script.sh
# shell_executable: (OPTIONAL: defaults to /bin/bash)
# shell_execute_on_hostname: dog-10001-prod-nydc1.nydc1.outbrain.com (OPTIONAL: defaults to inventory_hostname)
# shell_timeout: 7200 (OPTIONAL: defaults to 3600 seconds)
# shell_retries: if added will retry in case of failure (OPTIONAL: by default will not retry)
# shell_delay_sec: if there are retries, delay in seconds between retries (OPTIONAL: defaults to 5 seconds)
---
- name: Sanity check
  assert:
    that:
      - sms_vars['shell_command'] is defined
    fail_msg: "Variable 'shell_command' is not defined!"
    quiet: true

- name: Run {{ sms_vars['shell_command'] }} on remote host with {{ sms_vars['shell_retries'] | default(0) }} retries
  shell: "{{ sms_vars['shell_command'] }}"
  args:
    executable: "{{ sms_vars['shell_executable'] | default('/bin/bash') }}"
  when:
    - not sms_check_mode  # TODO: this is a quick fix for async & check mode not working together
    - sms_vars['shell_execute_on_hostname'] is not defined
  register: _shell_command_output
  until: _shell_command_output is not failed
  retries: "{{ sms_vars['shell_retries'] | default(0) }}"
  delay: "{{ sms_vars['shell_delay_sec'] | default(5) }}"
  async: "{{ sms_vars['shell_timeout'] | default(3600) }}"
  poll: 5

- name: Run {{ sms_vars['shell_command'] }} on {{ sms_vars['shell_execute_on_hostname'] }}
  shell: "{{ sms_vars['shell_command'] }}"
  args:
    executable: "{{ sms_vars['shell_executable'] | default('/bin/bash') }}"
  delegate_to: "{{ sms_vars['shell_execute_on_hostname'] }}"
  when:
    - not sms_check_mode  # TODO: this is a quick fix for async & check mode not working together
    - sms_vars['shell_execute_on_hostname'] is defined
    - sms_vars['shell_retries'] is not defined
  async: "{{ sms_vars['shell_timeout'] | default(3600) }}"
  poll: 5
