# Send machinelog of finish event with the hostname it runs on
---
- set_fact:
    _finish_event_dry_run_kv: "-kv 'dry_run: true'"
  when: sms_check_mode

- name: Send finish event
  shell: "/usr/local/bin/sendobevent -o '{{ sms_team }}' -t machinelog -or sms -m 'Finished SMS flow' -kv 'service:sms' -kv 'action:finish' -kv 'user:{{ sms_user }}' -kv 'job:{{ sms_job }}' -kv 'hostname:{{ inventory_hostname }}' {{ _finish_event_dry_run_kv | default('') }}"
  ignore_errors: yes
  check_mode: false