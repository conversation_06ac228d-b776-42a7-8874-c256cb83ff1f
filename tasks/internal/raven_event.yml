# This task will send an event or raven endpoint.
#
# Usage:
#- name: Send raven event
#  include_tasks: ../tasks/internal/raven_event.yml
#  vars:
#    raven_fields:
#      action: "Some action"
#      hostname: "{{ inventory_hostname }}"
#      service: "Some service"
#    raven_message: "This is a message"
---
- name: San<PERSON> check raven_message
  assert:
    that:
      - raven_fields is defined
      - raven_fields is mapping
      - raven_message is defined
    fail_msg: "Variables 'raven_fields' and 'raven_message' must be defined!"
    quiet: true

- name: Send Raven Event
  uri:
    url: "http://raven.outbrain.com:8080/Raven/api/sendEvent"
    method: PUT
    headers:
      Content-Type: "application/json"
    body_format: json
    body:
      owner: "{{ sms_team }}"
      type: "machinelog"
      origin: "sms"
      map: "{{ raven_fields }}"
      message: "{{ raven_message }}"
    status_code: 200
  delegate_to: localhost
  ignore_errors: yes
