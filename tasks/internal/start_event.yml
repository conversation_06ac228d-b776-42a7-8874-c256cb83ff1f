# Send machinelog of start event with the hostname it runs on
---
- set_fact:
    _start_event_dry_run_kv: "-kv 'dry_run: true'"
  when: sms_check_mode

- name: Send start event
  shell: "/usr/local/bin/sendobevent -o '{{ sms_team }}' -t machinelog -or sms -m 'Started SMS flow' -kv 'service:sms' -kv 'action:start' -kv 'user:{{ sms_user }}' -kv 'job:{{ sms_job }}' -kv 'hostname:{{ inventory_hostname }}' {{ _start_event_dry_run_kv | default('') }}"
  ignore_errors: yes
  check_mode: false
  changed_when: false
