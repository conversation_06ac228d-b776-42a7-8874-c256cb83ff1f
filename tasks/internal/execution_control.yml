---
- name: Fail everything on any error
  assert:
    that:
      - (ansible_play_hosts | length) == (ansible_play_hosts_all | length)
    fail_msg: "Stopping execution because of an error!"
    quiet: true
  when: option_any_errors_fatal | default(false) | bool

- name: Set default values
  set_fact:
    _option_max_fail_percentage: "{{ option_max_fail_percentage | default(10) | int }}"

- name: Fail everything when % of failed hosts over max_fail_percentage
  assert:
    that:
      - ((ansible_play_hosts | length) / (ansible_play_hosts_all | length) * 100) >= (100 - (_option_max_fail_percentage | int))
    fail_msg: "Stopping execution because % of failed nodes is over {{ _option_max_fail_percentage }}"
    quiet: true

- name: Check if no_chef_run is set
  ansible.builtin.command: /bin/bash manage_no_chef_run.sh status
  register: _ec_ncr_status
  changed_when: false
  failed_when: false

- name: Output message if no_chef_run is set
  debug:
    msg: "Skipping execution because no chef run is set: {{ _ec_ncr_status.stdout.split('\n')[1] }}"
  when: _ec_ncr_status.rc == 0 and _ec_ncr_status.stdout.startswith("no_chef_run is SET")

- name: Skip this node if no_chef_run is set
  meta: end_host
  when: _ec_ncr_status.rc == 0 and _ec_ncr_status.stdout.startswith("no_chef_run is SET")
