# This will wait until a node becomes Ready/NotReady in kubernetes, and will fail if retries are maxed out
#
# Usage:
# kubernetes_master_hostname: kube3master.nydc1.outbrain.com
# kubernetes_node_state: Ready / NotReady
# kubernetes_node_state_retries: 30 (OPTIONAL: defaults to 60)
# kubernetes_node_state_delay: 60 (OPTIONAL: defaults to 60)
---
- name: Variables check
  assert:
    that:
      - sms_vars['kubernetes_master_hostname'] is defined
      - sms_vars['kubernetes_node_state'] is defined
    fail_msg: "Variables 'kubernetes_master_hostname' and 'kubernetes_node_state' must be defined!"

- name: "Wait until node becomes {{ sms_vars['kubernetes_node_state'] }}"
  ansible.builtin.shell: "kubectl get nodes | grep {{ inventory_hostname }} | grep -w {{ sms_vars['kubernetes_node_state'] }} | wc -l | awk '{print $1}'"
  delegate_to: "{{ sms_vars['kubernetes_master_hostname'] }}"
  register: _nodes
  until: _nodes.stdout | int == 1
  retries: "{{ (sms_vars['kubernetes_node_state_retries'] | default(60)) }}"
  delay: "{{ (sms_vars['kubernetes_node_state_delay'] | default(60)) }}"
