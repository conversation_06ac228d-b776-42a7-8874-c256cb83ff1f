# Executes OS patching via ansible. You can control if you want to upgrade the kernel (by defualt is true) and if you
# allow the server to be rebooted (by default is true). Additionaly you can specify additional packeges that are
# prevented by being upgraded by the unattended-upgrades.

# Usage:
#   os_patch_reboot_allowed: [true|false] (optional, defaults to true)
#   os_patch_additional_blacklisted_packages: (optional)
#   os_patch_allow_powercycle: [true|false] (optional, defaults to false)
---
# validate
- name: Validate os_patch_reboot_allowed
  assert:
    that:
      - (sms_vars['os_patch_reboot_allowed']) | lower in ['true', 'false']
    fail_msg: "Value {{ sms_vars['os_patch_reboot_allowed'] }} is invalid. It must be true or false"
    quiet: true
  when: sms_vars['os_patch_reboot_allowed'] is defined

- name: Validate os_patch_additional_blacklisted_packages
  assert:
    that:
      - (sms_vars['os_patch_additional_blacklisted_packages']) | type_debug == "list"
    fail_msg: "Value {{ sms_vars['os_patch_additional_blacklisted_packages'] }} is invalid. It must be a list"
    quiet: true
  when: sms_vars['os_patch_additional_blacklisted_packages'] is defined

- name: Validate os_patch_allow_powercycle
  assert:
      that:
      - (sms_vars['os_patch_allow_powercycle']) | lower in ['true', 'false']
      fail_msg: "Value {{ sms_vars['os_patch_allow_powercycle'] }} is invalid. It must be true or false"
      quiet: true
  when: sms_vars['os_patch_allow_powercycle'] is defined

- name: Validate we run OS patch on Ubuntu >= 16.04 system
  assert:
    that:
      - ansible_distribution == 'Ubuntu' and ansible_distribution_version is version('16.04', '>=')
    fail_msg: "This task can be executed only on Ubuntu >= 16.04 linux system"
    quiet: true

# set values
- name: Set default values
  set_fact:
    _os_patch_reboot_allowed: "{{ (sms_vars['os_patch_reboot_allowed']) | default(true) | bool }}"
    _os_patch_allow_powercycle: "{{ (sms_vars['os_patch_allow_powercycle']) | default(false) | bool }}"

- name: Set blacklisted packages
  set_fact:
    _os_patch_blacklisted_packages:
      - quagga*
      - frr*
      - python-clcmd*
      - chef*

- name: Merge additional blacklisted packages
  set_fact:
    _os_patch_blacklisted_packages: "{{ _os_patch_blacklisted_packages + sms_vars['os_patch_additional_blacklisted_packages'] }}"
  when: sms_vars['os_patch_additional_blacklisted_packages'] is defined

# os patching
- name: Backup original 50unattended-upgrades when in check mode
  copy:
    src: "/etc/apt/apt.conf.d/50unattended-upgrades"
    dest: "/tmp/50unattended-upgrades"
    remote_src: true
  when: sms_check_mode
  check_mode: false

- name: Copy the 50unattended-upgrades file
  template:
    src: "../templates/50unattended-upgrades.j2"
    dest: "/etc/apt/apt.conf.d/50unattended-upgrades"
    owner: root
    group: root
    mode: 644
    backup: true
  check_mode: false

- name: Update apt cache
  apt:
    update_cache: true
  check_mode: false

- name: Run unattended upgrades
  shell: "unattended-upgrade"
  async: 3600
  poll: 30
  when: not sms_check_mode

- name: Run unattended upgrades with dry run
  shell: "unattended-upgrade --dry-run -v"
  when: sms_check_mode
  check_mode: false

- name: Restore 50unattended-upgrades when in check mode
  copy:
    src: "/tmp/50unattended-upgrades"
    dest: "/etc/apt/apt.conf.d/50unattended-upgrades"
    remote_src: true
  when: sms_check_mode
  check_mode: false

- name: Read /var/log/unattended-upgrades/unattended-upgrades.log content
  shell: "cat /var/log/unattended-upgrades/unattended-upgrades.log"
  register: _os_patch_log
  when: sms_check_mode
  check_mode: false

- name: Print /var/log/unattended-upgrades/unattended-upgrades.log
  debug:
    msg: "{{ _os_patch_log }}"
  when: sms_check_mode

- name: Check if '/var/run/reboot-required' is present and reboot is needed
  stat:
    path: /var/run/reboot-required
  register: _os_patch_reboot_required_file
  when: _os_patch_reboot_allowed

- name: Reboot and wait for startup
  reboot:
    msg: "Reboot initiated by SMS"
    connect_timeout: 15
    reboot_timeout: 900
    post_reboot_delay: 120
  ignore_errors: yes
  register: _os_patch_reboot_status
  when:
    - _os_patch_reboot_allowed
    - _os_patch_reboot_required_file.stat.exists

- name: Wait for connection if previous task failed
  wait_for_connection:
    connect_timeout: 15
    sleep: 10
    timeout: 60
  ignore_errors: "{{ _os_patch_allow_powercycle }}"
  register: _os_patch_wfc_status
  when:
    - _os_patch_reboot_allowed
    - _os_patch_reboot_required_file.stat.exists
    - _os_patch_reboot_status is failed

- name: Powercycle the server
  community.general.ipmi_power:
    name: "{{ ipmi_ip }}"
    password: !vault |
      $ANSIBLE_VAULT;1.2;AES256;sms
      65386266633439653434303863663937353532353231333836653237393032306333653463343537
      3565343436663837313830353032643338393935346366660a613732323032643234616565653065
      30396535653234373866666637393233383264313234363232656235353335323232313336656335
      6139323637303635340a393133643932346439323335353438613337633037356163303432376332
      3537
    user: "root"
    state: reset
  delegate_to: localhost
  when:
    - _os_patch_wfc_status is failed
    - _os_patch_allow_powercycle

- name: Wait for connection after powercycle
  wait_for_connection:
    connect_timeout: 15
    delay: 120
    sleep: 10
    timeout: 900
  when:
    - _os_patch_wfc_status is failed
    - _os_patch_allow_powercycle

- name: Prepare last_update content
  set_fact:
    _os_patch_last_update_content: {
      "epoch": "{{ ansible_date_time.epoch }}",
      "human": "{{ ansible_date_time.iso8601 }}",
      "quarter": "{{ '%Y' | strftime }}q{{ (('%-m' | strftime | int - 1) / 3) | round(0, 'floor') | int + 1 }}"
    }
    _os_patch_last_update_q: "{{ '%Y' | strftime }}q{{ (('%-m' | strftime | int - 1) / 3) | round(0, 'floor') | int + 1 }}"

- name: Update last_update file with the year and quarter
  copy:
    content: "{{ _os_patch_last_update_content | to_json(indent=2) }}"
    dest: "/var/log/unattended-upgrades/last_update"
    owner: root
    group: root
    mode: 0644

- name: Clean old packages and kernels
  apt:
    autoremove: true
    purge: true

- name: Set Azure VM tag with patch_last_updated
  azure.azcollection.azure_rm_tags:
    tenant: "{{ azure_tenant }}"
    secret: "{{ azure_secret }}"
    client_id: "{{ azure_client_id }}"
    subscription_id: "{{ azure_subscription_id }}"
    scope: "{{ id }}"
    tags:
      patch_last_updated: '{{ _os_patch_last_update_q }}'
  delegate_to: localhost
  ignore_errors: yes
  when:
    - resource_type is defined
    - resource_type == "Microsoft.Compute/virtualMachines"
