# DEPRECATED: check kubernetes_check_pre
#
# This will skip the node if the node is tainted
#
# Usage:
# kubernetes_master_hostname: kube3master.nydc1.outbrain.com
---
- name: Variables check
  assert:
    that:
      - sms_vars['kubernetes_master_hostname'] is defined
    fail_msg: "Variable 'kubernetes_master_hostname' must be defined!"
    quiet: true

- name: Verify if node has taints
  ansible.builtin.shell: "kubectl get node {{ inventory_hostname }} -o jsonpath='{range .spec.taints[*]}{.key} {end}' | wc -w | awk '{print $1}'"
  delegate_to: "{{ sms_vars['kubernetes_master_hostname'] }}"
  register: _kubernetes_skip_if_node_tainted

- debug:
    msg: "Skipping server as its tainted"
  when: 
    - not ansible_check_mode
    - _kubernetes_skip_if_node_tainted.stdout | int  > 0

- name: End the job when the host is tainted
  meta: end_host
  when:
    - not ansible_check_mode
    - _kubernetes_skip_if_node_tainted.stdout | int  > 0
