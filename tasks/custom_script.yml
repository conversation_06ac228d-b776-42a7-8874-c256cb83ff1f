# Provide the name of the custom script and optionaly the executable. Optionally you can execute the script on another node
# than the one from inventory
#
# Usage:
# custom_script_name: test_script.sh
# custom_script_executable: python (OPTIONAL: defaults to /bin/bash)
# custom_script_execute_on_hostname: dog-10001-prod-nydc1.nydc1.outbrain.com (OPTIONAL: defaults to inventory_hostname)
---
- name: Sanity check
  assert:
    that:
      - sms_vars['custom_script_name'] is defined
    fail_msg: "Variable 'custom_script_name' is not defined!"
    quiet: true

- name: Run {{ sms_vars['custom_script_name'] }} on remote host
  ansible.builtin.script: "../files/{{ sms_vars['custom_script_name'] }}"
  args:
    executable: "{{ custom_script_executable | default('/bin/bash') }}"
  when: custom_script_execute_on_hostname is not defined

- name: Run {{ sms_vars['custom_script_name'] }} on {{ custom_script_execute_on_hostname }}
  ansible.builtin.script: "../files/{{ sms_vars['custom_script_name'] }}"
  args:
    executable: "{{ custom_script_executable | default('/bin/bash') }}"
  delegate_to: "{{ custom_script_execute_on_hostname }}"
  when: custom_script_execute_on_hostname is defined
