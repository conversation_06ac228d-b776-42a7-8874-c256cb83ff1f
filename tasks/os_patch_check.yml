# This task will check if the node needs to be patched. The node requires patching if it has passed more than 6 months
# (or defined by os_patch_after_days) since the last patch. The check is done by checking the last_update file in
# /var/log/unattended-upgrades/last_update.
#
# If 'os_patch_check_command' is defined, it will be executed, valid exit codes are 0 an 1. If it returns exit code 1,
# the node will be skipped.

# Usage:
#   os_patch_check_command: if [ $should_skip = 'true' ]; then exit 1; else exit 0'; fi (optional)
#   os_patch_after_days: 30 (optional, defaults to 180)
---
- name: Validate os_patch_after_days
  assert:
    that:
      - (sms_vars['os_patch_after_days']) | int >= 0
    fail_msg: "Value {{ sms_vars['os_patch_after_days'] }} is invalid. It must be an integer greater or equal to 0"
    quiet: true
  when: sms_vars['os_patch_after_days'] is defined

# set values
- name: Set default values
  set_fact:
    _os_patch_after_days: "{{ (sms_vars['os_patch_after_days']) | default(omit) | int }}"

- name: Check if patch required
  patch_required:
    os_patch_after_days: "{{ _os_patch_after_days }}"
  register: _os_patch_check

- name: OS patch check status
  debug:
    msg: "{{ _os_patch_check.message }}"

- name: End the current run on the node if the node is already patched
  meta: end_host
  when: not _os_patch_check.patch_required

# check os_patch_check_command
- name: Run 'os_patch_check_command' command
  shell: "{{ sms_vars['os_patch_check_command'] }}"
  register: _os_patch_check_result
  failed_when: _os_patch_check_result.rc not in [0, 1]
  changed_when: false
  check_mode: false
  args:
    executable: /bin/bash
  when: sms_vars['os_patch_check_command'] is defined

- debug:
    msg: "'os_patch_check_command' returned exit code 1. Skipping!"
  when:
    - sms_vars['os_patch_check_command'] is defined
    - _os_patch_check_result.rc == 1

- name: Skip host as 'os_patch_check_command' returned exit code 1
  meta: end_host
  when:
    - sms_vars['os_patch_check_command'] is defined
    - _os_patch_check_result.rc == 1
