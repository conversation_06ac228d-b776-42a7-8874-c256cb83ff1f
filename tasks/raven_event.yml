# This task will send an event or raven endpoint.
# NOTE: if you want to include it into another task check internal/raven_event.yml
#
# Usage:
# raven_fields: {'field1':'value1','field2':'value2'}
# raven_message: "This is a message"
---
- name: Sanity check raven_message
  assert:
    that:
      - sms_vars['raven_fields'] is defined
      - sms_vars['raven_fields'] is mapping
      - sms_vars['raven_message'] is defined
    fail_msg: "Variables 'raven_fields' and 'raven_message' must be defined!"
    quiet: true

- name: Send Raven Event
  uri:
    url: "http://raven.outbrain.com:8080/Raven/api/sendEvent"
    method: PUT
    headers:
      Content-Type: "application/json"
    body_format: json
    body:
      owner: "{{ sms_team }}"
      type: "machinelog"
      origin: "sms"
      map: "{{ sms_vars['raven_fields'] }}"
      message: "{{ sms_vars['raven_message'] }}"
    status_code: 200
  delegate_to: localhost
  ignore_errors: yes
