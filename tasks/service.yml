# This task can start, stop, restart or reload specified service(s). It can be a single service or a comma-separeted list
#
# Usage:
# service_name: nginx
# service_state: [started|stopped|restarted|reloaded]
---
- name: Validate variables
  assert:
    that:
      - sms_vars['service_name'] is defined
      - sms_vars['service_state'] is defined
    fail_msg: "Variable 'service_name' or 'service_state' not defined!"
    quiet: true

- name: Validate service_state
  assert:
    that:
      - sms_vars['service_state'] in ['started', 'stopped', 'restarted', 'reloaded']
    fail_msg: "Invalid value {{ sms_vars['service_state'] }}. Supported states are: started, stopped, restarted, reloaded"

- name: "Service {{ sms_vars['service_name'] }} was {{ sms_vars['service_state'] }}"
  service:
    name: "{{ item }}"
    state: "{{ sms_vars['service_state'] }}"
  loop: "{{ sms_vars['service_name'].split(',') }}"
