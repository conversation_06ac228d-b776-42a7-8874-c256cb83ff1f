# This task checks if the current namenode is active and if is active it failover to the standby namenode
---
- name: Namenodes facts
  set_fact:
    _hadoop_namenodes:
      - "hadoop-10001-prod-nydc1.nydc1.outbrain.com"
      - "hadoop-10002-prod-nydc1.nydc1.outbrain.com"
      - "hadoop-40001-prod-chidc2.chidc2.outbrain.com"
      - "hadoop-40002-prod-chidc2.chidc2.outbrain.com"
      - "hadooptest-10001-prod-nydc1.nydc1.outbrain.com"
      - "hadooptest-10002-prod-nydc1.nydc1.outbrain.com"
      - "hadooptest-40001-prod-chidc2.chidc2.outbrain.com"
      - "hadooptest-40002-prod-chidc2.chidc2.outbrain.com"

- name: Is namenode
  set_fact:
    _hadoop_is_nn: "{{ inventory_hostname in _hadoop_namenodes }}"

# get namenode ids and state
- name: Determine namenode environment and hostnames
  set_fact:
    _hadoop_namenode01_hostname: "{{ 'hadooptest-namenode01' if inventory_hostname.startswith('hadooptest-') else 'hadoop-namenode01' }}"
    _hadoop_namenode02_hostname: "{{ 'hadooptest-namenode02' if inventory_hostname.startswith('hadooptest-') else 'hadoop-namenode02' }}"
  when: _hadoop_is_nn | bool

- name: Resolve namenode01 hostname
  ansible.builtin.shell: "host {{ _hadoop_namenode01_hostname }}"
  register: _hadoop_namenode01_host
  check_mode: false
  changed_when: false
  when: _hadoop_is_nn | bool

- name: Resolve namenode02 hostname
  ansible.builtin.shell: "host {{ _hadoop_namenode02_hostname }}"
  register: _hadoop_namenode02_host
  check_mode: false
  changed_when: false
  when: _hadoop_is_nn | bool

- name: Get all namenode service states
  ansible.builtin.shell: hdfs haadmin -getAllServiceState
  environment:
    PATH: "{{ ansible_env.PATH }}:/opt/hadoop/bin"
    HADOOP_USER_NAME: "hdfs"
    JAVA_HOME: "/usr/lib/jvm/default-java"
  become: true
  become_user: hdfs
  register: _hadoop_service_states
  check_mode: false
  changed_when: false
  when: _hadoop_is_nn | bool

- name: Parse namenode service states
  ansible.builtin.set_fact:
    _hadoop_current_node_ip: "{{ ansible_default_ipv4.address }}"
    _hadoop_namenode01_ip: "{{ _hadoop_namenode01_host.stdout | regex_search('(\\d+\\.\\d+\\.\\d+\\.\\d+)') }}"
    _hadoop_namenode02_ip: "{{ _hadoop_namenode02_host.stdout | regex_search('(\\d+\\.\\d+\\.\\d+\\.\\d+)') }}"
  when: _hadoop_is_nn | bool

- name: Determine current namenode identity
  ansible.builtin.set_fact:
    _hadoop_current_namenode: "{{ 'namenode01' if _hadoop_current_node_ip == _hadoop_namenode01_ip else 'namenode02' if _hadoop_current_node_ip == _hadoop_namenode02_ip else 'unknown' }}"
    _hadoop_current_nn_id: "{{ 'nn1' if _hadoop_current_node_ip == _hadoop_namenode01_ip else 'nn2' if _hadoop_current_node_ip == _hadoop_namenode02_ip else 'unknown' }}"
  when: _hadoop_is_nn | bool

- name: Determine other namenode identity
  ansible.builtin.set_fact:
    _hadoop_other_namenode: "{{ 'namenode01' if _hadoop_current_namenode == 'namenode02' else 'namenode02' }}"
    _hadoop_other_nn_id: "{{ 'nn1' if _hadoop_current_nn_id == 'nn2' else 'nn2' }}"
  when: _hadoop_is_nn | bool

# assert valid id
- name: Assert current namenode id is valid
  ansible.builtin.assert:
    that:
      - _hadoop_current_namenode in ['namenode01', 'namenode02']
      - _hadoop_current_nn_id in ['nn1', 'nn2']
      - _hadoop_other_namenode in ['namenode01', 'namenode02']
      - _hadoop_other_nn_id in ['nn1', 'nn2']
    fail_msg: "Invalid namenode ids"
  when: _hadoop_is_nn | bool

- name: Check if current node is active namenode
  ansible.builtin.set_fact:
    _hadoop_is_active: "{{ _hadoop_service_states.stdout | regex_search(_hadoop_current_namenode + '.*active', ignorecase=true) is not none }}"
  when: _hadoop_is_nn | bool

# Perform failover if current node is active
- name: Perform namenode failover if current node is active
  ansible.builtin.shell: "hdfs haadmin -failover {{ _hadoop_current_nn_id }} {{ _hadoop_other_nn_id }}"
  environment:
    PATH: "{{ ansible_env.PATH }}:/opt/hadoop/bin"
    HADOOP_USER_NAME: "hdfs"
    JAVA_HOME: "/usr/lib/jvm/default-java"
  become: true
  become_user: hdfs
  when:
    - _hadoop_is_nn | bool
    - _hadoop_is_active | bool
  register: _hadoop_failover_result

- name: Wait 1 minute after failover
  ansible.builtin.pause:
    seconds: 60
  when:
    - _hadoop_is_nn | bool
    - _hadoop_is_active | bool

# Verify failover was successful
- name: Get namenode service states after failover
  ansible.builtin.shell: hdfs haadmin -getAllServiceState
  environment:
    PATH: "{{ ansible_env.PATH }}:/opt/hadoop/bin"
    HADOOP_USER_NAME: "hdfs"
    JAVA_HOME: "/usr/lib/jvm/default-java"
  become: true
  become_user: hdfs
  register: _hadoop_service_states_post_failover
  check_mode: false
  changed_when: false
  when:
    - _hadoop_is_nn | bool
    - _hadoop_is_active | bool

- name: Verify state
  ansible.builtin.set_fact:
    _hadoop_is_now_standby: "{{ _hadoop_service_states_post_failover.stdout | regex_search(_hadoop_current_namenode + '.*standby', ignorecase=true) is not none }}"
    _hadoop_other_is_active: "{{ _hadoop_service_states_post_failover.stdout | regex_search(_hadoop_other_namenode + '.*active', ignorecase=true) is not none }}"
  when:
    - _hadoop_is_nn | bool
    - _hadoop_is_active | bool

- name: Assert failover was successful
  ansible.builtin.assert:
    that:
      - _hadoop_is_now_standby | bool
      - _hadoop_other_is_active | bool
    fail_msg: "Failover verification failed - current node should be standby and other node should be active"
    success_msg: "Failover successful - {{ _hadoop_current_namenode }} is now standby, {{ _hadoop_other_namenode }} is now active"
  when:
    - _hadoop_is_nn | bool
    - _hadoop_is_active | bool

# Check that safemode is disabled
- name: Check HDFS safemode status
  ansible.builtin.shell: hdfs dfsadmin -safemode get
  environment:
    PATH: "{{ ansible_env.PATH }}:/opt/hadoop/bin"
    HADOOP_USER_NAME: "hdfs"
    JAVA_HOME: "/usr/lib/jvm/default-java"
  become: true
  become_user: hdfs
  register: _hadoop_safemode_status
  changed_when: false
  when:
    - _hadoop_is_nn | bool
    - _hadoop_is_active | bool

# asert that safemode is off
- name: Assert safemode is off
  ansible.builtin.assert:
    that: "'ON' not in _hadoop_safemode_status.stdout"
    fail_msg: "Safemode is ON"
  when:
    - _hadoop_is_nn | bool
    - _hadoop_is_active | bool
