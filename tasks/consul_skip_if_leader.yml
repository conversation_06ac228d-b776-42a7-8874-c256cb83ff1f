# This task will check if the node is the Consul leader.
---
- name: Check if Consul node is the leader
  ansible.builtin.shell: "consul operator raft list-peers | grep $(hostname) | grep leader | wc -l"
  register: _consul_leader_check
  check_mode: false

- debug:
    msg: "Skipping server as it is the Consul leader"
  when:
    - _consul_leader_check.stdout | int  > 0

- name: End the job if the server is the leader
  meta: end_host
  when:
    - _consul_leader_check.stdout | int  > 0