# This will validate if os_patch step is going to upgrade an apt package that has a specific version in chef
---
- name: Get the no_chef_run file
  stat:
    path: "/outbrain/no_chef_run"
  register: _no_chef_run

- name: Skip the node if no_chef_run is set
  fail:
    msg: "VALIDATION FAILED: No chef run is set on the server, cannot perform validation"
  when: _no_chef_run.stat.size > 0

- name: Get the unattended-upgrades file
  stat:
    path: "/var/log/unattended-upgrades/unattended-upgrades.log"
  register: _unattended_upgrades

- name: Skip the node if unattended-upgrades isnt present
  fail:
    msg: "VALIDATION FAILED: The file /var/log/unattended-upgrades/unattended-upgrades.log isn't present, cannot perform validation"
  when: _unattended_upgrades.stat.exists != True

- name: Run chef to get packages installed by chef
  shell: chef-client -l debug | grep --text apt_package | grep satisifies > /tmp/chef-installed.log

- name: Check if os_patch step is going to upgrade an apt package that has a specific version in chef
  shell: |
    packages_to_upgrade=$(grep "Packages that will be upgraded" /var/log/unattended-upgrades/unattended-upgrades.log | awk -F "Packages that will be upgraded: " '{print $2}')
    while read -r line
    do
      locked_package=$(echo $line | awk -F " satisifies " '{print $1}' | awk '{print $(NF-1)}')
      locked_version=$(echo $line | awk -F " satisifies " '{print $1}' | awk '{print $NF}')
      echo $packages_to_upgrade | grep -w $locked_package >/dev/null 2>&1
      if [[ $? -eq 0 ]]
      then
        upgraded_locked="$upgraded_locked,$locked_package==$locked_version"
      fi
    done < "/tmp/chef-installed.log"
    if [[ ! -z "$upgraded_locked" ]]
    then
      echo "VALIDATION FAILED: The following packages will be upgraded with os patch - $upgraded_locked"
    else
      echo "VALIDATION OK: No locked packages will be upgraded"
    fi
  args:
    executable: /bin/bash
  register: _package_validation

- debug:
    msg: "{{_package_validation.stdout}}"