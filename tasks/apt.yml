# Usage:
# apt_packages:
#   - telnet
#   - nginx=1.18.0-0ubuntu1.2
# apt_state: [present|absent] (OPTIONAL: defaults to present)
# apt_update_cache [true|false] (OPTIONAL: defaults to true)
---
- name: Validate variable 'apt_packages'
  assert:
    that:
      - sms_vars['apt_packages'] is defined
    fail_msg: "Variable 'apt_packages' is not defined!"
    quiet: true

- name: "Install {{ sms_vars['apt_packages'] }}"
  apt:
    name: "{{ sms_vars['apt_packages'] }}"
    state: "{{ sms_vars['apt_state'] | default('present') }}"
    update_cache: "{{ sms_vars['apt_update_cache'] | default(true) }}"
