# Usage:
# consul_node_name: kafka1-10001-prod-nydc1.nydc1.outbrain.com (OPTIONAL: defaults to inventory hostname)
# consul_node_retries: 30 (OPTIONAL: defaults to 5)
# consul_node_delay: 30 (OPTIONAL: defaults to 5)
---
- set_fact:
    _consul_node_name: "{{ sms_vars['consul_node_name'] | default(ansible_hostname) }}"
  when: inventory_hostname.endswith('zemanta.com')

- set_fact:
    _consul_node_name: "{{ sms_vars['consul_node_name'] | default(inventory_hostname) }}"
  when: inventory_hostname.endswith('outbrain.com')

- set_fact:
    _consul_node_retries: "{{ sms_vars['consul_node_retries'] | default(5) }}"
    _consul_node_delay: "{{ sms_vars['consul_node_delay'] | default(5) }}"

- block:
    - name: Get node consul data as JSON
      uri:
        url: "http://localhost:8500/v1/health/node/{{ _consul_node_name }}"
        method: "GET"
      retries: 5
      delay: 10
      until: _consul_node_data is not failed
      register: _consul_node_data
      check_mode: false

    - name: Extract node consul health status from JSON
      set_fact:
        _consul_node_status: "{{ _consul_node_data.json | selectattr('Name', 'equalto', 'Serf Health Status') | map(attribute='Status') | list }}"

    - name: Validate consul response
      assert:
        that:
          - _consul_node_status | length == 1
        fail_msg: "Invalid consul response, {{ _consul_node_status | length }} elements in the response"
        quiet: true

    - name: Validate node consul health status
      assert:
        that: "_consul_node_status | first == 'passing'"
        fail_msg: "Node is unhealthy, consul status is {{ _consul_node_status | first }}"
        quiet: true

  rescue:
    - name: Set the retry count
      set_fact:
        _consul_node_retry_count: "{{ 0 if _consul_node_retry_count is undefined else _consul_node_retry_count | int + 1 }}"

    - fail:
        msg: "Node is unhealthy, consul status is {{ _consul_node_status | first | default('UNKNOWN') }}."
      when: _consul_node_retry_count | int >= _consul_node_retries | int

    - debug:
        msg: "Retrying in {{ _consul_node_delay | int + 15 * _consul_node_retry_count | int }} seconds."

    - wait_for:
        timeout: "{{ _consul_node_delay | int + 15 * _consul_node_retry_count | int }}"

    - include_tasks: consul_check_node.yml
