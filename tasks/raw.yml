# Executes an arbitrary raw command.
#
# Usage:
# raw_command: cat < /tmp/*txt
# raw_timeout: 7200 (OPTIONAL: defaults to 3600 seconds)
---
- name: Sanity check
  assert:
    that:
      - sms_vars['raw_command'] is defined
    fail_msg: "Variable 'raw_command' is not defined!"
    quiet: true

- name: Run {{ sms_vars['raw_command'] }} on remote host
  raw: "{{ sms_vars['raw_command'] }}"
  args:
    executable: /bin/bash
  async: {{ sms_vars['raw_timeout'] | default(3600) }}
  poll: 5
