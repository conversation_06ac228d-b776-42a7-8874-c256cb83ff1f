# This task checks that the master node is ready for patching. It makes sure that all httpfs and journal services
# are running on all nodes. Besides that if the node is namenode it makes sure that the namenode service is running as well.
---

# Check hadoop-httpfs service health across all nodes
- name: Get all hadoop-httpfs instances from Consul
  uri:
    url: "http://localhost:8500/v1/health/service/hadoop-httpfs"
    method: GET
    status_code: 200
  register: _hadoop_httpfs_consul_response
  retries: 5
  delay: 10
  until: _hadoop_httpfs_consul_response is not failed
  check_mode: false

- name: Extract hadoop-httpfs health check variables
  set_fact:
    _hadoop_httpfs_all_checks: "{{ _hadoop_httpfs_consul_response.json | selectattr('Checks', 'defined') | map(attribute='Checks') | flatten | list }}"
    _hadoop_httpfs_passing_checks: "{{ _hadoop_httpfs_consul_response.json | selectattr('Checks', 'defined') | map(attribute='Checks') | flatten | selectattr('Status', 'equalto', 'passing') | list }}"

- name: Validate that all hadoop-httpfs instances are healthy
  assert:
    that:
      - _hadoop_httpfs_consul_response.json | length > 0
      - _hadoop_httpfs_passing_checks | length == _hadoop_httpfs_all_checks | length
    fail_msg: "Not all hadoop-httpfs instances are healthy. Found {{ _hadoop_httpfs_passing_checks | length }} healthy out of {{ _hadoop_httpfs_all_checks | length }} total instances"
    quiet: true

# Check hadoop-journalnode service health across all nodes
- name: Get all hadoop-journalnode instances from Consul
  uri:
    url: "http://localhost:8500/v1/health/service/hadoop-journalnode"
    method: GET
    status_code: 200
  register: _hadoop_journalnode_consul_response
  retries: 5
  delay: 10
  until: _hadoop_journalnode_consul_response is not failed
  check_mode: false

- name: Extract hadoop-journalnode health check variables
  set_fact:
    _hadoop_journalnode_all_checks: "{{ _hadoop_journalnode_consul_response.json | selectattr('Checks', 'defined') | map(attribute='Checks') | flatten | list }}"
    _hadoop_journalnode_passing_checks: "{{ _hadoop_journalnode_consul_response.json | selectattr('Checks', 'defined') | map(attribute='Checks') | flatten | selectattr('Status', 'equalto', 'passing') | list }}"

- name: Validate that all hadoop-journalnode instances are healthy
  assert:
    that:
      - _hadoop_journalnode_consul_response.json | length > 0
      - _hadoop_journalnode_passing_checks | length == _hadoop_journalnode_all_checks | length
    fail_msg: "Not all hadoop-journalnode instances are healthy. Found {{ _hadoop_journalnode_passing_checks | length }} healthy out of {{ _hadoop_journalnode_all_checks | length }} total instances"
    quiet: true

# namenode check
- name: Namenodes facts
  set_fact:
    _hadoop_namenodes:
      - "hadoop-10001-prod-nydc1.nydc1.outbrain.com"
      - "hadoop-10002-prod-nydc1.nydc1.outbrain.com"
      - "hadoop-40001-prod-chidc2.chidc2.outbrain.com"
      - "hadoop-40002-prod-chidc2.chidc2.outbrain.com"
      - "hadooptest-10001-prod-nydc1.nydc1.outbrain.com"
      - "hadooptest-10002-prod-nydc1.nydc1.outbrain.com"
      - "hadooptest-40001-prod-chidc2.chidc2.outbrain.com"
      - "hadooptest-40002-prod-chidc2.chidc2.outbrain.com"
    _hadoop_nn_service:
      - "hadoop-namenode"
      - "hadoop-standby-namenode"
      - "hadooptest-namenode"
      - "hadooptest-standby-namenode"
    _hadoop_zkfc_service:
      - "hadoop-zkfc"
      - "hadooptest-zkfc"


- name: Is namenode
  set_fact:
    _hadoop_is_nn: "{{ inventory_hostname in _hadoop_namenodes }}"

# Check namenode services when this node is a namenode
- name: Get consul services on this node
  uri:
    url: "http://localhost:8500/v1/health/node/{{ inventory_hostname }}"
    method: GET
    status_code: 200
  register: _hadoop_local_services_response
  retries: 3
  delay: 5
  until: _hadoop_local_services_response is not failed
  check_mode: false
  when: _hadoop_is_nn

- name: Extract namenode and zkfc services if present
  set_fact:
    _hadoop_namenode_passing_services: "{{ _hadoop_local_services_response.json | selectattr('ServiceName', 'in', _hadoop_nn_service) | selectattr('Status', 'equalto', 'passing') | list }}"
    _hadoop_zkfc_passing_services: "{{ _hadoop_local_services_response.json | selectattr('ServiceName', 'in', _hadoop_zkfc_service) | selectattr('Status', 'equalto', 'passing') | list }}"
  when: _hadoop_is_nn

- name: Validate namenode service is healthy
  assert:
    that:
      - _hadoop_namenode_passing_services | length == 1
    fail_msg: "Expected exactly one healthy namenode service, but found {{ _hadoop_namenode_passing_services | length }} passing services"
    quiet: true
  when: _hadoop_is_nn

- name: Validate hadoop-zkfc service is healthy
  assert:
    that:
      - _hadoop_zkfc_passing_services | length == 1
    fail_msg: "hadoop-zkfc service is not healthy"
    quiet: true
  when: _hadoop_is_nn
