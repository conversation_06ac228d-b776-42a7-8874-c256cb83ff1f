---
#- name: Sanity check
#  assert:
#    that:
#      - ansible_hostname is defined
#
#- name: Check node health
#  uri:
#    url: "http://consul.service.nydc1.consul:8500/v1/health/node/zemb1agent-10001-prod-nydc1.nydc1.outbrain.com"
#    method: "GET"
#  register: _consul_node_health
#
#- set_fact:
#    _consul_node_status: "{{ _consul_node_health.json | selectattr('Name', 'equalto', 'Serf Health Status') | map(attribute='Status') | first }}"
#
#- assert:
#    that:
#      - _consul_node_status == 'passing'
# TEST