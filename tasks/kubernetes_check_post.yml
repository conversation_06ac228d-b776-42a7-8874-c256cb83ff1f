# This task will check if the node is ready to be added back to the k8s cluster

---
- name: Execute post validation
  shell: "{{ item }}"
  args:
    executable: /bin/bash
  register: _kubernetes_check_post_ldap
  retries: 5
  delay: 10
  until: _kubernetes_check_post_ldap is not failed
  changed_when: false
  loop:
    - docker run --rm alpine sh -c 'if ! getent hosts ldap; then echo "ERROR! Cant resolve ldap"; exit 1; fi'
    - docker run --rm alpine sh -c 'if ! getent hosts {{ inventory_hostname }}; then echo "ERROR! Cant resolve {{ inventory_hostname }}"; exit 1; fi'
    - if ! getent hosts {{ inventory_hostname }}; then echo "ERROR! Can't resolve {{ inventory_hostname }}"; exit 1; fi
    - if ! getent hosts chefmaster.outbrain.com; then echo "ERROR! Can't resolve chefmaster.outbrain.com"; exit 1; fi
    - docker run --rm alpine sh -c 'if ! getent hosts consul.service.consul; then echo "ERROR! Cant resolve consul.service.consul"; exit 1; fi'
    - if iptables -L | grep 'Chain FORWARD (policy DROP)'; then echo "ERROR! has 'Chain FORWARD (policy DROP)' rule in iptable"; exit 1; fi
    - if [ "$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8500/v1/agent/self)" != "200" ]; then echo "Consul agent is not running or accessible"; exit 1; fi
    - !unsafe if [ "$( sudo docker container inspect -f '{{ .State.Running }}' registrator )" != "true" ]; then echo "Registrator container is not running or accessible"; exit 1; fi
    - for i in /etc/network/interfaces.d/*; do iface=$(basename $i); [[ $iface == "lo0" ]] &&  iface="lo" || [[ $iface == lo[1-9]* ]] && iface=$(echo $iface | sed 's/\([a-zA-Z]*\)\([0-9]\+\)/\1:\2/'); if ! ifconfig | grep "$iface" | grep -q "UP"; then echo "Interface $iface is down" && exit 1; fi; done
  async: 60
  poll: 5
