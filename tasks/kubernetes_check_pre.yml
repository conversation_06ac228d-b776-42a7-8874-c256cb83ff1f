# This task will check if the node can be patched. If any of the skip condition are met the node will be skipped.

# Usage:
# kubernetes_skip_ceph: [true|false] (OPTIONAL: defaults to true)
# kubernetes_skip_memcache: [true|false] (OPTIONAL: defaults to false)
# kubernetes_only_memcache: [true|false] (OPTIONAL: defaults to false)
# kubernetes_skip_tainted: [true|false] (OPTIONAL: defaults to true, IGNORES: node.kubernetes.io/unreachable and node.kubernetes.io/unschedulable taints)
# kubernetes_only_tainted: [true|false] (OPTIONAL: defaults to false, IGNORES: node.kubernetes.io/unreachable and node.kubernetes.io/unschedulable taints)
# kubernetes_skip_ingress: [true|false] (OPTIONAL: defaults to true)
# kubernetes_max_unavailable_percentage: 10 (OPTIONAL: defaults to 5)
# kubernetes_master_hostname: kube3master.nydc1.outbrain.com
---
# validate
- name: Validate kuberne<PERSON>_master_hostname
  assert:
    that:
      - sms_vars['kubernetes_master_hostname'] is defined
    fail_msg: "Variable 'kubernetes_master_hostname' is not defined!"
    quiet: true

- name: Validate kubernetes_skip_ceph
  assert:
    that:
      - (sms_vars['kubernetes_skip_ceph']) | lower in ['true', 'false']
    fail_msg: "Value {{ sms_vars['kubernetes_skip_ceph'] }} is invalid. It must be true or false"
    quiet: true
  when: sms_vars['kubernetes_skip_ceph'] is defined

- name: Validate kubernetes_skip_memcache
  assert:
    that:
      - (sms_vars['kubernetes_skip_memcache']) | lower in ['true', 'false']
    fail_msg: "Value {{ sms_vars['kubernetes_skip_memcache'] }} is invalid. It must be true or false"
    quiet: true
  when: sms_vars['kubernetes_skip_memcache'] is defined


- name: Validate kubernetes_only_memcache
  assert:
    that:
      - (sms_vars['kubernetes_only_memcache']) | lower in ['true', 'false']
    fail_msg: "Value {{ sms_vars['kubernetes_only_memcache'] }} is invalid. It must be true or false"
    quiet: true
  when: sms_vars['kubernetes_only_memcache'] is defined

- name: Validate kubernetes_skip_tainted
  assert:
    that:
      - (sms_vars['kubernetes_skip_tainted']) | lower in ['true', 'false']
    fail_msg: "Value {{ sms_vars['kubernetes_skip_tainted'] }} is invalid. It must be true or false"
    quiet: true
  when: sms_vars['kubernetes_skip_tainted'] is defined

- name: Validate kubernetes_only_tainted
  assert:
    that:
      - (sms_vars['kubernetes_only_tainted']) | lower in ['true', 'false']
    fail_msg: "Value {{ sms_vars['kubernetes_only_tainted'] }} is invalid. It must be true or false"
    quiet: true
  when: sms_vars['kubernetes_only_tainted'] is defined

- name: Validate kubernetes_skip_ingress
  assert:
    that:
      - (sms_vars['kubernetes_skip_ingress']) | lower in ['true', 'false']
    fail_msg: "Value {{ sms_vars['kubernetes_skip_ingress'] }} is invalid. It must be true or false"
    quiet: true
  when: sms_vars['kubernetes_skip_ingress'] is defined

- name: Validate kubernetes_max_unavailable_percentage is in range [0-100]
  assert:
    that:
      - (sms_vars['kubernetes_max_unavailable_percentage']) | int >= 0 and (sms_vars['kubernetes_max_unavailable_percentage']) | int <= 100
    fail_msg: "Value {{ sms_vars['kubernetes_max_unavailable_percentage'] }} is invalid. It must be an integer in range [0-100]"
    quiet: true
  when: sms_vars['kubernetes_max_unavailable_percentage'] is defined

# set values
- name: Set default values
  set_fact:
    _k8s_skip_ceph: "{{ (sms_vars['kubernetes_skip_ceph']) | default(true) | bool }}"
    _k8s_skip_memcache: "{{ (sms_vars['kubernetes_skip_memcache']) | default(false) | bool }}"
    _k8s_only_memcache: "{{ (sms_vars['kubernetes_only_memcache']) | default(false) | bool }}"
    _k8s_skip_tainted: "{{ (sms_vars['kubernetes_skip_tainted']) | default(true) | bool }}"
    _k8s_skip_ingress: "{{ (sms_vars['kubernetes_skip_ingress']) | default(true) | bool }}"
    _k8s_only_tainted: "{{ (sms_vars['kubernetes_only_tainted']) | default(false) | bool }}"
    _k8s_max_unavailable_pct: "{{ (sms_vars['kubernetes_max_unavailable_percentage']) | default(5) | int }}"

# check skip ceph
- name: Check if ceph is running (exclude the csi-rbdplugin daemon set containers)
  ansible.builtin.shell: "docker ps | grep _rook-ceph-block_ | grep -v _rook-ceph-tools | grep -v csi-rbdplugin | grep -v '/pause' | wc -l | awk '{print $1}'"
  register: _k8s_ceph_check
  when: _k8s_skip_ceph
  check_mode: false

- debug:
    msg: "Skipping server as ceph is running on the server"
  when:
    - _k8s_skip_ceph
    - _k8s_ceph_check.stdout | int  > 0

- name: End the job if ceph is running on the node
  meta: end_host
  when:
    - _k8s_skip_ceph
    - _k8s_ceph_check.stdout | int  > 0

# check skip memcache
- name: Check if memcached is running
  ansible.builtin.shell: "docker ps | grep k8s_memcached | wc -l | awk '{print $1}'"
  register: _k8s_memcache_check
  when: _k8s_skip_memcache or _k8s_only_memcache
  check_mode: false

- debug:
    msg: "Skipping server as memcached is running on the server"
  when:
    - _k8s_skip_memcache
    - _k8s_memcache_check.stdout | int  > 0

- name: End the job if memcached is running on the node
  meta: end_host
  when:
    - _k8s_skip_memcache
    - _k8s_memcache_check.stdout | int  > 0

# check only memcache
- debug:
    msg: "Skipping server as memcached is not running on the server"
  when:
    - _k8s_only_memcache
    - _k8s_memcache_check.stdout | int  == 0

- name: End the job if memcached is not running on the node
  meta: end_host
  when:
    - _k8s_only_memcache
    - _k8s_memcache_check.stdout | int  == 0

# check skip tainted
- name: Verify if node has taints
  ansible.builtin.shell: "kubectl get node {{ inventory_hostname }} -o json | jq '.spec.taints[] | select(.key | test(\"node.kubernetes.io/unschedulable|node.kubernetes.io/unreachable\") | not) | .key' | wc -w | awk '{print $1}'"
  delegate_to: "{{ sms_vars['kubernetes_master_hostname'] }}"
  register: _k8s_tainted_check
  when: _k8s_skip_tainted or _k8s_only_tainted
  check_mode: false

- debug:
    msg: "Skipping server as it's tainted"
  when:
    - _k8s_skip_tainted
    - _k8s_tainted_check.stdout | int  > 0

- name: End the job when the host is tainted
  meta: end_host
  when:
    - _k8s_skip_tainted
    - _k8s_tainted_check.stdout | int  > 0

# check only tainted
- debug:
    msg: "Skipping server as it is not tainted"
  when:
    - _k8s_only_tainted
    - _k8s_tainted_check.stdout | int  == 0

- name: End the job when the host is not tainted
  meta: end_host
  when:
    - _k8s_only_tainted
    - _k8s_tainted_check.stdout | int  == 0

# check skip ingress
- name: Verify if node is running ingress
  ansible.builtin.shell: "docker ps | grep k8s_nginx-ingress-controller | wc -l | awk '{print $1}'"
  register: _k8s_ingress_check
  when: _k8s_skip_ingress
  check_mode: false

- debug:
    msg: "Skipping server that is running ingress"
  when:
    - _k8s_skip_ingress
    - _k8s_ingress_check.stdout | int  > 0

- name: End the job when the host is running ingress
  meta: end_host
  when:
    - _k8s_skip_ingress
    - _k8s_ingress_check.stdout | int  > 0

# check max unavailable percentage
- name: Get unavailable servers
  ansible.builtin.shell: "kubectl get nodes | grep -E 'NotReady|SchedulingDisabled' | wc -l | awk '{print $1}'"
  delegate_to: "{{ sms_vars['kubernetes_master_hostname'] }}"
  register: _k8s_unavailable_servers
  check_mode: false

- name: Get total servers
  ansible.builtin.shell: "kubectl get nodes | wc -l | awk '{print $1}'"
  delegate_to: "{{ sms_vars['kubernetes_master_hostname'] }}"
  register: _k8s_total_servers
  check_mode: false

- debug:
    msg: "Skipping server as there are more unavailable servers than the maximum allowed"
  when:
    - _k8s_unavailable_servers.stdout | int / _k8s_total_servers.stdout | int * 100 | int > _k8s_max_unavailable_pct | int

- name: End the job if there are more unavailable servers than the maximum allowed
  meta: end_host
  when:
    - _k8s_unavailable_servers.stdout | int / _k8s_total_servers.stdout | int * 100 > _k8s_max_unavailable_pct | int
