# This will end the run on a node if a specific OS is installed on the node
#
# Usage:
# os_name: ubuntu (OPTIONAL: defaults to 'ubuntu')
# os_version: 16.04
---
- name: Sanity check
  assert:
    that:
      - sms_vars['os_version'] is defined
    fail_msg: "Variable 'os_version' is not defined!"
    quiet: true

- name: Re-fetch the ansible_distribution* info to make sure we are using latest information
  setup: filter='ansible_distribution*'

- debug:
    msg: "Skipping server as it's OS is {{ ansible_distribution }} {{ ansible_distribution_version }}"
  when:
    - sms_vars['os_name'] | default('ubuntu') | lower == ansible_distribution | lower
    - sms_vars['os_version'] | lower == ansible_distribution_version | lower
    
- name: Skip host if it runs the specific OS
  meta: end_host
  when:
    - sms_vars['os_name'] | default('ubuntu') | lower == ansible_distribution | lower
    - sms_vars['os_version'] | lower == ansible_distribution_version | lower
