# This task will check if the node can be patched. It checks the state of HDFS and if it's safe to proceed

# Usage:
# hadoop_hdfs_max_unavailable: 1 (OPTIONAL: defaults to 0, maximum number of dead datanodes allowed)
# hadoop_namenode_consul_service: hadoop-namenode2 (OPTIONAL: defaults to hadoop-namenode)
# hadoop_hdfs_user: hdfs (OPTIONAL: defaults to hdfs, user to run HDFS commands as)
---
# validate
- name: Validate hadoop_namenode_consul_service (optional)
  assert:
    that:
      - sms_vars['hadoop_namenode_consul_service'] | length > 0
    fail_msg: "Variable 'hadoop_namenode_consul_service' is defined but empty!"
    quiet: true
  when: sms_vars['hadoop_namenode_consul_service'] is defined

- name: Validate hadoop_hdfs_max_unavailable is a positive number
  assert:
    that:
      - (sms_vars['hadoop_hdfs_max_unavailable']) | int > 0
    fail_msg: "Value {{ sms_vars['hadoop_hdfs_max_unavailable'] }} is invalid. It must be a positive integer (> 0)"
    quiet: true
  when: sms_vars['hadoop_hdfs_max_unavailable'] is defined

- name: Validate hadoop_hdfs_user is a valid string
  assert:
    that:
      - (sms_vars['hadoop_hdfs_user']) | length > 0
    fail_msg: "Value {{ sms_vars['hadoop_hdfs_user'] }} is invalid. It must be a non-empty string"
    quiet: true
  when: sms_vars['hadoop_hdfs_user'] is defined

# set values
- name: Set default values
  set_fact:
    _hadoop_hdfs_exec: "source /etc/profile && /opt/hadoop/bin/hdfs"
    _hadoop_hdfs_max_unavailable: "{{ (sms_vars['hadoop_hdfs_max_unavailable']) | default(0) | int }}"
    _hadoop_hdfs_user: "{{ (sms_vars['hadoop_hdfs_user']) | default('hdfs') }}"
    _hadoop_nn_svc: "{{ sms_vars['hadoop_namenode_consul_service'] | default('hadoop-namenode') }}"

# get active namenode
- name: Get healthy {{ _hadoop_nn_svc }} instances from Consul
  uri:
    url: "http://localhost:8500/v1/health/service/{{ _hadoop_nn_svc }}?passing=true"
    method: GET
    status_code: 200
  register: _hadoop_consul_response
  retries: 5
  delay: 10
  until: _hadoop_consul_response is not failed
  check_mode: false

- name: Validate that at least one healthy namenode was found
  assert:
    that:
      - _hadoop_consul_response.json | length > 0
    fail_msg: "No healthy {{ _hadoop_nn_svc }} instances found in Consul"
    quiet: true

- name: Extract namenode
  set_fact:
    _hadoop_namenode_hostname: "{{ _hadoop_consul_response.json | first | json_query('Node.Node') }}"

# get HDFS report
- name: Get HDFS cluster status
  ansible.builtin.shell: "{{ _hadoop_hdfs_exec }} dfsadmin -report"
  delegate_to: "{{ _hadoop_namenode_hostname }}"
  args:
    executable: /bin/bash
  become: true
  become_user: "{{ _hadoop_hdfs_user }}"
  register: _hadoop_hdfs_status
  check_mode: false
  changed_when: false
  retries: 5
  delay: 10
  until: _hadoop_hdfs_status is not failed

- name: Check that command succeeded without errors
  assert:
    that:
      - _hadoop_hdfs_status.stderr == ""
    fail_msg: "Failed to get HDFS cluster status"
    quiet: true

- name: Parse HDFS cluster status
  set_fact:
    _hadoop_hdfs_dead_datanodes: "{{ _hadoop_hdfs_status.stdout | regex_search('Dead datanodes \\((\\d+)\\):') | regex_replace('Dead datanodes \\((\\d+)\\):', '\\1') }}"
    _hadoop_hdfs_decommissioning_datanodes: "{{ _hadoop_hdfs_status.stdout | regex_search('Decommissioning datanodes \\((\\d+)\\):') | regex_replace('Decommissioning datanodes \\((\\d+)\\):', '\\1') }}"
    _hadoop_hdfs_maintenance_datanodes: "{{ _hadoop_hdfs_status.stdout | regex_search('In maintenance datanodes \\((\\d+)\\):') | regex_replace('In maintenance datanodes \\((\\d+)\\):', '\\1') }}"
    _hadoop_hdfs_under_replicated_blocks: "{{ _hadoop_hdfs_status.stdout | regex_search('Under replicated blocks: \\d+') | regex_replace('Under replicated blocks: (\\d+)', '\\1') }}"
    _hadoop_hdfs_corrupt_replicas: "{{ _hadoop_hdfs_status.stdout | regex_search('Blocks with corrupt replicas: \\d+') | regex_replace('Blocks with corrupt replicas: (\\d+)', '\\1') }}"
    _hadoop_hdfs_missing_blocks: "{{ _hadoop_hdfs_status.stdout | regex_search('Missing blocks: \\d+') | regex_replace('Missing blocks: (\\d+)', '\\1') }}"

- name: Set defaults
  set_fact:
    _hadoop_hdfs_dead_datanodes: "{{ 0 if _hadoop_hdfs_dead_datanodes == 'None' else _hadoop_hdfs_dead_datanodes }}"
    _hadoop_hdfs_decommissioning_datanodes: "{{ 0 if _hadoop_hdfs_decommissioning_datanodes == 'None' else _hadoop_hdfs_decommissioning_datanodes }}"
    _hadoop_hdfs_maintenance_datanodes: "{{ 0 if _hadoop_hdfs_maintenance_datanodes == 'None' else _hadoop_hdfs_maintenance_datanodes }}"
    _hadoop_hdfs_under_replicated_blocks: "{{ 0 if _hadoop_hdfs_under_replicated_blocks == 'None' else _hadoop_hdfs_under_replicated_blocks }}"
    _hadoop_hdfs_corrupt_replicas: "{{ 0 if _hadoop_hdfs_corrupt_replicas == 'None' else _hadoop_hdfs_corrupt_replicas }}"
    _hadoop_hdfs_missing_blocks: "{{ 0 if _hadoop_hdfs_missing_blocks == 'None' else _hadoop_hdfs_missing_blocks }}"

# check unavailable nodes
- name: Check if too many datanodes are already unavailable
  debug:
    msg: "Too many datanodes are already unavailable ({{ _hadoop_hdfs_dead_datanodes }} dead, {{ _hadoop_hdfs_decommissioning_datanodes }} decommissioning, {{ _hadoop_hdfs_maintenance_datanodes }} in maintenance; max allowed down: {{ _hadoop_hdfs_max_unavailable }}) - skipping node"
  when:
    - (_hadoop_hdfs_dead_datanodes | int + _hadoop_hdfs_decommissioning_datanodes | int) > _hadoop_hdfs_max_unavailable | int

- name: End the job if too many datanodes are unavailable
  meta: end_host
  when:
    - (_hadoop_hdfs_dead_datanodes | int + _hadoop_hdfs_decommissioning_datanodes | int) > _hadoop_hdfs_max_unavailable | int

# check under replicated blocks
- name: Check for under-replicated blocks
  debug:
    msg: "HDFS has under-replicated blocks ({{ _hadoop_hdfs_under_replicated_blocks }}) - skipping node"
  when:
    - _hadoop_hdfs_under_replicated_blocks | int > 0

- name: End the job if HDFS has under-replicated blocks
  meta: end_host
  when:
    - _hadoop_hdfs_under_replicated_blocks | int > 0

# check for corrupt replicas
- name: Check for corrupt replicas
  debug:
    msg: "HDFS has corrupt replicas ({{ _hadoop_hdfs_corrupt_replicas }}) - skipping node"
  when:
    - _hadoop_hdfs_corrupt_replicas | int > 100

- name: End the job if HDFS has corrupt replicas
  meta: end_host
  when:
    - _hadoop_hdfs_corrupt_replicas | int > 100

# check for missing blocks
- name: Check for missing blocks
  debug:
    msg: "HDFS has missing blocks ({{ _hadoop_hdfs_missing_blocks }}) - skipping node"
  when:
    - _hadoop_hdfs_missing_blocks | int > 0

- name: End the job if HDFS has missing blocks
  meta: end_host
  when:
    - _hadoop_hdfs_missing_blocks | int > 0

# check if in safe mode
- name: Check HDFS safe mode status
  ansible.builtin.shell: "{{ _hadoop_hdfs_exec }} dfsadmin -safemode get"
  delegate_to: "{{ _hadoop_namenode_hostname }}"
  args:
    executable: /bin/bash
  become: true
  become_user: "{{ _hadoop_hdfs_user }}"
  register: _hadoop_hdfs_safemode
  check_mode: false
  changed_when: false
  retries: 5
  delay: 10
  until: _hadoop_hdfs_safemode is not failed

- name: Check if HDFS is in safe mode
  debug:
    msg: "HDFS is in safe mode - skipping node"
  when: "'ON' in _hadoop_hdfs_safemode.stdout"

- name: End the job if HDFS is in safe mode
  meta: end_host
  when: "'ON' in _hadoop_hdfs_safemode.stdout"
