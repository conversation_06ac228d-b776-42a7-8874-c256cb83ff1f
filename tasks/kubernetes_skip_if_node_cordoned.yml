# DEPRECATED: check kubernetes_check_pre
#
# This will skip the node if the node is cordoned
#
# Usage:
# kubernetes_master_hostname: kube3master.nydc1.outbrain.com
---
- name: Variables check
  assert:
    that:
      - sms_vars['kubernetes_master_hostname'] is defined
    fail_msg: "Variable 'kubernetes_master_hostname' must be defined!"

- name: Check nodes scheduling status
  ansible.builtin.shell: "kubectl get node {{ inventory_hostname }} -o json"
  delegate_to: "{{ sms_vars['kubernetes_master_hostname'] }}"
  register: _kubernetes_skip_if_node_cordoned_unschedulable

- debug:
    msg: "Skipping server as its cordoned"
  when: 
    - not ansible_check_mode
    - _kubernetes_skip_if_node_cordoned_unschedulable.stdout | from_json | json_query('spec.unschedulable') | lower == 'true'


- name: End the job when the host is cordoned
  meta: end_host
  when:
    - not ansible_check_mode
    - _kubernetes_skip_if_node_cordoned_unschedulable.stdout | from_json | json_query('spec.unschedulable') | lower == 'true'
