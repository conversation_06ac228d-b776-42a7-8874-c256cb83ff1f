# Performs uncordon operation of a kubernetes node
#
# Usage:
# kubernetes_master_hostname: kube3master.nydc1.outbrain.com
---
- name: Sanity check
  assert:
    that:
      - sms_vars['kubernetes_master_hostname'] is defined
    fail_msg: "Variable 'kubernetes_master_hostname' must be defined!"
    quiet: true

- name: Send Uncordon Event
  include_tasks: ../tasks/internal/raven_event.yml
  vars:
    raven_fields:
      node_name: "{{ inventory_hostname }}"
      action: "UNCORDON"
      username: "awx"
      service: "awx-dog"
      obmessage: "SMS PATCH"
      kubeCluster: "{{ _consul_service_name.split('-')[0] }}"
    raven_message: "SMS PATCH"

- name: Uncordon {{ inventory_hostname }}
  ansible.builtin.shell: "kubectl uncordon {{ inventory_hostname }}"
  delegate_to: "{{ sms_vars['kubernetes_master_hostname'] }}"
