# Rebuild the server this task runs on using ngptool. The --names <servername> will be always added to the options
#
# Usage:
# ngptool_server: ops-10001-prod-nydc1.nydc1.outbrain.com (OPTIONAL: defaults to 'ops-10001-prod-nydc1.nydc1.outbrain.com')
# ngptool_options: '--os-name ubuntu20 --role OB-kube3' (OPTIONAL: defaults to '')
# ngptool_silence_owner: 'delivery' (OPTIONAL: defaults to '')
# ngptool_rebuild_timeout: 7200 (OPTIONAL: defaults to 5400)
# ngptool_new_hostname: kubeworkera4 (OPTIONAL: defaults to '')
# ngptool_new_hostname_env: test (OPTIONAL: defaults to 'prod')
# ngptool_chef_version: 18.1.0-1 (OPTIONAL: defaults to '18.1.0-1')

---
- name: Silence alerts for {{ inventory_hostname }}
  shell: "/outbrain/prometheus/tools/silence_host_alerts 1 {{ inventory_hostname }} {{ sms_vars['ngptool_silence_owner'] | default('') }}"

- name: Extract machine ID from {{ inventory_hostname }}
  set_fact:
    _ngptool_new_name_args: "-x -t {{ sms_vars['ngptool_new_hostname'] }} -i {{ inventory_hostname | regex_search('-[0-9]{2}([^-\\n]+)', '\\1') | first | int }} -e {{ sms_vars['ngptool_new_hostname_env'] | default('prod') }}"
  when: sms_vars['ngptool_new_hostname'] is defined

- name: log the new args
  debug:
    msg: "going to append: {{ _ngptool_new_name_args }} to the rebuild command"
  when: sms_vars['ngptool_new_hostname'] is defined

- name: setting chef version {{ inventory_hostname }}
  set_fact:
    _ngptool_chef_version_args: "-c {{ sms_vars['ngptool_chef_version'] | default('18.1.0-1') }}"
  when: sms_vars['ngptool_options'] is not defined or '-c' not in sms_vars['ngptool_options']

- name: Rebuild {{ inventory_hostname }}
  shell: "echo Y | /outbrain/ops/scripts/git/provisioning-tools/ngptool.rb rebuild --name {{ inventory_hostname }} {{ sms_vars['ngptool_options'] | default('') }} {{ _ngptool_new_name_args | default('') }} {{ _ngptool_chef_version_args | default('')}}"
  delegate_to: "{{ sms_vars['ngptool_server'] | default('ops-10001-prod-nydc1.nydc1.outbrain.com') }}"
  become: yes
  become_user: "{{ sms_user }}"
  register: _ngptool_output

- name: ngptool response
  debug:
    msg: "{{ _ngptool_output.stdout }}"
  when: not sms_check_mode

- name: Set the new hostname for {{ inventory_hostname }}
  set_fact:
    _ngptool_new_hostname: "{{ sms_vars['ngptool_new_hostname'] }}-{{ inventory_hostname | regex_replace('^[^-]*-', '') }}"
  when: sms_vars['ngptool_new_hostname'] is defined

- name: Update ansible_host to the new hostname
  set_fact:
    ansible_host: "{{ _ngptool_new_hostname }}"
    inventory_hostname: "{{ _ngptool_new_hostname }}"
    ansible_ssh_host: "{{ _ngptool_new_hostname }}"
  when: sms_vars['ngptool_new_hostname'] is defined

- name: Refresh connection to use new hostname
  meta: reset_connection

- name: Wait for server to be reinstalled
  wait_for_connection:
    delay: 1800
    sleep: 60
    timeout: "{{ sms_vars['ngptool_rebuild_timeout'] | default(5400) }}"
  delegate_to: "{{ inventory_hostname }}"

