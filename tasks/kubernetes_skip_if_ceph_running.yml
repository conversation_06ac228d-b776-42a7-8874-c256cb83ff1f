# DEPRECATED: check kuberne<PERSON>_check_pre
#
# This will end the run on a node if ceph block storage is running on it
---
- name: Check if ceph is running (excude the csi-rbdplugin daemonset containers)
  ansible.builtin.shell: "docker ps | grep _rook-ceph-block_ | grep -v csi-rbdplugin | grep -v '/pause' | wc -l | awk '{print $1}'"
  register: _number_of_ceph

- debug:
    msg: "Skipping server as ceph is running on the server"
  when: _number_of_ceph.stdout | int  > 0

- name: End the job if ceph is running on the node
  meta: end_host
  when: _number_of_ceph.stdout | int  > 0
