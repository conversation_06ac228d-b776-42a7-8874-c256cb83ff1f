# Usage:
# consul_service_name: kafka
# consul_service_node_name: kafka1-10001-prod-nydc1.nydc1.outbrain.com (OPTIONAL: defaults to inventory hostname)
# consul_service_retries: 60 (OPTIONAL: defaults to 30)
# consul_service_delay: 30 (OPTIONAL: defaults to 10)
---
- set_fact:
    _consul_service_node_name: "{{ sms_vars['consul_service_node_name'] | default(ansible_hostname) }}"
  when: inventory_hostname.endswith('zemanta.com')

- set_fact:
    _consul_service_node_name: "{{ sms_vars['consul_service_node_name'] | default(inventory_hostname) }}"
  when: inventory_hostname.endswith('outbrain.com')

- name: Validate variable 'consul_service_name'
  assert:
    that:
      - sms_vars['consul_service_name'] is defined
    fail_msg: "Variable 'consul_service_name' is not defined!"
    quiet: true

- set_fact:
    _consul_service_name: "{{ sms_vars['consul_service_name'] }}"
    _consul_service_retries : "{{ (sms_vars['consul_service_retries'] | default(30)) }}"
    _consul_service_delay: "{{ sms_vars['consul_service_delay'] | default(10) }}"

- block:
    - name: Get {{ _consul_service_name }} service consul data as JSON
      uri:
        url: "http://localhost:8500/v1/health/node/{{ _consul_service_node_name }}"
        method: "GET"
      retries: 5
      delay: 10
      until: _consul_response is not failed
      register: _consul_response
      check_mode: false

    - name: Extract {{ _consul_service_name }} service consul health status from JSON
      set_fact:
        _consul_service_status: "{{ _consul_response.json | selectattr('ServiceName', 'equalto', _consul_service_name) | map(attribute='Status') | list }}"

    - name: Validate consul response
      assert:
        that:
          - _consul_service_status | length == 1
        fail_msg: "Invalid consul response, {{ _consul_service_status | length }} elements in the response"
        quiet: true

    - name: "Validate {{ _consul_service_name }} service consul health status"
      assert:
        that: "_consul_service_status | first == 'passing'"
        fail_msg: "Service {{ _consul_service_name }} is unhealthy, consul status is {{ _consul_service_status | first }}"
        quiet: true

  rescue:
    - name: Set the retry count
      set_fact:
        _consul_service_retry_count: "{{ 0 if _consul_service_retry_count is undefined else _consul_service_retry_count | int + 1 }}"

    - fail:
        msg: "Service {{ _consul_service_name }} is unhealthy, consul status is {{ _consul_service_status | first | default('UNKNOWN') }}."
      when: _consul_service_retry_count | int >= _consul_service_retries | int

    - debug:
        msg: "Retrying in {{ _consul_service_delay | int + 15 * _consul_service_retry_count | int }} seconds."

    - wait_for:
        timeout: "{{ _consul_service_delay | int + 15 * _consul_service_retry_count | int }}"

    - include_tasks: consul_check_service.yml
