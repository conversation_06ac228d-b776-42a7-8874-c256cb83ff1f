# This task will bring back the node after patching by re-enabling all the services and adding the node back to the 
# cluster

# Usage:
# hadoop_namenode_consul_service: hadoop-namenode2 (OPTIONAL: defaults to hadoop-namenode)
---
# validate
- name: Validate hadoop_namenode_consul_service (optional)
  assert:
    that:
      - sms_vars['hadoop_namenode_consul_service'] | length > 0
    fail_msg: "Variable 'hadoop_namenode_consul_service' is defined but empty!"
    quiet: true
  when: sms_vars['hadoop_namenode_consul_service'] is defined

# set values
- name: Set default values
  set_fact:
    _hadoop_nn_svc: "{{ sms_vars['hadoop_namenode_consul_service'] | default('hadoop-namenode') }}"

# get active namenode
- name: Get healthy {{ _hadoop_nn_svc }} instances from Consul
  uri:
    url: "http://localhost:8500/v1/health/service/{{ _hadoop_nn_svc }}?passing=true"
    method: GET
    status_code: 200
  register: _hadoop_consul_response
  retries: 5
  delay: 10
  until: _hadoop_consul_response is not failed
  check_mode: false

- name: Validate that at least one healthy namenode was found
  assert:
    that:
      - _hadoop_consul_response.json | length > 0
    fail_msg: "No healthy {{ _hadoop_nn_svc }} instances found in Consul"
    quiet: true

- name: Extract namenode
  set_fact:
    _hadoop_namenode_hostname: "{{ _hadoop_consul_response.json | first | json_query('Node.Node') }}"

# create directory /var/run/hadoop-hdfs with user hdfs and group hadoop, permissions 0755
- name: Make sure directory /var/run/hadoop-hdfs exists
  file:
    path: /var/run/hadoop-hdfs
    owner: hdfs
    group: hadoop
    mode: '0755'
    state: directory

# start hadoop services
- name: Start hadoop services
  service:
    name: "{{ item }}"
    state: "started"
  loop:
    - yarn-nodemanager
    - hadoop-datanode

# check that Hadoop services are ready
- name: Wait for DataNode JMX endpoint to be available
  uri:
    url: "http://localhost:50075/jmx"
    method: HEAD
    status_code: 200
  register: _hadoop_datanode_health_check
  until: _hadoop_datanode_health_check is succeeded
  retries: 120
  delay: 10

- name: Wait for YARN NodeManager JMX endpoint to be available
  uri:
    url: "http://localhost:8042/jmx?qry=Hadoop:*"
    method: HEAD
    status_code: 200
  register: nodemanager_health_check
  until: nodemanager_health_check is succeeded
  retries: 120
  delay: 10
  
- name: Wait for DataNode to appear in HDFS live datanodes report
  ansible.builtin.shell: "HADOOP_USER_NAME=hdfs JAVA_HOME=/usr/lib/jvm/default-java hdfs dfsadmin -report -live"
  args:
    executable: /bin/bash
  environment:
    PATH: "{{ ansible_env.PATH }}:/opt/hadoop/bin"
  register: _hadoop_hdfs_live_report
  until: inventory_hostname in _hadoop_hdfs_live_report.stdout
  retries: 120
  delay: 10
  async: 60
  poll: 10

# bring the node back to the cluster
- name: Silence alerts
  shell: "/outbrain/prometheus/tools/silence_host_alerts -r"

- name: Set yarn into maintenance mode
  shell: "/outbrain/ops/scripts/apache-hadoop-set-nodemanager-state.py --hosts {{ inventory_hostname }} --state NORMAL"
  delegate_to: "{{ _hadoop_namenode_hostname }}"

- name: Refresh yarn state
  shell: "yarn rmadmin -refreshNodes"
  environment:
    PATH: "{{ ansible_env.PATH }}:/opt/hadoop/bin"
    HADOOP_USER_NAME: "hdfs"
    JAVA_HOME: "/usr/lib/jvm/default-java"
  delegate_to: "{{ _hadoop_namenode_hostname }}"
  become: true
  become_user: "yarn"

- name: Set datanode into maintenance mode
  shell: "/outbrain/ops/scripts/apache-hadoop-set-datanode-state.py --hosts {{ inventory_hostname }} --state NORMAL"
  delegate_to: "{{ _hadoop_namenode_hostname }}"

- name: Refresh hdfs state
  shell: "hdfs dfsadmin -refreshNodes"
  environment:
    PATH: "{{ ansible_env.PATH }}:/opt/hadoop/bin"
    HADOOP_USER_NAME: "hdfs"
    JAVA_HOME: "/usr/lib/jvm/default-java"
  delegate_to: "{{ _hadoop_namenode_hostname }}"
  become: true
  become_user: "hdfs"

- name: Set no chef run
  shell: "manage_no_chef_run.sh remove -u sms"
