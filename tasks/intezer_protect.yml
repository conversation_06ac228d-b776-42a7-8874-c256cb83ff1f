# Install/upgrade Intezer Protect on barebone linux node
# more on deployment: https://support.intezer.com/hc/en-us/articles/360013645799-Sensor-Installation-Manual-Installation-Advanced-
#
# We are skipping k8s nodes, because we are deploying using intezer in pod through deamonset
#
# TODO: this will be rewritten to https://bitbucket.outbrain.com/projects/ZINF/repos/ansible-role-base/browse/tasks/intezer.yml
---
- name: Validate that variables are defined
  assert:
    that:
      - protect_sensor_license_key is defined
    # - protect_proxy_url  # optional
    # - protect_proxy_user # optional
    # - protect_proxy_password # optional
    fail_msg: "Variable protect_sensor_license_key'' must be defined!"
    quiet: true

- name: 'Verify that node is not part of k8s cluster - check one'
  shell: |
    dpkg -l|grep kubelet
  register: protect_kubelet_dpkg_status
  failed_when: protect_kubelet_dpkg_status.rc != 1 
  when:
   - ansible_distribution == 'Ubuntu'

- name: 'Verify that node is not part of k8s cluster - check two'
  shell: |
    netstat -pluton|grep -iE 'kubelet'
  register: protect_kubelet_listen_status
  failed_when: protect_kubelet_listen_status.rc != 1 
  when:
    - ansible_distribution == 'Ubuntu'

- name: Validate that node is not part of k8s cluster
  assert:
    that:
      - protect_kubelet_dpkg_status.rc == 1
      - protect_kubelet_listen_status.rc == 1 
    fail_msg: "Node is part of k8s cluster and Intezer Protect needs to be installed as daemonset"
    quiet: true

###
## DEFAULT INSTALL (good enough for quick and dirty install | Ubuntu 14.04)
- name: 'Install sensor (in case of Ubuntu 14.04)'
  shell: "wget -O - https://protect.intezer.com/v2/install?key={{protect_sensor_license_key}} | sh"
  when: 
    - protect_proxy_url is undefined
    - ansible_distribution == 'Ubuntu' and ansible_distribution_version == "14.04"
  args:
    warn: false

- name: 'Install sensor with proxy (in case of Ubuntu 14.04)'
  shell: 'https_proxy={{protect_proxy_url}} wget -O - https://protect.intezer.com/v2/install?key={{protect_sensor_license_key}} | sh -s "{{protect_proxy_url}}"'
  when: 
    - protect_proxy_url is defined and protect_proxy_user is undefined
    - ansible_distribution == 'Ubuntu' and ansible_distribution_version == "14.04"
  args:
    warn: false

- name: 'Install sensor with proxy basic auth (in case of Ubuntu 14.04)'
  shell: 'https_proxy={{protect_proxy_url}} wget --proxy-user={{protect_proxy_user}} --proxy-passwd={{protect_proxy_password}} -O - https://protect.intezer.com/v2/install?key={{protect_sensor_license_key}} | sh -s "{{protect_proxy_url}}" "{{protect_proxy_user}}" "{{protect_proxy_password}}"'
  when: 
    - protect_proxy_url is defined and protect_proxy_user is defined
    - ansible_distribution == 'Ubuntu' and ansible_distribution_version == "14.04"
  args:
    warn: false
###
###

- name: GET sha256sum of .deb package
  uri:
    url: "https://protect-sensor-public.s3.amazonaws.com/intezer-protect.amd64.deb.sha256"
    method: "GET"
    return_content: yes
  retries: 5
  delay: 3
  until: protect_deb_sha256sum is not failed
  register: protect_deb_sha256sum
  when: 
    - protect_proxy_url is undefined
    - ansible_distribution == 'Ubuntu' and ansible_distribution_version != "14.04"

# FIXME: TO BE REMOVED / COMMENT OUT
#- name: Debug
#  debug:
#   msg: "{{ protect_deb_sha256sum.content | regex_replace('(\\n)','') }}"

- name: Get URI for Intezer .deb package
  uri:
    url: "https://protect.intezer.com/v2/install/pkg?key={{protect_sensor_license_key}}&pkg_type=deb"
    method: "GET"
    return_content: yes
  retries: 5
  delay: 3
  until: protect_deb_uri is not failed
  register: protect_deb_uri
  when: 
    - protect_proxy_url is undefined
    - ansible_distribution == 'Ubuntu' and ansible_distribution_version != "14.04"

#FIXME: TO BE REMOVED / COMMNET OUT
#- name: Debug
#  debug:
#   msg: "{{ protect_deb_uri.content }}"

- name: Download Intezer .deb package
  get_url:
    url: "{{protect_deb_uri.content}}"
    dest: /tmp/intezer-protect.amd64.deb
    checksum: "sha256:{{protect_deb_sha256sum.content|regex_replace('(\\n)','')}}"
  when:
    - protect_proxy_url is undefined
    - ansible_distribution == 'Ubuntu' and ansible_distribution_version != "14.04"

- name: Create '/etc/intezer' if not there yet
  file:
   path: /etc/intezer
   state: directory
   owner: root
   group: root
   mode: 'u=rwx,g=rx,o=rx'
  when:
    - ansible_distribution == 'Ubuntu' and ansible_distribution_version != "14.04"

- name: Prepare sensor configuration
  get_url:
    url: https://protect.intezer.com/v2/install/config.yml?key={{protect_sensor_license_key}}
    dest: /etc/intezer/config.yml
    mode: 600
  when:
    - protect_proxy_url is undefined
    - ansible_distribution == 'Ubuntu' and ansible_distribution_version != "14.04"
    
 ## TODO: Apply custom configuration (Optional): At this step, you may apply 
 ## custom configuration such as proxy_address. Read more about sensor configuration.

- name: Install Protect sensor .deb file
  shell: |
    dpkg -i  /tmp/intezer-protect.amd64.deb
  when:
    - ansible_distribution == 'Ubuntu' and ansible_distribution_version != "14.04"
    
- name: Remove '/tmp/intezer-protect.amd64.deb'
  file:
    state: absent
    path: /tmp/intezer-protect.amd64.deb
  when:
    - ansible_distribution == 'Ubuntu' and ansible_distribution_version != "14.04"
    
- name: Enable service Intezer Protect
  ansible.builtin.systemd:
    name: intezer-protect
    enabled: yes
    masked: no
  when:
    - ansible_distribution == 'Ubuntu' and ansible_distribution_version != "14.04"

- name: Restart service Intezer Protect
  ansible.builtin.systemd:
    state: restarted
    daemon_reload: yes
    name: intezer-protect
  when:
    - ansible_distribution == 'Ubuntu' and ansible_distribution_version != "14.04"
    