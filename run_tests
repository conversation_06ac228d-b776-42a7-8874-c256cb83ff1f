#!/usr/bin/env python3
# Copyright 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Wrapper to run linters and pytest with the right settings."""

import os
import subprocess
import sys

import pytest


ROOT_DIR = os.path.dirname(os.path.realpath(__file__))


def run_black():
    """Returns the exit code from black."""
    # Black by default only matches .py files.  We have to list standalone
    # scripts manually.
    extra_programs = [
        "repo",
        "run_tests",
        "release/update-hooks",
        "release/update-manpages",
    ]
    return subprocess.run(
        [sys.executable, "-m", "black", "--check", ROOT_DIR] + extra_programs,
        check=False,
    ).returncode


def run_flake8():
    """Returns the exit code from flake8."""
    return subprocess.run(
        [sys.executable, "-m", "flake8", ROOT_DIR], check=False
    ).returncode


def run_isort():
    """Returns the exit code from isort."""
    return subprocess.run(
        [sys.executable, "-m", "isort", "--check", ROOT_DIR], check=False
    ).returncode


def main(argv):
    """The main entry."""
    checks = (
        lambda: pytest.main(argv),
        run_black,
        run_flake8,
        run_isort,
    )
    return 0 if all(not c() for c in checks) else 1


if __name__ == "__main__":
    sys.exit(main(sys.argv[1:]))
