# GitHub actions workflow.
# https://docs.github.com/en/actions/learn-github-actions/workflow-syntax-for-github-actions

# https://github.com/superbrothers/close-pull-request
name: Close Pull Request

on:
  pull_request_target:
    types: [opened]

jobs:
  run:
    runs-on: ubuntu-latest
    steps:
    - uses: superbrothers/close-pull-request@v3
      with:
        comment: >
          Thanks for your contribution!
          Unfortunately, we don't use GitHub pull requests to manage code
          contributions to this repository.
          Instead, please see [README.md](../blob/HEAD/SUBMITTING_PATCHES.md)
          which provides full instructions on how to get involved.
