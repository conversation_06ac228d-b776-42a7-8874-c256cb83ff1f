# GitHub actions workflow.
# https://help.github.com/en/actions/automating-your-workflow-with-github-actions/workflow-syntax-for-github-actions

name: Test CI

on:
  push:
    branches: [main, repo-1, stable, maint]
    tags: [v*]

jobs:
  test:
    strategy:
      fail-fast: false
      matrix:
        # ubuntu-20.04 is the last version that supports python 3.6
        os: [ubuntu-20.04, macos-latest, windows-latest]
        python-version: ['3.6', '3.7', '3.8', '3.9', '3.10', '3.11', '3.12']
    runs-on: ${{ matrix.os }}

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        python -m pip install tox tox-gh-actions
    - name: Test with tox
      run: tox
