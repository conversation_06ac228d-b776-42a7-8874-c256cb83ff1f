# GitHub actions workflow.
# https://help.github.com/en/actions/automating-your-workflow-with-github-actions/workflow-syntax-for-github-actions
# https://github.com/marketplace/actions/python-flake8

name: Flake8

on:
  push:
    branches: [main]

jobs:
  lint:
    name: Python Lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: "3.9"
      - name: Run flake8
        uses: julianwachholz/flake8-action@v2
        with:
          checkName: "Python Lint"
