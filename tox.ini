# Copyright 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# https://tox.readthedocs.io/

[tox]
envlist = lint, py36, py37, py38, py39, py310, py311, py312
requires = virtualenv<20.22.0

[gh-actions]
python =
    3.6: py36
    3.7: py37
    3.8: py38
    3.9: py39
    3.10: py310
    3.11: py311
    3.12: py312

[testenv]
deps =
    -c constraints.txt
    black
    flake8
    isort
    pytest
    pytest-timeout
commands = {envpython} run_tests {posargs}
setenv =
    GIT_AUTHOR_NAME = Repo test author
    GIT_COMMITTER_NAME = Repo test committer
    EMAIL = <EMAIL>

[testenv:lint]
skip_install = true
deps =
    -c constraints.txt
    black
    flake8
commands =
    black --check {posargs:. repo run_tests release/update-hooks release/update-manpages}
    flake8

[testenv:format]
skip_install = true
deps =
    -c constraints.txt
    black
    flake8
commands =
    black {posargs:. repo run_tests release/update-hooks release/update-manpages}
    flake8
