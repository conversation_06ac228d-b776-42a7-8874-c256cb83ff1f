ansible==11.5.0
molecule==25.4.0
paramiko>=3.4.0 # not directly required, pinned by Snyk to avoid a vulnerability
requests>=2.32.2 # not directly required, pinned by Snyk to avoid a vulnerability
urllib3>=2.2.2 # not directly required, pinned by Snyk to avoid a vulnerability
zipp>=3.19.1 # not directly required, pinned by Snyk to avoid a vulnerability

# azure.azcollection
packaging
requests[security]
xmltodict
msgraph-sdk==1.6.0
azure-cli-core==2.64.0
azure-common==1.1.28
azure-identity==1.19.0
azure-mgmt-authorization==4.0.0
azure-mgmt-apimanagement==4.0.1
azure-mgmt-batch==17.3.0
azure-mgmt-compute==33.0.0
azure-mgmt-cdn==13.1.1
azure-mgmt-containerinstance==10.1.0
azure-mgmt-core==1.4.0
azure-mgmt-containerregistry==10.3.0
azure-containerregistry==1.2.0
azure-mgmt-containerservice==32.1.0
azure-mgmt-datafactory==9.0.0
azure-mgmt-dns==8.1.0
azure-mgmt-marketplaceordering==1.2.0b2
azure-mgmt-monitor==6.0.2
azure-mgmt-managedservices==7.0.0b2
azure-mgmt-managementgroups==1.1.0b2
azure-mgmt-network==28.0.0
azure-mgmt-nspkg==3.0.2
azure-mgmt-privatedns==1.1.0
azure-mgmt-redis==14.4.0
azure-mgmt-resource==23.2.0
azure-mgmt-rdbms==10.2.0b17
azure-mgmt-mysqlflexibleservers==1.0.0b3
azure-mgmt-postgresqlflexibleservers==1.1.0
azure-mgmt-search==9.2.0b2
azure-mgmt-servicebus==8.2.1
azure-mgmt-sql==4.0.0b19
azure-mgmt-storage==21.2.1
azure-mgmt-trafficmanager==1.1.0
azure-mgmt-web==7.3.1
azure-nspkg==3.0.2
azure-storage-blob==12.23.0b1
azure-core==1.31.0
azure-keyvault==4.2.0
azure-mgmt-keyvault==10.3.1
azure-mgmt-cosmosdb==10.0.0b3
azure-mgmt-hdinsight==9.1.0b1
azure-mgmt-devtestlabs==10.0.0b2
azure-mgmt-loganalytics==13.0.0b7
azure-mgmt-automation==1.1.0b4
azure-mgmt-iothub==3.0.0
azure-iot-hub==2.6.1;platform_machine=="x86_64"
azure-mgmt-recoveryservices==3.0.0
azure-mgmt-recoveryservicesbackup==9.1.0
azure-mgmt-notificationhubs==8.1.0b1
azure-mgmt-eventhub==11.1.0
azure-mgmt-resourcehealth==1.0.0b6
oras
netaddr
