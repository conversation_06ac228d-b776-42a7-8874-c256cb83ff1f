.\" DO NOT MODIFY THIS FILE!  It was generated by help2man.
.TH REPO "1" "July 2022" "repo list" "Repo Manual"
.SH NAME
repo \- repo list - manual page for repo list
.SH SYNOPSIS
.B repo
\fI\,list \/\fR[\fI\,-f\/\fR] [\fI\,<project>\/\fR...]
.SH DESCRIPTION
Summary
.PP
List projects and their associated directories
.PP
repo list [\-f] \fB\-r\fR str1 [str2]...
.SH OPTIONS
.TP
\fB\-h\fR, \fB\-\-help\fR
show this help message and exit
.TP
\fB\-r\fR, \fB\-\-regex\fR
filter the project list based on regex or wildcard
matching of strings
.TP
\fB\-g\fR GROUPS, \fB\-\-groups\fR=\fI\,GROUPS\/\fR
filter the project list based on the groups the
project is in
.TP
\fB\-a\fR, \fB\-\-all\fR
show projects regardless of checkout state
.TP
\fB\-n\fR, \fB\-\-name\-only\fR
display only the name of the repository
.TP
\fB\-p\fR, \fB\-\-path\-only\fR
display only the path of the repository
.TP
\fB\-f\fR, \fB\-\-fullpath\fR
display the full work tree path instead of the
relative path
.TP
\fB\-\-relative\-to\fR=\fI\,PATH\/\fR
display paths relative to this one (default: top of
repo client checkout)
.SS Logging options:
.TP
\fB\-v\fR, \fB\-\-verbose\fR
show all output
.TP
\fB\-q\fR, \fB\-\-quiet\fR
only show errors
.SS Multi\-manifest options:
.TP
\fB\-\-outer\-manifest\fR
operate starting at the outermost manifest
.TP
\fB\-\-no\-outer\-manifest\fR
do not operate on outer manifests
.TP
\fB\-\-this\-manifest\-only\fR
only operate on this (sub)manifest
.TP
\fB\-\-no\-this\-manifest\-only\fR, \fB\-\-all\-manifests\fR
operate on this manifest and its submanifests
.PP
Run `repo help list` to view the detailed manual.
.SH DETAILS
.PP
List all projects; pass '.' to list the project for the cwd.
.PP
By default, only projects that currently exist in the checkout are shown. If you
want to list all projects (using the specified filter settings), use the \fB\-\-all\fR
option. If you want to show all projects regardless of the manifest groups, then
also pass \fB\-\-groups\fR all.
.PP
This is similar to running: repo forall \fB\-c\fR 'echo "$REPO_PATH : $REPO_PROJECT"'.
