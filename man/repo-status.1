.\" DO NOT MODIFY THIS FILE!  It was generated by help2man.
.TH REPO "1" "July 2022" "repo status" "Repo Manual"
.SH NAME
repo \- repo status - manual page for repo status
.SH SYNOPSIS
.B repo
\fI\,status \/\fR[\fI\,<project>\/\fR...]
.SH DESCRIPTION
Summary
.PP
Show the working tree status
.SH OPTIONS
.TP
\fB\-h\fR, \fB\-\-help\fR
show this help message and exit
.TP
\fB\-j\fR JOBS, \fB\-\-jobs\fR=\fI\,JOBS\/\fR
number of jobs to run in parallel (default: based on
number of CPU cores)
.TP
\fB\-o\fR, \fB\-\-orphans\fR
include objects in working directory outside of repo
projects
.SS Logging options:
.TP
\fB\-v\fR, \fB\-\-verbose\fR
show all output
.TP
\fB\-q\fR, \fB\-\-quiet\fR
only show errors
.SS Multi\-manifest options:
.TP
\fB\-\-outer\-manifest\fR
operate starting at the outermost manifest
.TP
\fB\-\-no\-outer\-manifest\fR
do not operate on outer manifests
.TP
\fB\-\-this\-manifest\-only\fR
only operate on this (sub)manifest
.TP
\fB\-\-no\-this\-manifest\-only\fR, \fB\-\-all\-manifests\fR
operate on this manifest and its submanifests
.PP
Run `repo help status` to view the detailed manual.
.SH DETAILS
.PP
\&'repo status' compares the working tree to the staging area (aka index), and the
most recent commit on this branch (HEAD), in each project specified. A summary
is displayed, one line per file where there is a difference between these three
states.
.PP
The \fB\-j\fR/\-\-jobs option can be used to run multiple status queries in parallel.
.PP
The \fB\-o\fR/\-\-orphans option can be used to show objects that are in the working
directory, but not associated with a repo project. This includes unmanaged
top\-level files and directories, but also includes deeper items. For example, if
dir/subdir/proj1 and dir/subdir/proj2 are repo projects, dir/subdir/proj3 will
be shown if it is not known to repo.
.PP
Status Display
.PP
The status display is organized into three columns of information, for example
if the file 'subcmds/status.py' is modified in the project 'repo' on branch
\&'devwork':
.TP
project repo/
branch devwork
.TP
\fB\-m\fR
subcmds/status.py
.PP
The first column explains how the staging area (index) differs from the last
commit (HEAD). Its values are always displayed in upper case and have the
following meanings:
.TP
\-:
no difference
.TP
A:
added         (not in HEAD,     in index                     )
.TP
M:
modified      (    in HEAD,     in index, different content  )
.TP
D:
deleted       (    in HEAD, not in index                     )
.TP
R:
renamed       (not in HEAD,     in index, path changed       )
.TP
C:
copied        (not in HEAD,     in index, copied from another)
.TP
T:
mode changed  (    in HEAD,     in index, same content       )
.TP
U:
unmerged; conflict resolution required
.PP
The second column explains how the working directory differs from the index. Its
values are always displayed in lower case and have the following meanings:
.TP
\-:
new / unknown (not in index,     in work tree                )
.TP
m:
modified      (    in index,     in work tree, modified      )
.TP
d:
deleted       (    in index, not in work tree                )
