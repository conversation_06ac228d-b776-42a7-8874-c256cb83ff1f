.\" DO NOT MODIFY THIS FILE!  It was generated by help2man.
.TH REPO "1" "July 2022" "repo grep" "Repo Manual"
.SH NAME
repo \- repo grep - manual page for repo grep
.SH SYNOPSIS
.B repo
\fI\,grep {pattern | -e pattern} \/\fR[\fI\,<project>\/\fR...]
.SH DESCRIPTION
Summary
.PP
Print lines matching a pattern
.SH OPTIONS
.TP
\fB\-h\fR, \fB\-\-help\fR
show this help message and exit
.TP
\fB\-j\fR JOBS, \fB\-\-jobs\fR=\fI\,JOBS\/\fR
number of jobs to run in parallel (default: based on
number of CPU cores)
.SS Logging options:
.TP
\fB\-\-verbose\fR
show all output
.TP
\fB\-q\fR, \fB\-\-quiet\fR
only show errors
.SS Multi\-manifest options:
.TP
\fB\-\-outer\-manifest\fR
operate starting at the outermost manifest
.TP
\fB\-\-no\-outer\-manifest\fR
do not operate on outer manifests
.TP
\fB\-\-this\-manifest\-only\fR
only operate on this (sub)manifest
.TP
\fB\-\-no\-this\-manifest\-only\fR, \fB\-\-all\-manifests\fR
operate on this manifest and its submanifests
.SS Sources:
.TP
\fB\-\-cached\fR
Search the index, instead of the work tree
.TP
\fB\-r\fR TREEish, \fB\-\-revision\fR=\fI\,TREEish\/\fR
Search TREEish, instead of the work tree
.SS Pattern:
.TP
\fB\-e\fR PATTERN
Pattern to search for
.TP
\fB\-i\fR, \fB\-\-ignore\-case\fR
Ignore case differences
.TP
\fB\-a\fR, \fB\-\-text\fR
Process binary files as if they were text
.TP
\fB\-I\fR
Don't match the pattern in binary files
.TP
\fB\-w\fR, \fB\-\-word\-regexp\fR
Match the pattern only at word boundaries
.TP
\fB\-v\fR, \fB\-\-invert\-match\fR
Select non\-matching lines
.TP
\fB\-G\fR, \fB\-\-basic\-regexp\fR
Use POSIX basic regexp for patterns (default)
.TP
\fB\-E\fR, \fB\-\-extended\-regexp\fR
Use POSIX extended regexp for patterns
.TP
\fB\-F\fR, \fB\-\-fixed\-strings\fR
Use fixed strings (not regexp) for pattern
.SS Pattern Grouping:
.TP
\fB\-\-all\-match\fR
Limit match to lines that have all patterns
.TP
\fB\-\-and\fR, \fB\-\-or\fR, \fB\-\-not\fR
Boolean operators to combine patterns
.TP
\-(, \-)
Boolean operator grouping
.SS Output:
.TP
\fB\-n\fR
Prefix the line number to matching lines
.TP
\fB\-C\fR CONTEXT
Show CONTEXT lines around match
.TP
\fB\-B\fR CONTEXT
Show CONTEXT lines before match
.TP
\fB\-A\fR CONTEXT
Show CONTEXT lines after match
.TP
\fB\-l\fR, \fB\-\-name\-only\fR, \fB\-\-files\-with\-matches\fR
Show only file names containing matching lines
.TP
\fB\-L\fR, \fB\-\-files\-without\-match\fR
Show only file names not containing matching lines
.PP
Run `repo help grep` to view the detailed manual.
.SH DETAILS
.PP
Search for the specified patterns in all project files.
.PP
Boolean Options
.PP
The following options can appear as often as necessary to express the pattern to
locate:
.HP
\fB\-e\fR PATTERN
.HP
\fB\-\-and\fR, \fB\-\-or\fR, \fB\-\-not\fR, \-(, \-)
.PP
Further, the \fB\-r\fR/\-\-revision option may be specified multiple times in order to
scan multiple trees. If the same file matches in more than one tree, only the
first result is reported, prefixed by the revision name it was found under.
.PP
Examples
.PP
Look for a line that has '#define' and either 'MAX_PATH or 'PATH_MAX':
.IP
repo grep \fB\-e\fR '#define' \fB\-\-and\fR \-\e( \fB\-e\fR MAX_PATH \fB\-e\fR PATH_MAX \e)
.PP
Look for a line that has 'NODE' or 'Unexpected' in files that contain a line
that matches both expressions:
.IP
repo grep \fB\-\-all\-match\fR \fB\-e\fR NODE \fB\-e\fR Unexpected
