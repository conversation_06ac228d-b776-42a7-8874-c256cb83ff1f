.\" DO NOT MODIFY THIS FILE!  It was generated by help2man.
.TH REPO "1" "July 2022" "repo forall" "Repo Manual"
.SH NAME
repo \- repo forall - manual page for repo forall
.SH SYNOPSIS
.B repo
\fI\,forall \/\fR[\fI\,<project>\/\fR...] \fI\,-c <command> \/\fR[\fI\,<arg>\/\fR...]
.SH DESCRIPTION
Summary
.PP
Run a shell command in each project
.PP
repo forall \fB\-r\fR str1 [str2] ... \fB\-c\fR <command> [<arg>...]
.SH OPTIONS
.TP
\fB\-h\fR, \fB\-\-help\fR
show this help message and exit
.TP
\fB\-j\fR JOBS, \fB\-\-jobs\fR=\fI\,JOBS\/\fR
number of jobs to run in parallel (default: based on
number of CPU cores)
.TP
\fB\-r\fR, \fB\-\-regex\fR
execute the command only on projects matching regex or
wildcard expression
.TP
\fB\-i\fR, \fB\-\-inverse\-regex\fR
execute the command only on projects not matching
regex or wildcard expression
.TP
\fB\-g\fR GROUPS, \fB\-\-groups\fR=\fI\,GROUPS\/\fR
execute the command only on projects matching the
specified groups
.TP
\fB\-c\fR, \fB\-\-command\fR
command (and arguments) to execute
.TP
\fB\-e\fR, \fB\-\-abort\-on\-errors\fR
abort if a command exits unsuccessfully
.TP
\fB\-\-ignore\-missing\fR
silently skip & do not exit non\-zero due missing
checkouts
.TP
\fB\-\-interactive\fR
force interactive usage
.SS Logging options:
.TP
\fB\-v\fR, \fB\-\-verbose\fR
show all output
.TP
\fB\-q\fR, \fB\-\-quiet\fR
only show errors
.TP
\fB\-p\fR
show project headers before output
.SS Multi\-manifest options:
.TP
\fB\-\-outer\-manifest\fR
operate starting at the outermost manifest
.TP
\fB\-\-no\-outer\-manifest\fR
do not operate on outer manifests
.TP
\fB\-\-this\-manifest\-only\fR
only operate on this (sub)manifest
.TP
\fB\-\-no\-this\-manifest\-only\fR, \fB\-\-all\-manifests\fR
operate on this manifest and its submanifests
.PP
Run `repo help forall` to view the detailed manual.
.SH DETAILS
.PP
Executes the same shell command in each project.
.PP
The \fB\-r\fR option allows running the command only on projects matching regex or
wildcard expression.
.PP
By default, projects are processed non\-interactively in parallel. If you want to
run interactive commands, make sure to pass \fB\-\-interactive\fR to force \fB\-\-jobs\fR 1.
While the processing order of projects is not guaranteed, the order of project
output is stable.
.PP
Output Formatting
.PP
The \fB\-p\fR option causes 'repo forall' to bind pipes to the command's stdin, stdout
and stderr streams, and pipe all output into a continuous stream that is
displayed in a single pager session. Project headings are inserted before the
output of each command is displayed. If the command produces no output in a
project, no heading is displayed.
.PP
The formatting convention used by \fB\-p\fR is very suitable for some types of
searching, e.g. `repo forall \fB\-p\fR \fB\-c\fR git log \fB\-SFoo\fR` will print all commits that
add or remove references to Foo.
.PP
The \fB\-v\fR option causes 'repo forall' to display stderr messages if a command
produces output only on stderr. Normally the \fB\-p\fR option causes command output to
be suppressed until the command produces at least one byte of output on stdout.
.PP
Environment
.PP
pwd is the project's working directory. If the current client is a mirror
client, then pwd is the Git repository.
.PP
REPO_PROJECT is set to the unique name of the project.
.PP
REPO_PATH is the path relative the the root of the client.
.PP
REPO_OUTERPATH is the path of the sub manifest's root relative to the root of
the client.
.PP
REPO_INNERPATH is the path relative to the root of the sub manifest.
.PP
REPO_REMOTE is the name of the remote system from the manifest.
.PP
REPO_LREV is the name of the revision from the manifest, translated to a local
tracking branch. If you need to pass the manifest revision to a locally executed
git command, use REPO_LREV.
.PP
REPO_RREV is the name of the revision from the manifest, exactly as written in
the manifest.
.PP
REPO_COUNT is the total number of projects being iterated.
.PP
REPO_I is the current (1\-based) iteration count. Can be used in conjunction with
REPO_COUNT to add a simple progress indicator to your command.
.PP
REPO__* are any extra environment variables, specified by the "annotation"
element under any project element. This can be useful for differentiating trees
based on user\-specific criteria, or simply annotating tree details.
.PP
shell positional arguments ($1, $2, .., $#) are set to any arguments following
<command>.
.PP
Example: to list projects:
.IP
repo forall \fB\-c\fR 'echo $REPO_PROJECT'
.PP
Notice that $REPO_PROJECT is quoted to ensure it is expanded in the context of
running <command> instead of in the calling shell.
.PP
Unless \fB\-p\fR is used, stdin, stdout, stderr are inherited from the terminal and are
not redirected.
.PP
If \fB\-e\fR is used, when a command exits unsuccessfully, 'repo forall' will abort
without iterating through the remaining projects.
