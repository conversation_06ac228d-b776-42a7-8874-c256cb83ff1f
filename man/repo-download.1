.\" DO NOT MODIFY THIS FILE!  It was generated by help2man.
.TH REPO "1" "July 2022" "repo download" "Repo Manual"
.SH NAME
repo \- repo download - manual page for repo download
.SH SYNOPSIS
.B repo
\fI\,download {\/\fR[\fI\,project\/\fR] \fI\,change\/\fR[\fI\,/patchset\/\fR]\fI\,}\/\fR...
.SH DESCRIPTION
Summary
.PP
Download and checkout a change
.SH OPTIONS
.TP
\fB\-h\fR, \fB\-\-help\fR
show this help message and exit
.TP
\fB\-b\fR BRANCH, \fB\-\-branch\fR=\fI\,BRANCH\/\fR
create a new branch first
.TP
\fB\-c\fR, \fB\-\-cherry\-pick\fR
cherry\-pick instead of checkout
.TP
\fB\-x\fR, \fB\-\-record\-origin\fR
pass \fB\-x\fR when cherry\-picking
.TP
\fB\-r\fR, \fB\-\-revert\fR
revert instead of checkout
.TP
\fB\-f\fR, \fB\-\-ff\-only\fR
force fast\-forward merge
.SS Logging options:
.TP
\fB\-v\fR, \fB\-\-verbose\fR
show all output
.TP
\fB\-q\fR, \fB\-\-quiet\fR
only show errors
.SS Multi\-manifest options:
.TP
\fB\-\-outer\-manifest\fR
operate starting at the outermost manifest
.TP
\fB\-\-no\-outer\-manifest\fR
do not operate on outer manifests
.TP
\fB\-\-this\-manifest\-only\fR
only operate on this (sub)manifest
.TP
\fB\-\-no\-this\-manifest\-only\fR, \fB\-\-all\-manifests\fR
operate on this manifest and its submanifests
.PP
Run `repo help download` to view the detailed manual.
.SH DETAILS
.PP
The 'repo download' command downloads a change from the review system and makes
it available in your project's local working directory. If no project is
specified try to use current directory as a project.
