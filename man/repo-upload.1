.\" DO NOT MODIFY THIS FILE!  It was generated by help2man.
.TH REPO "1" "June 2024" "repo upload" "Repo Manual"
.SH NAME
repo \- repo upload - manual page for repo upload
.SH SYNOPSIS
.B repo
\fI\,upload \/\fR[\fI\,--re --cc\/\fR] [\fI\,<project>\/\fR]...
.SH DESCRIPTION
Summary
.PP
Upload changes for code review
.SH OPTIONS
.TP
\fB\-h\fR, \fB\-\-help\fR
show this help message and exit
.TP
\fB\-j\fR JOBS, \fB\-\-jobs\fR=\fI\,JOBS\/\fR
number of jobs to run in parallel (default: based on
number of CPU cores)
.TP
\fB\-t\fR, \fB\-\-topic\-branch\fR
set the topic to the local branch name
.TP
\fB\-\-topic\fR=\fI\,TOPIC\/\fR
set topic for the change
.TP
\fB\-\-hashtag\fR=\fI\,HASHTAGS\/\fR, \fB\-\-ht\fR=\fI\,HASHTAGS\/\fR
add hashtags (comma delimited) to the review
.TP
\fB\-\-hashtag\-branch\fR, \fB\-\-htb\fR
add local branch name as a hashtag
.TP
\fB\-l\fR LABELS, \fB\-\-label\fR=\fI\,LABELS\/\fR
add a label when uploading
.TP
\fB\-\-pd\fR=\fI\,PATCHSET_DESCRIPTION\/\fR, \fB\-\-patchset\-description\fR=\fI\,PATCHSET_DESCRIPTION\/\fR
description for patchset
.TP
\fB\-\-re\fR=\fI\,REVIEWERS\/\fR, \fB\-\-reviewers\fR=\fI\,REVIEWERS\/\fR
request reviews from these people
.TP
\fB\-\-cc\fR=\fI\,CC\/\fR
also send email to these email addresses
.TP
\fB\-\-br\fR=\fI\,BRANCH\/\fR, \fB\-\-branch\fR=\fI\,BRANCH\/\fR
(local) branch to upload
.TP
\fB\-c\fR, \fB\-\-current\-branch\fR
upload current git branch
.TP
\fB\-\-no\-current\-branch\fR
upload all git branches
.TP
\fB\-\-ne\fR, \fB\-\-no\-emails\fR
do not send e\-mails on upload
.TP
\fB\-p\fR, \fB\-\-private\fR
upload as a private change (deprecated; use \fB\-\-wip\fR)
.TP
\fB\-w\fR, \fB\-\-wip\fR
upload as a work\-in\-progress change
.TP
\fB\-r\fR, \fB\-\-ready\fR
mark change as ready (clears work\-in\-progress setting)
.TP
\fB\-o\fR PUSH_OPTIONS, \fB\-\-push\-option\fR=\fI\,PUSH_OPTIONS\/\fR
additional push options to transmit
.TP
\fB\-D\fR BRANCH, \fB\-\-destination\fR=\fI\,BRANCH\/\fR, \fB\-\-dest\fR=\fI\,BRANCH\/\fR
submit for review on this target branch
.TP
\fB\-n\fR, \fB\-\-dry\-run\fR
do everything except actually upload the CL
.TP
\fB\-y\fR, \fB\-\-yes\fR
answer yes to all safe prompts
.TP
\fB\-\-ignore\-untracked\-files\fR
ignore untracked files in the working copy
.TP
\fB\-\-no\-ignore\-untracked\-files\fR
always ask about untracked files in the working copy
.TP
\fB\-\-no\-cert\-checks\fR
disable verifying ssl certs (unsafe)
.SS Logging options:
.TP
\fB\-v\fR, \fB\-\-verbose\fR
show all output
.TP
\fB\-q\fR, \fB\-\-quiet\fR
only show errors
.SS Multi\-manifest options:
.TP
\fB\-\-outer\-manifest\fR
operate starting at the outermost manifest
.TP
\fB\-\-no\-outer\-manifest\fR
do not operate on outer manifests
.TP
\fB\-\-this\-manifest\-only\fR
only operate on this (sub)manifest
.TP
\fB\-\-no\-this\-manifest\-only\fR, \fB\-\-all\-manifests\fR
operate on this manifest and its submanifests
.SS pre\-upload hooks:
.TP
\fB\-\-no\-verify\fR
Do not run the pre\-upload hook.
.TP
\fB\-\-verify\fR
Run the pre\-upload hook without prompting.
.TP
\fB\-\-ignore\-hooks\fR
Do not abort if pre\-upload hooks fail.
.PP
Run `repo help upload` to view the detailed manual.
.SH DETAILS
.PP
The 'repo upload' command is used to send changes to the Gerrit Code Review
system. It searches for topic branches in local projects that have not yet been
published for review. If multiple topic branches are found, 'repo upload' opens
an editor to allow the user to select which branches to upload.
.PP
\&'repo upload' searches for uploadable changes in all projects listed at the
command line. Projects can be specified either by name, or by a relative or
absolute path to the project's local directory. If no projects are specified,
\&'repo upload' will search for uploadable changes in all projects listed in the
manifest.
.PP
If the \fB\-\-reviewers\fR or \fB\-\-cc\fR options are passed, those emails are added to the
respective list of users, and emails are sent to any new users. Users passed as
\fB\-\-reviewers\fR must already be registered with the code review system, or the
upload will fail.
.PP
While most normal Gerrit options have dedicated command line options, direct
access to the Gerit options is available via \fB\-\-push\-options\fR. This is useful when
Gerrit has newer functionality that repo upload doesn't yet support, or doesn't
have plans to support. See the Push Options documentation for more details:
https://gerrit\-review.googlesource.com/Documentation/user\-upload.html#push_options
.PP
Configuration
.PP
review.URL.autoupload:
.PP
To disable the "Upload ... (y/N)?" prompt, you can set a per\-project or global
Git configuration option. If review.URL.autoupload is set to "true" then repo
will assume you always answer "y" at the prompt, and will not prompt you
further. If it is set to "false" then repo will assume you always answer "n",
and will abort.
.PP
review.URL.autoreviewer:
.PP
To automatically append a user or mailing list to reviews, you can set a
per\-project or global Git option to do so.
.PP
review.URL.autocopy:
.PP
To automatically copy a user or mailing list to all uploaded reviews, you can
set a per\-project or global Git option to do so. Specifically,
review.URL.autocopy can be set to a comma separated list of reviewers who you
always want copied on all uploads with a non\-empty \fB\-\-re\fR argument.
.PP
review.URL.username:
.PP
Override the username used to connect to Gerrit Code Review. By default the
local part of the email address is used.
.PP
The URL must match the review URL listed in the manifest XML file, or in the
\&.git/config within the project. For example:
.IP
[remote "origin"]
.IP
url = git://git.example.com/project.git
review = http://review.example.com/
.IP
[review "http://review.example.com/"]
.IP
autoupload = true
autocopy = <EMAIL>,my\-team\-<EMAIL>
.PP
review.URL.uploadtopic:
.PP
To add a topic branch whenever uploading a commit, you can set a per\-project or
global Git option to do so. If review.URL.uploadtopic is set to "true" then repo
will assume you always want the equivalent of the \fB\-t\fR option to the repo command.
If unset or set to "false" then repo will make use of only the command line
option.
.PP
review.URL.uploadhashtags:
.PP
To add hashtags whenever uploading a commit, you can set a per\-project or global
Git option to do so. The value of review.URL.uploadhashtags will be used as
comma delimited hashtags like the \fB\-\-hashtag\fR option.
.PP
review.URL.uploadlabels:
.PP
To add labels whenever uploading a commit, you can set a per\-project or global
Git option to do so. The value of review.URL.uploadlabels will be used as comma
delimited labels like the \fB\-\-label\fR option.
.PP
review.URL.uploadnotify:
.PP
Control e\-mail notifications when uploading.
https://gerrit\-review.googlesource.com/Documentation/user\-upload.html#notify
.PP
review.URL.uploadwarningthreshold:
.PP
Repo will warn you if you are attempting to upload a large number of commits in
one or more branches. By default, the threshold is five commits. This option
allows you to override the warning threshold to a different value.
.PP
References
.PP
Gerrit Code Review: https://www.gerritcodereview.com/
