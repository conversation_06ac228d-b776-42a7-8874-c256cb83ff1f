.\" DO NOT MODIFY THIS FILE!  It was generated by help2man.
.TH REPO "1" "July 2022" "repo selfupdate" "Repo Manual"
.SH NAME
repo \- repo selfupdate - manual page for repo selfupdate
.SH SYNOPSIS
.B repo
\fI\,selfupdate\/\fR
.SH DESCRIPTION
Summary
.PP
Update repo to the latest version
.SH OPTIONS
.TP
\fB\-h\fR, \fB\-\-help\fR
show this help message and exit
.SS Logging options:
.TP
\fB\-v\fR, \fB\-\-verbose\fR
show all output
.TP
\fB\-q\fR, \fB\-\-quiet\fR
only show errors
.SS Multi\-manifest options:
.TP
\fB\-\-outer\-manifest\fR
operate starting at the outermost manifest
.TP
\fB\-\-no\-outer\-manifest\fR
do not operate on outer manifests
.TP
\fB\-\-this\-manifest\-only\fR
only operate on this (sub)manifest
.TP
\fB\-\-no\-this\-manifest\-only\fR, \fB\-\-all\-manifests\fR
operate on this manifest and its submanifests
.SS repo Version options:
.TP
\fB\-\-no\-repo\-verify\fR
do not verify repo source code
.PP
Run `repo help selfupdate` to view the detailed manual.
.SH DETAILS
.PP
The 'repo selfupdate' command upgrades repo to the latest version, if a newer
version is available.
.PP
Normally this is done automatically by 'repo sync' and does not need to be
performed by an end\-user.
