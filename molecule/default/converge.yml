- name: Converge
  hosts: all
  become: true
  vars:
    vag_node_name: "vagrantsms-10001-prod-nydc1.nydc1.outbrain.com"
  tasks:
    - name: "Include apt"
      include_tasks: ../../tasks/apt.yml
      vars:
        sms_vars:
          apt_packages:
            - nginx
            - supervisor
    - name: "Include docker_service_status"
      include_tasks: ../../tasks/docker_service_status.yml
    #- name: "Include docker_skip_if_not_aufs_and_not_needs_restart"
    #  include_tasks: ../../tasks/docker_skip_if_not_aufs_and_not_needs_restart.yml
    - name: "Include service"
      include_tasks: ../../tasks/service.yml
      vars:
        sms_vars:
          service_name: nginx
          service_state: started
    - name: "Include supervisor"
      include_tasks: ../../tasks/supervisor.yml
      vars:
        sms_vars:
          supervisor_name: dummy
          supervisor_state: started
    - name: "Include shell"
      include_tasks: ../../tasks/shell.yml
      vars:
        sms_vars:
          shell_command: /opt/dummy_supervisor_service/dummy_script.sh
    - name: "Include os_patch_ansible"
      include_tasks: ../../tasks/os_patch_ansible.yml
      vars:
        sms_vars:
          os_patch_kernel_upgrade: true
          os_patch_reboot_allowed: true
        sms_check_mode: true
    - name: "Include os_patch_check"
      include_tasks: ../../tasks/os_patch_check.yml
    - name: "Include raw"
      include_tasks: ../../tasks/raw.yml
      vars:
        sms_vars:
          raw_command: echo $(hostname) $(date)


