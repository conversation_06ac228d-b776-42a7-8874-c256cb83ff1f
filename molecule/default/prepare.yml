---
- name: Prepare
  hosts: all
  gather_facts: true
  become: yes
  vars:
    consul_version: "1.2.1"
    vag_node_name: "vagrantsms-10001-prod-nydc1.nydc1.outbrain.com"
  tasks:
    - name: Install python for Ansible
      raw: test -e /usr/bin/python || (apt -y update && apt install -y python)
      become: true
      changed_when: false 
    - name: Install required packages
      apt:
        pkg: "{{item}}"
        update_cache: yes
        state: present
      with_items:
        - python3-pip
        - python3
        - unzip
        - apt-transport-https
        - ca-certificates
        - curl
        - gnupg-agent
        - software-properties-common
        - supervisor
        - nginx
        - jq
    - name: Add docker repo GPG
      apt_key:
        url: https://download.docker.com/linux/ubuntu/gpg
        state: present
    - name: Add repository docker
      apt_repository:
        repo: "deb https://download.docker.com/linux/ubuntu {{ ansible_distribution_release }} stable"
        state: present
    - name: Install docker
      apt:
        name: "{{item}}"
        state: latest
        update_cache: yes
      loop:
        - docker-ce
        - docker-ce-cli
        - containerd.io
    - name: Make sure docker service is active
      service:
        name: docker
        state: started
        enabled: yes
    - name: Create a directory for dummy_supervisor_service
      ansible.builtin.file:
        path: /opt/dummy_supervisor_service
        state: directory
        mode: '0755'
    - name: Creating dummy_supervisor_service with content
      copy:
        dest: /opt/dummy_supervisor_service/service.sh
        content: |
          #!/bin/bash
          while true
          do
               # Echo hostname to stdout
                echo `hostname`
               # Echo 'redirect error' to stderr
                echo 'redirect error' >&2
                sleep 10
          done
    - name: Create a new file with permissions
      file:
        path: /opt/dummy_supervisor_service/service.sh
        mode: 0755
    - name: Creating dummy_supervisor_service service file
      copy:
        dest: /etc/supervisor/conf.d/dummy_supervisor_service.conf
        content: |
          [program:dummy]
          command=/opt/dummy_supervisor_service/service.sh
          autostart=true
          autorestart=true
          stderr_logfile=/tmp/test.err
          stdout_logfile=/tmp/test.out
    - name: Reread supervisord service files
      ansible.builtin.command: supervisorctl reread
    - name: Update supervisord services
      ansible.builtin.command: supervisorctl update
    - name: Creating dummy_script with content
      copy:
        dest: /opt/dummy_supervisor_service/dummy_script.sh
        content: |
          #!/bin/bash
          # Echo hostname to stdout
          echo "$(hostname) - $0"
    - name: Create a new file with permissions
      file:
        path: /opt/dummy_supervisor_service/dummy_script.sh
        mode: 0755


