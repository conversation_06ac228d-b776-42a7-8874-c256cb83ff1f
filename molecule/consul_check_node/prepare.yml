---
- name: Prepare
  hosts: all
  gather_facts: false
  become: yes
  vars:
    consul_version: "1.2.1"
    vag_node_name: "vagrantsms-10001-prod-nydc1.nydc1.outbrain.com"
  tasks:
    - name: Install python for Ansible
      raw: test -e /usr/bin/python || (apt -y update && apt install -y python)
      become: true
      changed_when: false 
    - name: Install required packages
      apt:
        pkg: "{{item}}"
        update_cache: yes
        state: present
      with_items:
        - python3-pip
        - python3
        - unzip
    - name: Fetch consul
      get_url:
        url: "https://artifactorymaster.outbrain.com/artifactory/ob-filestore/consul/{{ consul_version }}/consul_{{ consul_version }}_linux_amd64.zip"
        dest: /root/consul_linux_amd64.zip
    - name: Unarchive consul
      unarchive:
        src: /root/consul_linux_amd64.zip
        dest: /root/
        creates: /root/consul
        remote_src: yes
    - name: Run consul
      shell: "/root/consul agent -dev -node {{ vag_node_name }} &"
      changed_when: false
      async: 600
      poll: 0  