# Ansible Collection - dog.sms

This is a collection of our plugins needed by the projects.

## Inventory plugin
This is a modified [Device42](https://github.com/device42/ansible_device42) plugin tailored to our needs. It differs
from the original in way that it changes the field names and it adds some additional composed fields.

### Workaround for AWX 17.1
We are currently running on AWX 17.1 (latest non kubernetes release) due to some issues that we hit with kubernetes
release and couldn't solve yet. 

Installing collection directly from git repos as
```
- name: ssh://**************************:7999/dog/sms-ansible-collections.git
  type: git
```
is not supported in the built-in ansible version (2.9) so the following workaround is required:

The collection needs to be built as tar file with 
```shell
ansible-galaxy collection build
```
and pushed to repo server (or some other storage) that needs to be public since auth is not supported with type url. 
Then you can include it in the `requirements.yml` file as
```
- name: http://repo-master/misc/ansible/collections/dog/sms/dog-sms-1.0.X.tar.gz
  type: url
```
