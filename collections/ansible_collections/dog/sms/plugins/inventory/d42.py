from __future__ import (absolute_import, division, print_function)

from urllib.parse import urljoin

from ansible.plugins.inventory import BaseInventoryPlugin, Constructable, Cacheable, to_safe_group_name
import requests
import sys
import time

import re
RE_EXPECTED_NAME_FIELD = re.compile(r"([\w\-]+)-(\d{5})-([a-z]+)-([a-z]+dc[0-9])\s\((\w{6})\)")

__metaclass__ = type

DOCUMENTATION = r'''
    module: d42
    plugin_type: inventory
    author:
        - <PERSON> (@willtome)
        - <PERSON> (@osanchez42)
        - <PERSON><PERSON>
    short_description: Device42 Inventory Plugin
    version_added: "2.10"
    description:
        - Device42 Inventory plugin
    extends_documentation_fragment:
        - constructed
    options:
        plugin:
            description: The name of the Device42 Inventory Plugin, this should always be 'dog.sms.d42'.
            required: true
            choices: ['dog.sms.d42']
        url:
            description: URI of Device42. The URI should be the fully-qualified domain name, e.g. 'your-instance.device42.net'.
            type: string
            required: true
            env:
                - name: D42_URL
        username:
            description: The Device42 user account.
            type: string
            required: true
            env:
                - name: D42_USER
        password:
            description: The Device42 instance user password.
            type: string
            secret: true
            required: true
            env:
                - name: D42_PWD
        ssl_check:
            description: SSL verification.
            type: boolean
            default: true
            env:
                - name: D42_SSL_CHECK
        debug:
            description: Debug option.
            type: boolean
            default: false
            env:
                - name: D42_DEBUG
        clean_device_name:
            description: group name cleaning option.
            type: boolean
            default: true
            env:
                - name: D42_CLEAN_DEVICE_NAME
'''

EXAMPLES = r'''
plugin: dog.sms.d42
d42_url: https://device42.outbrain.com/
d42_username: admin
d42_password: password
ssl_check: False
debug: False
clean_device_name: True
keyed_groups:
    - key: d42_service_level
      prefix: ''
      separator: ''
'''


class InventoryModule(BaseInventoryPlugin, Constructable, Cacheable):
    NAME = 'dog.sms.d42'

    def debug(self, *args, **kwargs):
        if self.get_option('debug'):
            print(*args, file=sys.stderr, **kwargs)

    def verify_file(self, path):
        valid = False

        if super(InventoryModule, self).verify_file(path):
            if path.endswith(('d42.yaml', 'd42.yml')):
                valid = True
            else:
                self.display.vvv('Skipping due to inventory source not ending in "d42.yaml" nor "d42.yml"')

        return valid

    def _ob_parse_name(self, name: str):
        if RE_EXPECTED_NAME_FIELD.match(name):
            split = RE_EXPECTED_NAME_FIELD.split(name)
            environment = split[3]
            datacenter = split[4]
            hid = split[5]
            hostname = '{hostname_short}.{dc}.outbrain.com'.format(hostname_short='-'.join(split[1:5]), dc=datacenter)
            return hostname, environment, datacenter, hid
        else:
            self.debug("[WARN] Device name '%s' doesn't follow naming convention in D42" % name)
            return name, 'n/a', 'n/a', 'n/a'

    def _keys_to_lower(self, input: dict) -> dict:
        new_dict = dict()
        for k, v in input.items():
            new_dict[k.lower()] = v

        return new_dict

    def parse(self, inventory, loader, path, cache=False):
        super(InventoryModule, self).parse(inventory, loader, path)

        self._read_config_data(path)

        strict = self.get_option('strict')

        try:
            try:
                clean_device_name = self.get_option('clean_device_name')
            except Exception:
                print("clean_device_name has not been defined in *.d42.yml. defaulting to True")
                clean_device_name = True

            objects = []

            json_response = self.get_d42_inventory()

            if 'Devices' in json_response:
                objects = json_response['Devices']

            for object_ in objects:
                if clean_device_name:
                    host_name = self.inventory.add_host(to_safe_group_name(object_['name']))
                else:
                    host_name = self.inventory.add_host(object_['name'])

                for k in object_.keys():
                    self.inventory.set_variable(host_name, k, object_[k])

                if object_['ip']:
                    self.inventory.set_variable(host_name, 'ansible_host', object_['ip'])

                self._set_composite_vars(
                    self.get_option('compose'),
                    self.inventory.get_host(host_name).get_vars(), host_name,
                    strict)

                self._add_host_to_composed_groups(self.get_option('groups'), dict(), host_name, strict)
                self._add_host_to_keyed_groups(self.get_option('keyed_groups'), dict(), host_name, strict)
        except Exception as e:
            print(e)

    def get_doql_json(self, query):
        base_url = self.get_option('url')
        username = self.get_option('username')
        password = self.get_option('password')
        ssl_check = self.get_option('ssl_check')
        debug = self.get_option('debug')

        data = {'output_type': 'json', 'query': query}
        start_time = time.time()

        try:
            # while there should be no timeout, ansible seems to get stuck sending requests without timeouts
            url = urljoin(base_url, "services/data/v1.0/query/")
            response = requests.post(url, data=data, auth=(username, password), verify=ssl_check, timeout=600)

            self.debug("Time elapsed: %.2f seconds" % (time.time()-start_time))

            status_code = response.status_code

            if not response.ok:
                print('an error was encountered on query API call to Device42')
                print('Response Status: ' + str(status_code))
                raise Exception('Error: ' + str(status_code))
            else:
                # csv response to json object
                if debug:
                    with open(f'dump-{hash(query)}.txt', 'w') as f:
                        f.write(response.text)
                    self.debug('Response Status: ' + str(status_code))
                unformatted_d42_inventory = response.json()

        except Exception as e:
            self.debug("Err:", e)
            raise

        return unformatted_d42_inventory

    def get_d42_inventory(self):
        # the final inventory that will be returned
        inventory = {
            'total_count': None,
            'Devices': [],
        }

        # the dictionary below is used to build the json object
        d42_inventory = {}

        # get all the items from device42 that will be used to build the json object
        self.debug('Getting Devices')
        d42_devices = self.get_devices()
        self.debug('Getting Custom Fields')
        d42_device_custom_fields = self.get_custom_fields()

        FIELDS = [
            'device_pk',
            'cpucore',
            'cpucount',
            'name',
            'os',
            'ram',
            'ip',
            'model'
        ]
        for device in d42_devices:
            device_record = {}
            for field in FIELDS:
                device_record[field] = device.get(field, 'n/a')

            device_record['id'] = device.get('device_pk')
            hostname, environment, datacenter, hid = self._ob_parse_name(device.get('name'))
            device_record['name'] = hostname
            device_record['environment'] = environment
            device_record['datacenter'] = datacenter
            device_record['hid'] = hid

            # store the device in a dict for updates
            d42_inventory[device['device_pk']] = device_record

        CUSTOM_FIELDS = [
            "Managed_For",
            "Network_Type",
            "InstalledFor",
            "Consul_Tags",
            "Owner",
            "Patch_Last_Updated",
            "IPMI_IP",
        ]
        for custom_field_record in d42_device_custom_fields:
            device_id = custom_field_record.get('device_pk')
            if device_id not in d42_inventory:
                continue

            if "custom_fields" not in custom_field_record:
                continue

            custom_field_record = custom_field_record["custom_fields"]

            device_record = d42_inventory[device_id]
            device_record['custom_fields'] = self._keys_to_lower(custom_field_record)

            for field in CUSTOM_FIELDS:
                device_record[field.lower()] = custom_field_record.get(field, 'n/a')

        total_count = 0
        for key, value in d42_inventory.items():
            inventory['Devices'].append(value)
            total_count += 1

        # update with the final device count
        inventory['total_count'] = total_count

        return inventory

    def get_devices(self):
        device_query = """
            SELECT
                dev.device_pk,
                dev.cpucore,
                dev.cpucount,
                dev.name,
                dev.os_name as os,
                dev.ram,
                ipa.ip_address as ip,
                h.name as model
            FROM
                view_device_v1 dev
            LEFT JOIN
                view_ipaddress_v1 ipa
            ON dev.device_pk = ipa.device_fk
            LEFT JOIN
                view_hardware_v1 h
            ON dev.hardware_fk = h.hardware_pk
            WHERE
                dev.service_level='Active' AND
                NOT dev.network_device AND
                ipa.ip_address IS NOT NULL
        """
        return self.get_doql_json(device_query)

    def get_custom_fields(self):
        custom_field_query = """
            SELECT
                c.device_fk AS device_pk,
                JSON_OBJECT_AGG(c.key, c.value) AS custom_fields
            FROM
                view_device_custom_fields_v1 c
            GROUP BY
                device_pk
        """
        return self.get_doql_json(custom_field_query)
