#!/usr/bin/env python3

"""
Test script to call the InventoryModule.parse() function from azure_rm.py
"""

import sys
import os
from unittest.mock import Mock, MagicMock

# Add the plugins directory to the path so we can import the module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'plugins', 'inventory'))

try:
    from azure_rm import InventoryModule
except ImportError as e:
    print(f"Error importing InventoryModule: {e}")
    sys.exit(1)

def create_mock_inventory():
    """Create a mock inventory object"""
    inventory = Mock()
    inventory.add_host = Mock()
    inventory.set_variable = Mock()
    inventory.add_group = Mock()
    return inventory

def create_mock_loader():
    """Create a mock loader object"""
    loader = Mock()

    def mock_load_from_file(path, cache=None):
        """Mock load_from_file that actually reads the file"""
        try:
            import yaml
            with open(path, 'r') as f:
                config = yaml.safe_load(f)
                # Add all default values for options that might not be in the config
                defaults = {
                    # Authentication options
                    'auth_source': 'auto',
                    'profile': None,
                    'subscription_id': None,
                    'client_id': None,
                    'secret': None,
                    'tenant': None,
                    'ad_user': None,
                    'password': None,
                    'cloud_environment': 'AzureCloud',
                    'cert_validation_mode': 'validate',
                    'api_profile': 'latest',
                    'adfs_authority_url': None,

                    # Resource group options
                    'include_vm_resource_groups': ['*'],
                    'include_vmss_resource_groups': [],
                    'include_arc_resource_groups': [],
                    'include_hcivm_resource_groups': [],

                    # Filter options
                    'exclude_host_filters': [],
                    'include_host_filters': [True],
                    'default_host_filters': ['powerstate != "running"', 'provisioning_state != "succeeded"'],

                    # Batch fetch options
                    'batch_fetch': True,
                    'batch_fetch_interval': 3,
                    'batch_fetch_timeout': 5,

                    # Hostname options
                    'plain_host_names': False,
                    'use_contrib_script_compatible_sanitization': False,

                    # Constructable options
                    'hostvar_expressions': None,
                    'compose': {},
                    'conditional_groups': {},
                    'keyed_groups': [],
                    'hostnames': ["default"],
                    'fail_on_template_errors': False,

                    # Cache options
                    'cache': False,
                    'cache_plugin': None,
                    'cache_timeout': 3600,
                    'cache_connection': None,
                    'cache_prefix': 'ansible_inventory_',
                }

                # Merge defaults with config, config takes precedence
                for key, default_value in defaults.items():
                    if key not in config:
                        config[key] = default_value

                return config
        except Exception as e:
            print(f"Mock loader error reading {path}: {e}")
            return {}

    loader.load_from_file = mock_load_from_file
    loader.get_basedir = Mock(return_value=os.getcwd())  # Return current directory as base dir
    return loader

def create_test_config_file():
    """Create a test azure_rm.yml configuration file"""
    config_content = """plugin: dog.sms.azure_rm

tenant: b1266500-6469-4cd5-b8d3-4e4502738030
secret: ****************************************
client_id: 3da4124d-eee5-47a1-8cc2-65661dd93686
subscription_id: ceab8b83-534c-4530-937e-25e79765899f

hostnames: 
- computer_name
include_host_filters:
- computer_name.endswith('outbrain.com')
cache: true
datacenters: 
- nldc1
- nldc2 
- wndc1 
- sngdc1
"""

    config_file = os.path.abspath("test_azure_rm.yml")  # Use absolute path
    with open(config_file, 'w') as f:
        f.write(config_content.strip())  # Remove leading/trailing whitespace
        f.flush()  # Ensure content is written to disk

    # Verify the file was created and has content
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            content = f.read()
            print(f"📝 Created config file at {config_file} with {len(content)} characters")
            print(f"📄 First 100 chars: {content[:100]}...")
            if len(content) == 0:
                print("⚠️  Warning: Config file is empty!")
    else:
        print(f"❌ Failed to create config file: {config_file}")

    return config_file

def main():
    """Main function to test the InventoryModule.parse() method"""
    print("Testing InventoryModule.parse() function...")
    
    # Create the inventory module instance
    inventory_module = InventoryModule()
    
    # Create mock objects for the required parameters
    inventory = create_mock_inventory()
    loader = create_mock_loader()
    
    # Create a test configuration file
    config_path = create_test_config_file()
    
    try:
        print(f"Calling parse() with config file: {config_path}")
        
        # Call the parse method with proper parameters
        # parse(self, inventory, loader, path, cache=True)
        inventory_module.parse(
            inventory=inventory,
            loader=loader, 
            path=config_path,
            cache=True
        )
        
        print("✅ parse() method called successfully!")
        
        # Print some information about what was called
        print(f"📊 Inventory add_host called {inventory.add_host.call_count} times")
        print(f"📊 Inventory set_variable called {inventory.set_variable.call_count} times")
        print(f"📊 Inventory add_group called {inventory.add_group.call_count} times")
        
        if inventory.add_host.call_count > 0:
            print("🏠 Hosts added:")
            for call in inventory.add_host.call_args_list:
                print(f"   - {call[0][0]}")
        
        if inventory.set_variable.call_count > 0:
            print("🔧 Variables set:")
            for call in inventory.set_variable.call_args_list[:5]:  # Show first 5
                print(f"   - {call[0][0]}: {call[0][1]} = {call[0][2]}")
            if inventory.set_variable.call_count > 5:
                print(f"   ... and {inventory.set_variable.call_count - 5} more")
                
    except Exception as e:
        print(f"❌ Error calling parse(): {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        # Clean up the test config file
        if os.path.exists(config_path):
            # os.remove(config_path)
            print(f"🧹 Cleaned up test config file: {config_path}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
