..
  Copyright (c) Ansible Project
  GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
  SPDX-License-Identifier: GPL-3.0-or-later

Merging lists of dictionaries
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

If you have two or more lists of dictionaries and want to combine them into a list of merged dictionaries, where the dictionaries are merged by an attribute, you can use the :ansplugin:`community.general.lists_mergeby <community.general.lists_mergeby#filter>` filter.

.. note:: The output of the examples in this section use the YAML callback plugin. Quoting: "Ansible output that can be quite a bit easier to read than the default JSON formatting." See the documentation for the :ansplugin:`community.general.yaml callback plugin <community.general.yaml#callback>`.

Let us use the lists below in the following examples:

.. code-block:: yaml

  {{ lookup('file', 'default-common.yml') | split('\n') | reject('match', '^(#|---)') | join ('\n')  | indent(2) }}

{% for i in examples[0:2] %}
{% if i.title | d('', true) | length > 0 %}
{{ i.title }}
{{ "%s" % ('"' * i.title|length) }}
{% endif %}
{{ i.description }}

.. code-block:: {{ i.lang }}

  {{ lookup('file', i.file) | split('\n') | reject('match', '^(#|---)') | join ('\n') | indent(2) }}

{% endfor %}

.. versionadded:: 2.0.0

{% for i in examples[2:6] %}
{% if i.title | d('', true) | length > 0 %}
{{ i.title }}
{{ "%s" % ('"' * i.title|length) }}
{% endif %}
{{ i.description }}

.. code-block:: {{ i.lang }}

  {{ lookup('file', i.file) | split('\n') | reject('match', '^(#|---)') | join ('\n') | indent(2) }}

{% endfor %}

The filter also accepts two optional parameters: :ansopt:`community.general.lists_mergeby#filter:recursive` and :ansopt:`community.general.lists_mergeby#filter:list_merge`. This is available since community.general 4.4.0.

**recursive**
    Is a boolean, default to ``false``. Should the :ansplugin:`community.general.lists_mergeby#filter` filter recursively merge nested hashes. Note: It does not depend on the value of the ``hash_behaviour`` setting in ``ansible.cfg``.

**list_merge**
    Is a string, its possible values are :ansval:`replace` (default), :ansval:`keep`, :ansval:`append`, :ansval:`prepend`, :ansval:`append_rp` or :ansval:`prepend_rp`. It modifies the behaviour of :ansplugin:`community.general.lists_mergeby#filter` when the hashes to merge contain arrays/lists.

The examples below set :ansopt:`community.general.lists_mergeby#filter:recursive=true` and display the differences among all six options of :ansopt:`community.general.lists_mergeby#filter:list_merge`. Functionality of the parameters is exactly the same as in the filter :ansplugin:`ansible.builtin.combine#filter`. See :ref:`Combining hashes/dictionaries <combine_filter>` to learn details about these options.

Let us use the lists below in the following examples

.. code-block:: yaml

  {{ lookup('file', 'default-recursive-true.yml') | split('\n') | reject('match', '^(#|---)') | join ('\n') |indent(2) }}

{% for i in examples[6:] %}
{% if i.title | d('', true) | length > 0 %}
{{ i.title }}
{{ "%s" % ('"' * i.title|length) }}
{% endif %}
{{ i.description }}

.. code-block:: {{ i.lang }}

  {{ lookup('file', i.file) | split('\n') | reject('match', '^(#|---)') | join ('\n') |indent(2) }}

{% endfor %}
