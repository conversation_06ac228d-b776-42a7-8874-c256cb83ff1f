---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: 7. Merge recursive by 'name', append lists 'remove present'
  include_vars:
    dir: example-007_vars
- debug:
    var: list3
  when: debug|d(false) | bool
- template:
    src: list3.out.j2
    dest: example-007.out
