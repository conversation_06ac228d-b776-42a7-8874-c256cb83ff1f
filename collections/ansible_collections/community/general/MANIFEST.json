{"collection_info": {"namespace": "community", "name": "general", "version": "11.0.0", "authors": ["Ansible (https://github.com/ansible)"], "readme": "README.md", "tags": ["community"], "description": "The community.general collection is a part of the Ansible package and includes many modules and plugins supported by Ansible community which are not part of more specialized community collections.", "license": [], "license_file": "COPYING", "dependencies": {}, "repository": "https://github.com/ansible-collections/community.general", "documentation": "https://docs.ansible.com/ansible/latest/collections/community/general/", "homepage": "https://github.com/ansible-collections/community.general", "issues": "https://github.com/ansible-collections/community.general/issues"}, "file_manifest_file": {"name": "FILES.json", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "0a5830db1dfe42e6e3525ac4b5d693286f4e5a4f5f174e37eec54f486bc0da80", "format": 1}, "format": 1}