---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# For the comprehensive list of the inputs supported by the ansible-community/ansible-test-gh-action GitHub Action, see
# https://github.com/marketplace/actions/ansible-test

name: EOL CI
"on":
  # Run EOL CI against all pushes (direct commits, also merged PRs), Pull Requests
  push:
    branches:
      - main
      - stable-*
  pull_request:
  # Run EOL CI once per day (at 08:00 UTC)
  schedule:
    - cron: '0 8 * * *'

concurrency:
  # Make sure there is at most one active run per PR, but do not cancel any non-PR runs
  group: ${{ github.workflow }}-${{ (github.head_ref && github.event.number) || github.run_id }}
  cancel-in-progress: true

jobs:
  sanity:
    name: <PERSON><PERSON> Sanity (Ⓐ${{ matrix.ansible }})
    strategy:
      matrix:
        ansible:
          - '2.16'
    runs-on: ubuntu-latest
    steps:
      - name: Perform sanity testing
        uses: felixfontein/ansible-test-gh-action@main
        with:
          ansible-core-version: stable-${{ matrix.ansible }}
          codecov-token: ${{ secrets.CODECOV_TOKEN }}
          coverage: ${{ github.event_name == 'schedule' && 'always' || 'never' }}
          pull-request-change-detection: 'true'
          testing-type: sanity
          pre-test-cmd: >-
            git clone --depth=1 --single-branch https://github.com/ansible-collections/community.internal_test_tools.git ../../community/internal_test_tools

  units:
    runs-on: ubuntu-latest
    name: EOL Units (Ⓐ${{ matrix.ansible }}+py${{ matrix.python }})
    strategy:
      # As soon as the first unit test fails, cancel the others to free up the CI queue
      fail-fast: true
      matrix:
        ansible:
          - ''
        python:
          - ''
        exclude:
          - ansible: ''
        include:
          - ansible: '2.16'
            python: '2.7'
          - ansible: '2.16'
            python: '3.6'
          - ansible: '2.16'
            python: '3.11'

    steps:
      - name: >-
          Perform unit testing against
          Ansible version ${{ matrix.ansible }}
        uses: felixfontein/ansible-test-gh-action@main
        with:
          ansible-core-version: stable-${{ matrix.ansible }}
          codecov-token: ${{ secrets.CODECOV_TOKEN }}
          coverage: ${{ github.event_name == 'schedule' && 'always' || 'never' }}
          pre-test-cmd: >-
            mkdir -p ../../ansible
            ;
            git clone --depth=1 --single-branch https://github.com/ansible-collections/community.internal_test_tools.git ../../community/internal_test_tools
          pull-request-change-detection: 'true'
          target-python-version: ${{ matrix.python }}
          testing-type: units

  integration:
    runs-on: ubuntu-latest
    name: EOL I (Ⓐ${{ matrix.ansible }}+${{ matrix.docker }}+py${{ matrix.python }}:${{ matrix.target }})
    strategy:
      fail-fast: false
      matrix:
        ansible:
          - ''
        docker:
          - ''
        python:
          - ''
        target:
          - ''
        exclude:
          - ansible: ''
        include:
          # 2.16
          # CentOS 7 does not work in GHA, that's why it's not listed here.
          - ansible: '2.16'
            docker: fedora38
            python: ''
            target: azp/posix/1/
          - ansible: '2.16'
            docker: fedora38
            python: ''
            target: azp/posix/2/
          - ansible: '2.16'
            docker: fedora38
            python: ''
            target: azp/posix/3/
          - ansible: '2.16'
            docker: opensuse15
            python: ''
            target: azp/posix/1/
          - ansible: '2.16'
            docker: opensuse15
            python: ''
            target: azp/posix/2/
          - ansible: '2.16'
            docker: opensuse15
            python: ''
            target: azp/posix/3/
          - ansible: '2.16'
            docker: alpine3
            python: ''
            target: azp/posix/1/
          - ansible: '2.16'
            docker: alpine3
            python: ''
            target: azp/posix/2/
          - ansible: '2.16'
            docker: alpine3
            python: ''
            target: azp/posix/3/
          # Right now all generic tests are disabled. Uncomment when at least one of them is re-enabled.
          # - ansible: '2.16'
          #   docker: default
          #   python: '2.7'
          #   target: azp/generic/1/
          # - ansible: '2.16'
          #   docker: default
          #   python: '3.6'
          #   target: azp/generic/1/
          # - ansible: '2.16'
          #   docker: default
          #   python: '3.11'
          #   target: azp/generic/1/

    steps:
      - name: >-
          Perform integration testing against
          Ansible version ${{ matrix.ansible }}
          under Python ${{ matrix.python }}
        uses: felixfontein/ansible-test-gh-action@main
        with:
          ansible-core-version: stable-${{ matrix.ansible }}
          codecov-token: ${{ secrets.CODECOV_TOKEN }}
          coverage: ${{ github.event_name == 'schedule' && 'always' || 'never' }}
          docker-image: ${{ matrix.docker }}
          integration-continue-on-error: 'false'
          integration-diff: 'false'
          integration-retry-on-error: 'true'
          # TODO: remove "--branch stable-2" from community.crypto install once we're only using ansible-core 2.17 or newer!
          pre-test-cmd: >-
            mkdir -p ../../ansible
            ;
            git clone --depth=1 --single-branch https://github.com/ansible-collections/ansible.posix.git ../../ansible/posix
            ;
            git clone --depth=1 --single-branch --branch stable-2 https://github.com/ansible-collections/community.crypto.git ../../community/crypto
            ;
            git clone --depth=1 --single-branch https://github.com/ansible-collections/community.docker.git ../../community/docker
            ;
            git clone --depth=1 --single-branch https://github.com/ansible-collections/community.internal_test_tools.git ../../community/internal_test_tools
          pull-request-change-detection: 'true'
          target: ${{ matrix.target }}
          target-python-version: ${{ matrix.python }}
          testing-type: integration
