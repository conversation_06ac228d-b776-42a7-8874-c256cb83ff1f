---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

name: Feature request
description: Suggest an idea for this project

body:
  - type: markdown
    attributes:
      value: |
        ⚠
        Verify first that your issue is not [already reported on GitHub][issue search].
        Also test if the latest release and devel branch are affected too.
        *Complete **all** sections as described, this form is processed automatically.*

        [issue search]: https://github.com/ansible-collections/community.general/search?q=is%3Aissue&type=issues


  - type: textarea
    attributes:
      label: Summary
      description: Describe the new feature/improvement briefly below.
      placeholder: >-
        I am trying to do X with the collection from the main branch on GitHub and
        I think that implementing a feature Y would be very helpful for me and
        every other user of community.general because of Z.
    validations:
      required: true

  - type: dropdown
    attributes:
      label: Issue Type
      # FIXME: Once GitHub allows defining the default choice, update this
      options:
        - Feature Idea
    validations:
      required: true

  - type: input
    attributes:
      label: Component Name
      description: >-
        Write the short name of the module or plugin, or which other part(s) of the collection this feature affects.
        *use your best guess if unsure*. Do not include `community.general.`!
      placeholder: dnf, apt, yum, pip, user etc.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Additional Information
      description: |
        Describe how the feature would be used, why it is needed and what it would solve.

        **HINT:** You can paste https://gist.github.com links for larger files.
      value: |
        <!--- Paste example playbooks or commands between quotes below -->
        ```yaml (paste below)

        ```
    validations:
      required: false
  - type: checkboxes
    attributes:
      label: Code of Conduct
      description: |
        Read the [Ansible Code of Conduct](https://docs.ansible.com/ansible/latest/community/code_of_conduct.html?utm_medium=github&utm_source=issue_form--ansible-collections) first.
      options:
        - label: I agree to follow the Ansible Code of Conduct
          required: true
...
