---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

name: Bug report
description: Create a report to help us improve

body:
  - type: markdown
    attributes:
      value: |
        ⚠
        Verify first that your issue is not [already reported on GitHub][issue search].
        Also test if the latest release and devel branch are affected too.
        *Complete **all** sections as described, this form is processed automatically.*

        [issue search]: https://github.com/ansible-collections/community.general/search?q=is%3Aissue&type=issues


  - type: textarea
    attributes:
      label: Summary
      description: Explain the problem briefly below.
      placeholder: >-
        When I try to do X with the collection from the main branch on GitHub, Y
        breaks in a way Z under the env E. Here are all the details I know
        about this problem...
    validations:
      required: true

  - type: dropdown
    attributes:
      label: Issue Type
      # FIXME: Once GitHub allows defining the default choice, update this
      options:
        - Bug Report
    validations:
      required: true

  - type: textarea
    attributes:
      # For smaller collections we could use a multi-select and hardcode the list
      # May generate this list via GitHub action and walking files under https://github.com/ansible-collections/community.general/tree/main/plugins
      # Select from list, filter as you type (`mysql` would only show the 3 mysql components)
      # OR freeform - doesn't seem to be supported in adaptivecards
      label: Component Name
      description: >-
        Write the short name of the module, plugin, task or feature below,
        *use your best guess if unsure*. Do not include `community.general.`!
      placeholder: dnf, apt, yum, pip, user etc.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Ansible Version
      description: >-
        Paste verbatim output from `ansible --version` between
        tripple backticks.
      value: |
        ```console (paste below)
        $ ansible --version

        ```
    validations:
      required: true

  - type: textarea
    attributes:
      label: Community.general Version
      description: >-
        Paste verbatim output from "ansible-galaxy collection list community.general"
        between tripple backticks.
      value: |
        ```console (paste below)
        $ ansible-galaxy collection list community.general

        ```
    validations:
      required: true

  - type: textarea
    attributes:
      label: Configuration
      description: >-
        If this issue has an example piece of YAML that can help to reproduce this problem, please provide it.
        This can be a piece of YAML from, e.g., an automation, script, scene or configuration.
        Paste verbatim output from `ansible-config dump --only-changed` between quotes
      value: |
        ```console (paste below)
        $ ansible-config dump --only-changed

        ```


  - type: textarea
    attributes:
      label: OS / Environment
      description: >-
        Provide all relevant information below, e.g. target OS versions,
        network device firmware, etc.
      placeholder: RHEL 8, CentOS Stream etc.
    validations:
      required: false


  - type: textarea
    attributes:
      label: Steps to Reproduce
      description: |
        Describe exactly how to reproduce the problem, using a minimal test-case. It would *really* help us understand your problem if you could also passed any playbooks, configs and commands you used.

        **HINT:** You can paste https://gist.github.com links for larger files.
      value: |
        <!--- Paste example playbooks or commands between quotes below -->
        ```yaml (paste below)

        ```
    validations:
      required: true

  - type: textarea
    attributes:
      label: Expected Results
      description: >-
        Describe what you expected to happen when running the steps above.
      placeholder: >-
        I expected X to happen because I assumed Y.
        that it did not.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Actual Results
      description: |
        Describe what actually happened. If possible run with extra verbosity (`-vvvv`).

        Paste verbatim command output between quotes.
      value: |
        ```console (paste below)

        ```
  - type: checkboxes
    attributes:
      label: Code of Conduct
      description: |
        Read the [Ansible Code of Conduct](https://docs.ansible.com/ansible/latest/community/code_of_conduct.html?utm_medium=github&utm_source=issue_form--ansible-collections) first.
      options:
        - label: I agree to follow the Ansible Code of Conduct
          required: true
...
