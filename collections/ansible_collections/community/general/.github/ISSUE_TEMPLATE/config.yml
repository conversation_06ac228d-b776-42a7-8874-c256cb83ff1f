---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Ref: https://help.github.com/en/github/building-a-strong-community/configuring-issue-templates-for-your-repository#configuring-the-template-chooser
blank_issues_enabled: false  # default: true
contact_links:
  - name: Security bug report
    url: https://docs.ansible.com/ansible-core/devel/community/reporting_bugs_and_features.html?utm_medium=github&utm_source=issue_template_chooser_ansible_collections
    about: |
      Please learn how to report security vulnerabilities here.

      For all security related bugs, email <EMAIL>
      instead of using this issue tracker and you will receive
      a prompt response.

      For more information, see
      https://docs.ansible.com/ansible/latest/community/reporting_bugs_and_features.html
  - name: Ansible Code of Conduct
    url: https://docs.ansible.com/ansible/latest/community/code_of_conduct.html?utm_medium=github&utm_source=issue_template_chooser_ansible_collections
    about: Be nice to other members of the community.
  - name: Talks to the community
    url: https://docs.ansible.com/ansible/latest/community/communication.html?utm_medium=github&utm_source=issue_template_chooser#mailing-list-information
    about: Please ask and answer usage questions here
  - name: Working groups
    url: https://github.com/ansible/community/wiki
    about: Interested in improving a specific area? Become a part of a working group!
  - name: For Enterprise
    url: https://www.ansible.com/products/engine?utm_medium=github&utm_source=issue_template_chooser_ansible_collections
    about: Red Hat offers support for the Ansible Automation Platform
