---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

name: Documentation Report
description: Ask us about docs
# NOTE: issue body is enabled to allow screenshots

body:
  - type: markdown
    attributes:
      value: |
        ⚠
        Verify first that your issue is not [already reported on GitHub][issue search].
        Also test if the latest release and devel branch are affected too.
        *Complete **all** sections as described, this form is processed automatically.*

        [issue search]: https://github.com/ansible-collections/community.general/search?q=is%3Aissue&type=issues


  - type: textarea
    attributes:
      label: Summary
      description: |
        Explain the problem briefly below, add suggestions to wording or structure.

        **HINT:** Did you know the documentation has an `Edit on GitHub` link on every page?
      placeholder: >-
        I was reading the Collection documentation of version X and I'm having
        problems understanding Y. It would be very helpful if that got
        rephrased as Z.
    validations:
      required: true

  - type: dropdown
    attributes:
      label: Issue Type
      # FIXME: Once GitHub allows defining the default choice, update this
      options:
        - Documentation Report
    validations:
      required: true

  - type: input
    attributes:
      label: Component Name
      description: >-
        Write the short name of the file, module, plugin, task or feature below,
        *use your best guess if unsure*. Do not include `community.general.`!
      placeholder: mysql_user
    validations:
      required: true

  - type: textarea
    attributes:
      label: Ansible Version
      description: >-
        Paste verbatim output from `ansible --version` between
        tripple backticks.
      value: |
        ```console (paste below)
        $ ansible --version

        ```
    validations:
      required: false

  - type: textarea
    attributes:
      label: Community.general Version
      description: >-
        Paste verbatim output from "ansible-galaxy collection list community.general"
        between tripple backticks.
      value: |
        ```console (paste below)
        $ ansible-galaxy collection list community.general

        ```
    validations:
      required: true

  - type: textarea
    attributes:
      label: Configuration
      description: >-
        Paste verbatim output from `ansible-config dump --only-changed` between quotes.
      value: |
        ```console (paste below)
        $ ansible-config dump --only-changed

        ```
    validations:
      required: false

  - type: textarea
    attributes:
      label: OS / Environment
      description: >-
        Provide all relevant information below, e.g. OS version,
        browser, etc.
      placeholder: Fedora 33, Firefox etc.
    validations:
      required: false

  - type: textarea
    attributes:
      label: Additional Information
      description: |
        Describe how this improves the documentation, e.g. before/after situation or screenshots.

        **Tip:** It's not possible to upload the screenshot via this field directly but you can use the last textarea in this form to attach them.

        **HINT:** You can paste https://gist.github.com links for larger files.
      placeholder: >-
        When the improvement is applied, it makes it more straightforward
        to understand X.
    validations:
      required: false

  - type: checkboxes
    attributes:
      label: Code of Conduct
      description: |
        Read the [Ansible Code of Conduct](https://docs.ansible.com/ansible/latest/community/code_of_conduct.html?utm_medium=github&utm_source=issue_form--ansible-collections) first.
      options:
        - label: I agree to follow the Ansible Code of Conduct
          required: true
...
