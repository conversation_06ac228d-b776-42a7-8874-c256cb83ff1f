# -*- coding: utf-8 -*-
# (c) 2020, <PERSON> <<EMAIL>>
# Copyright (c) 2020, Ansible Project
# Simplified BSD License (see LICENSES/BSD-2-Clause.txt or https://opensource.org/licenses/BSD-2-Clause)
# SPDX-License-Identifier: BSD-2-Clause

from __future__ import absolute_import, division, print_function
__metaclass__ = type

# pylint: disable=unused-import

from ansible_collections.community.general.plugins.module_utils.mh.module_helper import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, StateModuleHelper,
)
from ansible_collections.community.general.plugins.module_utils.mh.exceptions import ModuleHelperException  # noqa: F401
from ansible_collections.community.general.plugins.module_utils.mh.deco import (
    cause_changes, module_fails_on_exception, check_mode_skip, check_mode_skip_returns,
)
