---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

DOCUMENTATION:
  name: lists_symmetric_difference
  short_description: Symmetric Difference of lists with a predictive order
  version_added: 8.4.0
  description:
    - Provide a unique list containing the symmetric difference of two or more lists.
    - The order of the items in the resulting list is preserved.
  options:
    _input:
      description: A list.
      type: list
      elements: any
      required: true
    flatten:
      description: Whether to remove one hierarchy level from the input list.
      type: boolean
      default: false
  author:
    - <PERSON> (@cfiehe)

EXAMPLES: |
  - name: Return the symmetric difference of list1 and list2.
    ansible.builtin.debug:
      msg: "{{ list1 | community.general.lists_symmetric_difference(list2) }}"
    vars:
      list1: [1, 2, 5, 3, 4, 10]
      list2: [1, 2, 3, 4, 5, 11, 99]
  # => [10, 11, 99]

  - name: Return the symmetric difference of list1, list2 and list3.
    ansible.builtin.debug:
      msg: "{{ [list1, list2, list3] | community.general.lists_symmetric_difference(flatten=true) }}"
    vars:
      list1: [1, 2, 5, 3, 4, 10]
      list2: [1, 2, 3, 4, 5, 11, 99]
      list3: [1, 2, 3, 4, 5, 10, 99, 101]
  # => [11, 1, 2, 3, 4, 5, 101]

RETURN:
  _value:
    description: A unique list containing the symmetric difference of two or more lists.
    type: list
    elements: any
