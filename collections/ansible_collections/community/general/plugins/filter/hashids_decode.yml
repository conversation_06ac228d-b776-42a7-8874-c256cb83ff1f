---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

DOCUMENTATION:
  name: hashids_decode
  short_description: Decodes a sequence of numbers from a YouTube-like hash
  version_added: 3.0.0
  author: <PERSON> (@Ajpantuso)
  description:
    - Decodes a sequence of numbers from a YouTube-like hash.
  options:
    _input:
      description: A YouTube-like hash.
      type: string
      required: true
    salt:
      description:
        - String to use as salt when hashing.
      type: str
      default: excel
    alphabet:
      description:
        - String of 16 or more unique characters to produce a hash.
      type: list
      elements: str
    min_length:
      description:
        - Minimum length of hash produced.
      type: integer

EXAMPLES: |
  - name: Convert hash to list of integers
    ansible.builtin.debug:
      msg: "{{ 'o2fXhV' | community.general.hashids_decode }}"
      # Produces: [1, 2, 3]

RETURN:
  _value:
    description: A list of integers.
    type: list
    elements: integer
