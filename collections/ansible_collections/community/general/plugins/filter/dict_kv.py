# -*- coding: utf-8 -*-
# Copyright (C) 2020 <PERSON><PERSON>-<PERSON> (@giner) <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

from __future__ import annotations

DOCUMENTATION = r"""
name: dict_kv
short_description: Convert a value to a dictionary with a single key-value pair
version_added: 1.3.0
author: <PERSON><PERSON>-<PERSON><PERSON><PERSON> (@giner)
description:
  - Convert a value to a dictionary with a single key-value pair.
positional: key
options:
  _input:
    description: The value for the single key-value pair.
    type: any
    required: true
  key:
    description: The key for the single key-value pair.
    type: any
    required: true
"""

EXAMPLES = r"""
- name: Create a one-element dictionary from a value
  ansible.builtin.debug:
    msg: "{{ 'myvalue' | dict_kv('mykey') }}"
    # Produces the dictionary {'mykey': 'myvalue'}
"""

RETURN = r"""
_value:
  description: A dictionary with a single key-value pair.
  type: dictionary
"""


def dict_kv(value, key):
    '''Return a dictionary with a single key-value pair

    Example:

        - hosts: localhost
          gather_facts: false
          vars:
            myvar: myvalue
          tasks:
          - debug:
              msg: "{{ myvar | dict_kv('thatsmyvar') }}"

        produces:

        ok: [localhost] => {
            "msg": {
                "thatsmyvar": "myvalue"
            }
        }

    Example 2:

        - hosts: localhost
          gather_facts: false
          vars:
            common_config:
              type: host
              database: all
            myservers:
            - server1
            - server2
          tasks:
          - debug:
              msg: "{{ myservers | map('dict_kv', 'server') | map('combine', common_config) }}"

        produces:

        ok: [localhost] => {
            "msg": [
                {
                    "database": "all",
                    "server": "server1",
                    "type": "host"
                },
                {
                    "database": "all",
                    "server": "server2",
                    "type": "host"
                }
            ]
        }
    '''
    return {key: value}


class FilterModule(object):
    ''' Query filter '''

    def filters(self):
        return {
            'dict_kv': dict_kv
        }
