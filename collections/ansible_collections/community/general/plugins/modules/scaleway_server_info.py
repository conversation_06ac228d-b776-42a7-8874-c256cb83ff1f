#!/usr/bin/python
# -*- coding: utf-8 -*-
#
# Copyright (c) 2018, <PERSON><PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

from __future__ import (absolute_import, division, print_function)
__metaclass__ = type

DOCUMENTATION = r"""
module: scaleway_server_info
short_description: Gather information about the Scaleway servers available
description:
  - Gather information about the Scaleway servers available.
author:
  - "<PERSON><PERSON> (@Spredzy)"
  - "<PERSON><PERSON> (@remyleone)"
extends_documentation_fragment:
  - community.general.scaleway
  - community.general.attributes
  - community.general.attributes.info_module

options:
  region:
    type: str
    description:
      - Scaleway region to use (for example C(par1)).
    required: true
    choices:
      - ams1
      - EMEA-NL-EVS
      - par1
      - EMEA-FR-PAR1
      - par2
      - EMEA-FR-PAR2
      - waw1
      - EMEA-PL-WAW1
"""

EXAMPLES = r"""
- name: Gather Scaleway servers information
  community.general.scaleway_server_info:
    region: par1
  register: result

- ansible.builtin.debug:
    msg: "{{ result.scaleway_server_info }}"
"""

RETURN = r"""
scaleway_server_info:
  description:
    - Response from Scaleway API.
    - 'For more details please refer to: U(https://developers.scaleway.com/en/products/instance/api/).'
  returned: success
  type: list
  elements: dict
  sample:
    "scaleway_server_info": [
      {
        "arch": "x86_64",
        "boot_type": "local",
        "bootscript": {
          "architecture": "x86_64",
          "bootcmdargs": "LINUX_COMMON scaleway boot=local nbd.max_part=16",
          "default": true,
          "dtb": "",
          "id": "b1e68c26-a19c-4eac-9222-498b22bd7ad9",
          "initrd": "http://*************/initrd/initrd-Linux-x86_64-v3.14.5.gz",
          "kernel": "http://*************/kernel/x86_64-mainline-lts-4.4-4.4.127-rev1/vmlinuz-4.4.127",
          "organization": "11111111-1111-4111-8111-111111111111",
          "public": true,
          "title": "x86_64 mainline 4.4.127 rev1"
        },
        "commercial_type": "START1-XS",
        "creation_date": "2018-08-14T21:36:56.271545+00:00",
        "dynamic_ip_required": false,
        "enable_ipv6": false,
        "extra_networks": [],
        "hostname": "scw-e0d256",
        "id": "12f19bc7-108c-4517-954c-e6b3d0311363",
        "image": {
          "arch": "x86_64",
          "creation_date": "2018-04-26T12:42:21.619844+00:00",
          "default_bootscript": {
            "architecture": "x86_64",
            "bootcmdargs": "LINUX_COMMON scaleway boot=local nbd.max_part=16",
            "default": true,
            "dtb": "",
            "id": "b1e68c26-a19c-4eac-9222-498b22bd7ad9",
            "initrd": "http://*************/initrd/initrd-Linux-x86_64-v3.14.5.gz",
            "kernel": "http://*************/kernel/x86_64-mainline-lts-4.4-4.4.127-rev1/vmlinuz-4.4.127",
            "organization": "11111111-1111-4111-8111-111111111111",
            "public": true,
            "title": "x86_64 mainline 4.4.127 rev1"
          },
          "extra_volumes": [],
          "from_server": null,
          "id": "67375eb1-f14d-4f02-bb42-6119cecbde51",
          "modification_date": "2018-04-26T12:49:07.573004+00:00",
          "name": "Ubuntu Xenial",
          "organization": "51b656e3-4865-41e8-adbc-0c45bdd780db",
          "public": true,
          "root_volume": {
            "id": "020b8d61-3867-4a0e-84a4-445c5393e05d",
            "name": "snapshot-87fc282d-f252-4262-adad-86979d9074cf-2018-04-26_12:42",
            "size": 25000000000,
            "volume_type": "l_ssd"
          },
          "state": "available"
        },
        "ipv6": null,
        "location": {
          "cluster_id": "5",
          "hypervisor_id": "412",
          "node_id": "2",
          "platform_id": "13",
          "zone_id": "par1"
        },
        "maintenances": [],
        "modification_date": "2018-08-14T21:37:28.630882+00:00",
        "name": "scw-e0d256",
        "organization": "3f709602-5e6c-4619-b80c-e841c89734af",
        "private_ip": "*************",
        "protected": false,
        "public_ip": {
          "address": "***************",
          "dynamic": false,
          "id": "ea081794-a581-4495-8451-386ddaf0a451"
        },
        "security_group": {
          "id": "a37379d2-d8b0-4668-9cfb-1233fc436f7e",
          "name": "Default security group"
        },
        "state": "running",
        "state_detail": "booted",
        "tags": [],
        "volumes": {
          "0": {
            "creation_date": "2018-08-14T21:36:56.271545+00:00",
            "export_uri": "device://dev/vda",
            "id": "68386fae-4f55-4fbf-aabb-953036a85872",
            "modification_date": "2018-08-14T21:36:56.271545+00:00",
            "name": "snapshot-87fc282d-f252-4262-adad-86979d9074cf-2018-04-26_12:42",
            "organization": "3f709602-5e6c-4619-b80c-e841c89734af",
            "server": {
              "id": "12f19bc7-108c-4517-954c-e6b3d0311363",
              "name": "scw-e0d256"
            },
            "size": 25000000000,
            "state": "available",
            "volume_type": "l_ssd"
          }
        }
      }
    ]
"""

from ansible.module_utils.basic import AnsibleModule
from ansible_collections.community.general.plugins.module_utils.scaleway import (
    Scaleway,
    ScalewayException,
    scaleway_argument_spec,
    SCALEWAY_LOCATION,
)


class ScalewayServerInfo(Scaleway):

    def __init__(self, module):
        super(ScalewayServerInfo, self).__init__(module)
        self.name = 'servers'

        region = module.params["region"]
        self.module.params['api_url'] = SCALEWAY_LOCATION[region]["api_endpoint"]


def main():
    argument_spec = scaleway_argument_spec()
    argument_spec.update(dict(
        region=dict(required=True, choices=list(SCALEWAY_LOCATION.keys())),
    ))

    module = AnsibleModule(
        argument_spec=argument_spec,
        supports_check_mode=True,
    )

    try:
        module.exit_json(
            scaleway_server_info=ScalewayServerInfo(module).get_resources()
        )
    except ScalewayException as exc:
        module.fail_json(msg=exc.message)


if __name__ == '__main__':
    main()
