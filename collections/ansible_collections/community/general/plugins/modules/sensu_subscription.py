#!/usr/bin/python
# -*- coding: utf-8 -*-

# Copyright (c) 2014, <PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

from __future__ import absolute_import, division, print_function
__metaclass__ = type


DOCUMENTATION = r"""
module: sensu_subscription
short_description: Manage Sensu subscriptions
description:
  - Manage which I(sensu channels) a machine should subscribe to.
deprecated:
  removed_in: 13.0.0
  why: Sensu Core and Sensu Enterprise products have been End of Life since 2019/20.
  alternative: Use Sensu Go and its accompanying collection C(sensu.sensu_go).
extends_documentation_fragment:
  - community.general.attributes
attributes:
  check_mode:
    support: full
  diff_mode:
    support: none
options:
  name:
    type: str
    description:
      - The name of the channel.
    required: true
  state:
    type: str
    description:
      - Whether the machine should subscribe or unsubscribe from the channel.
    choices: ['present', 'absent']
    required: false
    default: present
  path:
    type: str
    description:
      - Path to the subscriptions JSON file.
    required: false
    default: /etc/sensu/conf.d/subscriptions.json
  backup:
    description:
      - Create a backup file (if yes), including the timestamp information so you can get the original file back if you somehow
        clobbered it incorrectly.
    type: bool
    required: false
    default: false
requirements: []
author: Anders Ingemann (@andsens)
"""

RETURN = r"""
reasons:
  description: The reasons why the module changed or did not change something.
  returned: success
  type: list
  sample: ["channel subscription was absent and state is `present'"]
"""

EXAMPLES = r"""
# Subscribe to the nginx channel
- name: Subscribe to nginx checks
  community.general.sensu_subscription: name=nginx

# Unsubscribe from the common checks channel
- name: Unsubscribe from common checks
  community.general.sensu_subscription: name=common state=absent
"""

import json
import traceback

from ansible.module_utils.basic import AnsibleModule
from ansible.module_utils.common.text.converters import to_native


def sensu_subscription(module, path, name, state='present', backup=False):
    changed = False
    reasons = []

    try:
        config = json.load(open(path))
    except IOError as e:
        if e.errno == 2:  # File not found, non-fatal
            if state == 'absent':
                reasons.append('file did not exist and state is `absent\'')
                return changed, reasons
            config = {}
        else:
            module.fail_json(msg=to_native(e), exception=traceback.format_exc())
    except ValueError:
        msg = '{path} contains invalid JSON'.format(path=path)
        module.fail_json(msg=msg)

    if 'client' not in config:
        if state == 'absent':
            reasons.append('`client\' did not exist and state is `absent\'')
            return changed, reasons
        config['client'] = {}
        changed = True
        reasons.append('`client\' did not exist')

    if 'subscriptions' not in config['client']:
        if state == 'absent':
            reasons.append('`client.subscriptions\' did not exist and state is `absent\'')
            return changed, reasons
        config['client']['subscriptions'] = []
        changed = True
        reasons.append('`client.subscriptions\' did not exist')

    if name not in config['client']['subscriptions']:
        if state == 'absent':
            reasons.append('channel subscription was absent')
            return changed, reasons
        config['client']['subscriptions'].append(name)
        changed = True
        reasons.append('channel subscription was absent and state is `present\'')
    else:
        if state == 'absent':
            config['client']['subscriptions'].remove(name)
            changed = True
            reasons.append('channel subscription was present and state is `absent\'')

    if changed and not module.check_mode:
        if backup:
            module.backup_local(path)
        try:
            open(path, 'w').write(json.dumps(config, indent=2) + '\n')
        except IOError as e:
            module.fail_json(msg='Failed to write to file %s: %s' % (path, to_native(e)),
                             exception=traceback.format_exc())

    return changed, reasons


def main():
    arg_spec = {'name': {'type': 'str', 'required': True},
                'path': {'type': 'str', 'default': '/etc/sensu/conf.d/subscriptions.json'},
                'state': {'type': 'str', 'default': 'present', 'choices': ['present', 'absent']},
                'backup': {'type': 'bool', 'default': False},
                }

    module = AnsibleModule(argument_spec=arg_spec,
                           supports_check_mode=True)

    path = module.params['path']
    name = module.params['name']
    state = module.params['state']
    backup = module.params['backup']

    changed, reasons = sensu_subscription(module, path, name, state, backup)

    module.exit_json(path=path, name=name, changed=changed, msg='OK', reasons=reasons)


if __name__ == '__main__':
    main()
