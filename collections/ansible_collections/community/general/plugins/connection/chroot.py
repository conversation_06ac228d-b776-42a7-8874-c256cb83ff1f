# -*- coding: utf-8 -*-
# Based on local.py (c) 2012, <PERSON> <micha<PERSON>.de<PERSON><EMAIL>>
#
# (c) 2013, <PERSON><PERSON> <<EMAIL>>
# (c) 2015, <PERSON><PERSON><PERSON> <<EMAIL>>
# Copyright (c) 2017 Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

from __future__ import annotations

DOCUMENTATION = r"""
author: <PERSON><PERSON> (!UNKNOWN) <<EMAIL>>
name: chroot
short_description: Interact with local chroot
description:
  - Run commands or put/fetch files to an existing chroot on the Ansible controller.
options:
  remote_addr:
    description:
      - The path of the chroot you want to access.
    type: string
    default: inventory_hostname
    vars:
      - name: inventory_hostname
      - name: ansible_host
  executable:
    description:
      - User specified executable shell.
    type: string
    ini:
      - section: defaults
        key: executable
    env:
      - name: ANSIBLE_EXECUTABLE
    vars:
      - name: ansible_executable
    default: /bin/sh
  chroot_exe:
    description:
      - User specified chroot binary.
    type: string
    ini:
      - section: chroot_connection
        key: exe
    env:
      - name: ANSIBLE_CHROOT_EXE
    vars:
      - name: ansible_chroot_exe
    default: chroot
  disable_root_check:
    description:
      - Do not check that the user is not root.
    ini:
      - section: chroot_connection
        key: disable_root_check
    env:
      - name: ANSIBLE_CHROOT_DISABLE_ROOT_CHECK
    vars:
      - name: ansible_chroot_disable_root_check
    default: false
    type: bool
    version_added: 7.3.0
"""

EXAMPLES = r"""
- hosts: chroots
  connection: community.general.chroot
  tasks:
    - debug:
        msg: "This is coming from chroot environment"
"""

import os
import os.path
import subprocess
import traceback

from ansible.errors import AnsibleError
from ansible.module_utils.basic import is_executable
from ansible.module_utils.common.process import get_bin_path
from ansible.module_utils.six.moves import shlex_quote
from ansible.module_utils.common.text.converters import to_bytes
from ansible.plugins.connection import ConnectionBase, BUFSIZE
from ansible.utils.display import Display

display = Display()


class Connection(ConnectionBase):
    """ Local chroot based connections """

    transport = 'community.general.chroot'
    has_pipelining = True
    # su currently has an undiagnosed issue with calculating the file
    # checksums (so copy, for instance, doesn't work right)
    # Have to look into that before re-enabling this
    has_tty = False

    default_user = 'root'

    def __init__(self, play_context, new_stdin, *args, **kwargs):
        super(Connection, self).__init__(play_context, new_stdin, *args, **kwargs)

        self.chroot = self._play_context.remote_addr

        # do some trivial checks for ensuring 'host' is actually a chroot'able dir
        if not os.path.isdir(self.chroot):
            raise AnsibleError(f"{self.chroot} is not a directory")

        chrootsh = os.path.join(self.chroot, 'bin/sh')
        # Want to check for a usable bourne shell inside the chroot.
        # is_executable() == True is sufficient.  For symlinks it
        # gets really complicated really fast.  So we punt on finding that
        # out.  As long as it is a symlink we assume that it will work
        if not (is_executable(chrootsh) or (os.path.lexists(chrootsh) and os.path.islink(chrootsh))):
            raise AnsibleError(f"{self.chroot} does not look like a chrootable dir (/bin/sh missing)")

    def _connect(self):
        """ connect to the chroot """
        if not self.get_option('disable_root_check') and os.geteuid() != 0:
            raise AnsibleError(
                "chroot connection requires running as root. "
                "You can override this check with the `disable_root_check` option.")

        if os.path.isabs(self.get_option('chroot_exe')):
            self.chroot_cmd = self.get_option('chroot_exe')
        else:
            try:
                self.chroot_cmd = get_bin_path(self.get_option('chroot_exe'))
            except ValueError as e:
                raise AnsibleError(str(e))

        super(Connection, self)._connect()
        if not self._connected:
            display.vvv("THIS IS A LOCAL CHROOT DIR", host=self.chroot)
            self._connected = True

    def _buffered_exec_command(self, cmd, stdin=subprocess.PIPE):
        """ run a command on the chroot.  This is only needed for implementing
        put_file() get_file() so that we don't have to read the whole file
        into memory.

        compared to exec_command() it looses some niceties like being able to
        return the process's exit code immediately.
        """
        executable = self.get_option('executable')
        local_cmd = [self.chroot_cmd, self.chroot, executable, '-c', cmd]

        display.vvv(f"EXEC {local_cmd}", host=self.chroot)
        local_cmd = [to_bytes(i, errors='surrogate_or_strict') for i in local_cmd]
        p = subprocess.Popen(local_cmd, shell=False, stdin=stdin,
                             stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        return p

    def exec_command(self, cmd, in_data=None, sudoable=False):
        """ run a command on the chroot """
        super(Connection, self).exec_command(cmd, in_data=in_data, sudoable=sudoable)

        p = self._buffered_exec_command(cmd)

        stdout, stderr = p.communicate(in_data)
        return p.returncode, stdout, stderr

    @staticmethod
    def _prefix_login_path(remote_path):
        """ Make sure that we put files into a standard path

            If a path is relative, then we need to choose where to put it.
            ssh chooses $HOME but we aren't guaranteed that a home dir will
            exist in any given chroot.  So for now we're choosing "/" instead.
            This also happens to be the former default.

            Can revisit using $HOME instead if it is a problem
        """
        if not remote_path.startswith(os.path.sep):
            remote_path = os.path.join(os.path.sep, remote_path)
        return os.path.normpath(remote_path)

    def put_file(self, in_path, out_path):
        """ transfer a file from local to chroot """
        super(Connection, self).put_file(in_path, out_path)
        display.vvv(f"PUT {in_path} TO {out_path}", host=self.chroot)

        out_path = shlex_quote(self._prefix_login_path(out_path))
        try:
            with open(to_bytes(in_path, errors='surrogate_or_strict'), 'rb') as in_file:
                if not os.fstat(in_file.fileno()).st_size:
                    count = ' count=0'
                else:
                    count = ''
                try:
                    p = self._buffered_exec_command(f'dd of={out_path} bs={BUFSIZE}{count}', stdin=in_file)
                except OSError:
                    raise AnsibleError("chroot connection requires dd command in the chroot")
                try:
                    stdout, stderr = p.communicate()
                except Exception:
                    traceback.print_exc()
                    raise AnsibleError(f"failed to transfer file {in_path} to {out_path}")
                if p.returncode != 0:
                    raise AnsibleError(f"failed to transfer file {in_path} to {out_path}:\n{stdout}\n{stderr}")
        except IOError:
            raise AnsibleError(f"file or module does not exist at: {in_path}")

    def fetch_file(self, in_path, out_path):
        """ fetch a file from chroot to local """
        super(Connection, self).fetch_file(in_path, out_path)
        display.vvv(f"FETCH {in_path} TO {out_path}", host=self.chroot)

        in_path = shlex_quote(self._prefix_login_path(in_path))
        try:
            p = self._buffered_exec_command(f'dd if={in_path} bs={BUFSIZE}')
        except OSError:
            raise AnsibleError("chroot connection requires dd command in the chroot")

        with open(to_bytes(out_path, errors='surrogate_or_strict'), 'wb+') as out_file:
            try:
                chunk = p.stdout.read(BUFSIZE)
                while chunk:
                    out_file.write(chunk)
                    chunk = p.stdout.read(BUFSIZE)
            except Exception:
                traceback.print_exc()
                raise AnsibleError(f"failed to transfer file {in_path} to {out_path}")
            stdout, stderr = p.communicate()
            if p.returncode != 0:
                raise AnsibleError(f"failed to transfer file {in_path} to {out_path}:\n{stdout}\n{stderr}")

    def close(self):
        """ terminate the connection; nothing to do here """
        super(Connection, self).close()
        self._connected = False
