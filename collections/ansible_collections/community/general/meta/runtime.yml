---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

requires_ansible: '>=2.16.0'
action_groups:
  consul:
    - consul_agent_check
    - consul_agent_service
    - consul_auth_method
    - consul_binding_rule
    - consul_policy
    - consul_role
    - consul_session
    - consul_token
  proxmox:
    - metadata:
        extend_group:
          - community.proxmox.proxmox
  keycloak:
    - keycloak_authentication
    - keycloak_authentication_required_actions
    - keycloak_authz_authorization_scope
    - keycloak_authz_custom_policy
    - keycloak_authz_permission
    - keycloak_authz_permission_info
    - keycloak_client
    - keycloak_client_rolemapping
    - keycloak_client_rolescope
    - keycloak_clientscope
    - keycloak_clientscope_type
    - keycloak_clientsecret_info
    - keycloak_clientsecret_regenerate
    - keycloak_clienttemplate
    - keycloak_component
    - keycloak_component_info
    - keycloak_group
    - keycloak_identity_provider
    - keycloak_realm
    - keycloak_realm_key
    - keycloak_realm_keys_metadata_info
    - keycloak_realm_rolemapping
    - keycloak_role
    - keycloak_user
    - keycloak_user_federation
    - keycloak_user_rolemapping
    - keycloak_userprofile
plugin_routing:
  callback:
    actionable:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use the 'default' callback plugin with 'display_skipped_hosts
          = no' and 'display_ok_hosts = no' options.
    full_skip:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use the 'default' callback plugin with 'display_skipped_hosts
          = no' option.
    hipchat:
      tombstone:
        removal_version: 10.0.0
        warning_text: The hipchat service has been discontinued and the self-hosted variant has been End of Life since 2020.
    osx_say:
      redirect: community.general.say
    stderr:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use the 'default' callback plugin with 'display_failed_stderr
          = yes' option.
    yaml:
      deprecation:
        removal_version: 12.0.0
        warning_text: >-
          The plugin has been superseded by the the option `result_format=yaml` in callback plugin ansible.builtin.default from ansible-core 2.13 onwards.
  connection:
    docker:
      redirect: community.docker.docker
    oc:
      redirect: community.okd.oc
    proxmox_pct_remote:
      redirect: community.proxmox.proxmox_pct_remote
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
  lookup:
    gcp_storage_file:
      redirect: community.google.gcp_storage_file
    hashi_vault:
      redirect: community.hashi_vault.hashi_vault
    manifold:
      tombstone:
        removal_version: 11.0.0
        warning_text: Company was acquired in 2021 and service was ceased afterwards.
    nios:
      redirect: infoblox.nios_modules.nios_lookup
    nios_next_ip:
      redirect: infoblox.nios_modules.nios_next_ip
    nios_next_network:
      redirect: infoblox.nios_modules.nios_next_network
  modules:
    ali_instance_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.ali_instance_info instead.
    atomic_container:
      deprecation:
        removal_version: 13.0.0
        warning_text: Project Atomic was sunset by the end of 2019.
    atomic_host:
      deprecation:
        removal_version: 13.0.0
        warning_text: Project Atomic was sunset by the end of 2019.
    atomic_image:
      deprecation:
        removal_version: 13.0.0
        warning_text: Project Atomic was sunset by the end of 2019.
    cisco_spark:
      redirect: community.general.cisco_webex
    clc_alert_policy:
      tombstone:
        removal_version: 11.0.0
        warning_text: CenturyLink Cloud services went EOL in September 2023.
    clc_blueprint_package:
      tombstone:
        removal_version: 11.0.0
        warning_text: CenturyLink Cloud services went EOL in September 2023.
    clc_firewall_policy:
      tombstone:
        removal_version: 11.0.0
        warning_text: CenturyLink Cloud services went EOL in September 2023.
    clc_group:
      tombstone:
        removal_version: 11.0.0
        warning_text: CenturyLink Cloud services went EOL in September 2023.
    clc_loadbalancer:
      tombstone:
        removal_version: 11.0.0
        warning_text: CenturyLink Cloud services went EOL in September 2023.
    clc_modify_server:
      tombstone:
        removal_version: 11.0.0
        warning_text: CenturyLink Cloud services went EOL in September 2023.
    clc_publicip:
      tombstone:
        removal_version: 11.0.0
        warning_text: CenturyLink Cloud services went EOL in September 2023.
    clc_server:
      tombstone:
        removal_version: 11.0.0
        warning_text: CenturyLink Cloud services went EOL in September 2023.
    clc_server_snapshot:
      tombstone:
        removal_version: 11.0.0
        warning_text: CenturyLink Cloud services went EOL in September 2023.
    consul_acl:
      tombstone:
        removal_version: 10.0.0
        warning_text: Use community.general.consul_token and/or community.general.consul_policy instead.
    docker_compose:
      redirect: community.docker.docker_compose
    docker_config:
      redirect: community.docker.docker_config
    docker_container:
      redirect: community.docker.docker_container
    docker_container_info:
      redirect: community.docker.docker_container_info
    docker_host_info:
      redirect: community.docker.docker_host_info
    docker_image:
      redirect: community.docker.docker_image
    docker_image_facts:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use community.docker.docker_image_info instead.
    docker_image_info:
      redirect: community.docker.docker_image_info
    docker_login:
      redirect: community.docker.docker_login
    docker_network:
      redirect: community.docker.docker_network
    docker_network_info:
      redirect: community.docker.docker_network_info
    docker_node:
      redirect: community.docker.docker_node
    docker_node_info:
      redirect: community.docker.docker_node_info
    docker_prune:
      redirect: community.docker.docker_prune
    docker_secret:
      redirect: community.docker.docker_secret
    docker_service:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use community.docker.docker_compose instead.
    docker_stack:
      redirect: community.docker.docker_stack
    docker_stack_info:
      redirect: community.docker.docker_stack_info
    docker_stack_task_info:
      redirect: community.docker.docker_stack_task_info
    docker_swarm:
      redirect: community.docker.docker_swarm
    docker_swarm_info:
      redirect: community.docker.docker_swarm_info
    docker_swarm_service:
      redirect: community.docker.docker_swarm_service
    docker_swarm_service_info:
      redirect: community.docker.docker_swarm_service_info
    docker_volume:
      redirect: community.docker.docker_volume
    docker_volume_info:
      redirect: community.docker.docker_volume_info
    facter:
      deprecation:
        removal_version: 12.0.0
        warning_text: Use community.general.facter_facts instead.
    flowdock:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on HTTPS APIs that do not exist anymore and
          there is no clear path to update.
    foreman:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use the modules from the theforeman.foreman collection instead.
    gc_storage:
      redirect: community.google.gc_storage
    gcdns_record:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use google.cloud.gcp_dns_resource_record_set instead.
    gcdns_zone:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use google.cloud.gcp_dns_managed_zone instead.
    gce:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use google.cloud.gcp_compute_instance instead.
    gce_eip:
      redirect: community.google.gce_eip
    gce_img:
      redirect: community.google.gce_img
    gce_instance_template:
      redirect: community.google.gce_instance_template
    gce_labels:
      redirect: community.google.gce_labels
    gce_lb:
      redirect: community.google.gce_lb
    gce_mig:
      redirect: community.google.gce_mig
    gce_net:
      redirect: community.google.gce_net
    gce_pd:
      redirect: community.google.gce_pd
    gce_snapshot:
      redirect: community.google.gce_snapshot
    gce_tag:
      redirect: community.google.gce_tag
    gcp_backend_service:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use google.cloud.gcp_compute_backend_service instead.
    gcp_forwarding_rule:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use google.cloud.gcp_compute_forwarding_rule or google.cloud.gcp_compute_global_forwarding_rule
          instead.
    gcp_healthcheck:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use google.cloud.gcp_compute_health_check, google.cloud.gcp_compute_http_health_check
          or google.cloud.gcp_compute_https_health_check instead.
    gcp_target_proxy:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use google.cloud.gcp_compute_target_http_proxy instead.
    gcp_url_map:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use google.cloud.gcp_compute_url_map instead.
    gcpubsub:
      redirect: community.google.gcpubsub
    gcpubsub_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.google.gcpubsub_info instead.
    gcpubsub_info:
      redirect: community.google.gcpubsub_info
    gcspanner:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use google.cloud.gcp_spanner_database and/or google.cloud.gcp_spanner_instance
          instead.
    github_hooks:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use community.general.github_webhook and community.general.github_webhook_info
          instead.
    hana_query:
      redirect: community.sap_libs.sap_hdbsql
    hetzner_failover_ip:
      redirect: community.hrobot.failover_ip
    hetzner_failover_ip_info:
      redirect: community.hrobot.failover_ip_info
    hetzner_firewall:
      redirect: community.hrobot.firewall
    hetzner_firewall_info:
      redirect: community.hrobot.firewall_info
    hipchat:
      tombstone:
        removal_version: 11.0.0
        warning_text: The hipchat service has been discontinued and the self-hosted variant has been End of Life since 2020.
    hpilo_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.hpilo_info instead.
    idrac_firmware:
      redirect: dellemc.openmanage.idrac_firmware
    idrac_redfish_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.idrac_redfish_info instead.
    idrac_server_config_profile:
      redirect: dellemc.openmanage.idrac_server_config_profile
    jenkins_job_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.jenkins_job_info instead.
    katello:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use the modules from the theforeman.foreman collection instead.
    kubevirt_cdi_upload:
      redirect: community.kubevirt.kubevirt_cdi_upload
    kubevirt_preset:
      redirect: community.kubevirt.kubevirt_preset
    kubevirt_pvc:
      redirect: community.kubevirt.kubevirt_pvc
    kubevirt_rs:
      redirect: community.kubevirt.kubevirt_rs
    kubevirt_template:
      redirect: community.kubevirt.kubevirt_template
    kubevirt_vm:
      redirect: community.kubevirt.kubevirt_vm
    ldap_attr:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.ldap_attrs instead.
    logicmonitor:
      tombstone:
        removal_version: 1.0.0
        warning_text: The logicmonitor_facts module is no longer maintained and the
          API used has been disabled in 2017.
    logicmonitor_facts:
      tombstone:
        removal_version: 1.0.0
        warning_text: The logicmonitor_facts module is no longer maintained and the
          API used has been disabled in 2017.
    memset_memstore_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.memset_memstore_info instead.
    memset_server_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.memset_server_info instead.
    na_cdot_aggregate:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use netapp.ontap.na_ontap_aggregate instead.
    na_cdot_license:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use netapp.ontap.na_ontap_license instead.
    na_cdot_lun:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use netapp.ontap.na_ontap_lun instead.
    na_cdot_qtree:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use netapp.ontap.na_ontap_qtree instead.
    na_cdot_svm:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use netapp.ontap.na_ontap_svm instead.
    na_cdot_user:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use netapp.ontap.na_ontap_user instead.
    na_cdot_user_role:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use netapp.ontap.na_ontap_user_role instead.
    na_cdot_volume:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use netapp.ontap.na_ontap_volume instead.
    na_ontap_gather_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use netapp.ontap.na_ontap_info instead.
    nginx_status_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.nginx_status_info instead.
    nios_a_record:
      redirect: infoblox.nios_modules.nios_a_record
    nios_aaaa_record:
      redirect: infoblox.nios_modules.nios_aaaa_record
    nios_cname_record:
      redirect: infoblox.nios_modules.nios_cname_record
    nios_dns_view:
      redirect: infoblox.nios_modules.nios_dns_view
    nios_fixed_address:
      redirect: infoblox.nios_modules.nios_fixed_address
    nios_host_record:
      redirect: infoblox.nios_modules.nios_host_record
    nios_member:
      redirect: infoblox.nios_modules.nios_member
    nios_mx_record:
      redirect: infoblox.nios_modules.nios_mx_record
    nios_naptr_record:
      redirect: infoblox.nios_modules.nios_naptr_record
    nios_network:
      redirect: infoblox.nios_modules.nios_network
    nios_network_view:
      redirect: infoblox.nios_modules.nios_network_view
    nios_nsgroup:
      redirect: infoblox.nios_modules.nios_nsgroup
    nios_ptr_record:
      redirect: infoblox.nios_modules.nios_ptr_record
    nios_srv_record:
      redirect: infoblox.nios_modules.nios_srv_record
    nios_txt_record:
      redirect: infoblox.nios_modules.nios_txt_record
    nios_zone:
      redirect: infoblox.nios_modules.nios_zone
    ome_device_info:
      redirect: dellemc.openmanage.ome_device_info
    one_image_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.one_image_info instead.
    onepassword_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.onepassword_info instead.
    oneview_datacenter_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.oneview_datacenter_info instead.
    oneview_enclosure_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.oneview_enclosure_info instead.
    oneview_ethernet_network_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.oneview_ethernet_network_info instead.
    oneview_fc_network_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.oneview_fc_network_info instead.
    oneview_fcoe_network_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.oneview_fcoe_network_info instead.
    oneview_logical_interconnect_group_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.oneview_logical_interconnect_group_info
          instead.
    oneview_network_set_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.oneview_network_set_info instead.
    oneview_san_manager_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.oneview_san_manager_info instead.
    online_server_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.online_server_info instead.
    online_user_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.online_user_info instead.
    ovirt:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_vm instead.
    ovirt_affinity_label_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_affinity_label_info instead.
    ovirt_api_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_api_info instead.
    ovirt_cluster_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_cluster_info instead.
    ovirt_datacenter_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_datacenter_info instead.
    ovirt_disk_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_disk_info instead.
    ovirt_event_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_event_info instead.
    ovirt_external_provider_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_external_provider_info instead.
    ovirt_group_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_group_info instead.
    ovirt_host_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_host_info instead.
    ovirt_host_storage_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_host_storage_info instead.
    ovirt_network_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_network_info instead.
    ovirt_nic_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_nic_info instead.
    ovirt_permission_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_permission_info instead.
    ovirt_quota_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_quota_info instead.
    ovirt_scheduling_policy_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_scheduling_policy_info instead.
    ovirt_snapshot_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_snapshot_info instead.
    ovirt_storage_domain_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_storage_domain_info instead.
    ovirt_storage_template_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_storage_template_info instead.
    ovirt_storage_vm_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_storage_vm_info instead.
    ovirt_tag_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_tag_info instead.
    ovirt_template_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_template_info instead.
    ovirt_user_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_user_info instead.
    ovirt_vm_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_vm_info instead.
    ovirt_vmpool_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use ovirt.ovirt.ovirt_vmpool_info instead.
    postgresql_copy:
      redirect: community.postgresql.postgresql_copy
    postgresql_db:
      redirect: community.postgresql.postgresql_db
    postgresql_ext:
      redirect: community.postgresql.postgresql_ext
    postgresql_idx:
      redirect: community.postgresql.postgresql_idx
    postgresql_info:
      redirect: community.postgresql.postgresql_info
    postgresql_lang:
      redirect: community.postgresql.postgresql_lang
    postgresql_membership:
      redirect: community.postgresql.postgresql_membership
    postgresql_owner:
      redirect: community.postgresql.postgresql_owner
    postgresql_pg_hba:
      redirect: community.postgresql.postgresql_pg_hba
    postgresql_ping:
      redirect: community.postgresql.postgresql_ping
    postgresql_privs:
      redirect: community.postgresql.postgresql_privs
    postgresql_publication:
      redirect: community.postgresql.postgresql_publication
    postgresql_query:
      redirect: community.postgresql.postgresql_query
    postgresql_schema:
      redirect: community.postgresql.postgresql_schema
    postgresql_sequence:
      redirect: community.postgresql.postgresql_sequence
    postgresql_set:
      redirect: community.postgresql.postgresql_set
    postgresql_slot:
      redirect: community.postgresql.postgresql_slot
    postgresql_subscription:
      redirect: community.postgresql.postgresql_subscription
    postgresql_table:
      redirect: community.postgresql.postgresql_table
    postgresql_tablespace:
      redirect: community.postgresql.postgresql_tablespace
    postgresql_user:
      redirect: community.postgresql.postgresql_user
    postgresql_user_obj_stat_info:
      redirect: community.postgresql.postgresql_user_obj_stat_info
    profitbricks:
      tombstone:
        removal_version: 11.0.0
        warning_text: Supporting library is unsupported since 2021.
    profitbricks_datacenter:
      tombstone:
        removal_version: 11.0.0
        warning_text: Supporting library is unsupported since 2021.
    profitbricks_nic:
      tombstone:
        removal_version: 11.0.0
        warning_text: Supporting library is unsupported since 2021.
    profitbricks_volume:
      tombstone:
        removal_version: 11.0.0
        warning_text: Supporting library is unsupported since 2021.
    profitbricks_volume_attachments:
      tombstone:
        removal_version: 11.0.0
        warning_text: Supporting library is unsupported since 2021.
    proxmox:
      redirect: community.proxmox.proxmox
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_backup:
      redirect: community.proxmox.proxmox_backup
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_backup_info:
      redirect: community.proxmox.proxmox_backup_info
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_disk:
      redirect: community.proxmox.proxmox_disk
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_domain_info:
      redirect: community.proxmox.proxmox_domain_info
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_group_info:
      redirect: community.proxmox.proxmox_group_info
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_kvm:
      redirect: community.proxmox.proxmox_kvm
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_nic:
      redirect: community.proxmox.proxmox_nic
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_node_info:
      redirect: community.proxmox.proxmox_node_info
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_pool:
      redirect: community.proxmox.proxmox_pool
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_pool_member:
      redirect: community.proxmox.proxmox_pool_member
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_snap:
      redirect: community.proxmox.proxmox_snap
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_storage_contents_info:
      redirect: community.proxmox.proxmox_storage_contents_info
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_storage_info:
      redirect: community.proxmox.proxmox_storage_info
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_tasks_info:
      redirect: community.proxmox.proxmox_tasks_info
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_template:
      redirect: community.proxmox.proxmox_template
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_user_info:
      redirect: community.proxmox.proxmox_user_info
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    proxmox_vm_info:
      redirect: community.proxmox.proxmox_vm_info
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    purefa_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use purestorage.flasharray.purefa_info instead.
    purefb_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use purestorage.flashblade.purefb_info instead.
    python_requirements_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.python_requirements_info instead.
    rax_cbs_attachments:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_cbs:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_cdb_database:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_cdb_user:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_cdb:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_clb_nodes:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_clb_ssl:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_clb:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_dns_record:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_dns:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_facts:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_files_objects:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_files:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_identity:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_keypair:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_meta:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_mon_alarm:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_mon_check:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_mon_entity:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_mon_notification_plan:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_mon_notification:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_network:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_queue:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_scaling_group:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    rax_scaling_policy:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on the deprecated package pyrax.
    redfish_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.redfish_info instead.
    rhn_channel:
      tombstone:
        removal_version: 10.0.0
        warning_text: RHN is EOL.
    rhn_register:
      tombstone:
        removal_version: 10.0.0
        warning_text: RHN is EOL.
    sapcar_extract:
      redirect: community.sap_libs.sapcar_extract
    sap_task_list_execute:
      redirect: community.sap_libs.sap_task_list_execute
    scaleway_image_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.scaleway_image_info instead.
    scaleway_ip_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.scaleway_ip_info instead.
    scaleway_organization_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.scaleway_organization_info instead.
    scaleway_security_group_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.scaleway_security_group_info instead.
    scaleway_server_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.scaleway_server_info instead.
    scaleway_snapshot_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.scaleway_snapshot_info instead.
    scaleway_volume_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.scaleway_volume_info instead.
    sensu_check:
      deprecation:
        removal_version: 13.0.0
        warning_text: Sensu Core and Sensu Enterprise products have been End of Life since 2019/20.
    sensu_client:
      deprecation:
        removal_version: 13.0.0
        warning_text: Sensu Core and Sensu Enterprise products have been End of Life since 2019/20.
    sensu_handler:
      deprecation:
        removal_version: 13.0.0
        warning_text: Sensu Core and Sensu Enterprise products have been End of Life since 2019/20.
    sensu_silence:
      deprecation:
        removal_version: 13.0.0
        warning_text: Sensu Core and Sensu Enterprise products have been End of Life since 2019/20.
    sensu_subscription:
      deprecation:
        removal_version: 13.0.0
        warning_text: Sensu Core and Sensu Enterprise products have been End of Life since 2019/20.
    sf_account_manager:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use netapp.elementsw.na_elementsw_account instead.
    sf_check_connections:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use netapp.elementsw.na_elementsw_check_connections instead.
    sf_snapshot_schedule_manager:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use netapp.elementsw.na_elementsw_snapshot_schedule instead.
    sf_volume_access_group_manager:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use netapp.elementsw.na_elementsw_access_group instead.
    sf_volume_manager:
      tombstone:
        removal_version: 2.0.0
        warning_text: Use netapp.elementsw.na_elementsw_volume instead.
    smartos_image_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.smartos_image_info instead.
    stackdriver:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on HTTPS APIs that do not exist anymore,
          and any new development in the direction of providing an alternative should
          happen in the context of the google.cloud collection.
    vertica_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.vertica_info instead.
    webfaction_app:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on HTTPS APIs that do not exist anymore and
          there is no clear path to update.
    webfaction_db:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on HTTPS APIs that do not exist anymore and
          there is no clear path to update.
    webfaction_domain:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on HTTPS APIs that do not exist anymore and
          there is no clear path to update.
    webfaction_mailbox:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on HTTPS APIs that do not exist anymore and
          there is no clear path to update.
    webfaction_site:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module relied on HTTPS APIs that do not exist anymore and
          there is no clear path to update.
    xenserver_guest_facts:
      tombstone:
        removal_version: 3.0.0
        warning_text: Use community.general.xenserver_guest_info instead.
  doc_fragments:
    _gcp:
      redirect: community.google._gcp
    docker:
      redirect: community.docker.docker
    hetzner:
      redirect: community.hrobot.robot
    kubevirt_common_options:
      redirect: community.kubevirt.kubevirt_common_options
    kubevirt_vm_options:
      redirect: community.kubevirt.kubevirt_vm_options
    nios:
      redirect: infoblox.nios_modules.nios
    postgresql:
      redirect: community.postgresql.postgresql
    proxmox:
      redirect: community.proxmox.proxmox
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    purestorage:
      deprecation:
        removal_version: 12.0.0
        warning_text: The modules for purestorage were removed in community.general 3.0.0, this document fragment was left behind.
    rackspace:
      tombstone:
        removal_version: 9.0.0
        warning_text: This doc fragment was used by rax modules, that relied on the deprecated
          package pyrax.
  module_utils:
    docker.common:
      redirect: community.docker.common
    docker.swarm:
      redirect: community.docker.swarm
    gcdns:
      redirect: community.google.gcdns
    gce:
      redirect: community.google.gce
    gcp:
      redirect: community.google.gcp
    hetzner:
      redirect: community.hrobot.robot
    kubevirt:
      redirect: community.kubevirt.kubevirt
    net_tools.nios.api:
      redirect: infoblox.nios_modules.api
    postgresql:
      redirect: community.postgresql.postgresql
    proxmox:
      redirect: community.proxmox.proxmox
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    pure:
      deprecation:
        removal_version: 12.0.0
        warning_text: The modules for purestorage were removed in community.general 3.0.0, this module util was left behind.
    rax:
      tombstone:
        removal_version: 9.0.0
        warning_text: This module util relied on the deprecated package pyrax.
    remote_management.dellemc.dellemc_idrac:
      redirect: dellemc.openmanage.dellemc_idrac
    remote_management.dellemc.ome:
      redirect: dellemc.openmanage.ome
  inventory:
    docker_machine:
      redirect: community.docker.docker_machine
    docker_swarm:
      redirect: community.docker.docker_swarm
    proxmox:
      redirect: community.proxmox.proxmox
      deprecation:
        removal_version: 15.0.0
        warning_text: The proxmox content has been moved to community.proxmox.
    kubevirt:
      redirect: community.kubevirt.kubevirt
    stackpath_compute:
      tombstone:
        removal_version: 11.0.0
        warning_text: The company and the service were sunset in June 2024.
  filter:
    path_join:
      # The ansible.builtin.path_join filter has been added in ansible-base 2.10.
      # Since plugin routing is only available since ansible-base 2.10, this
      # redirect will be used for ansible-base 2.10 or later. This was mostly
      # relevant before community.general 5.0.0, when community.general also
      # supported Ansible 2.9. Back then, the included path_join filter was used
      # for Ansible 2.9 or earlier. Now we only will have the redirect until we
      # eventually will deprecate and then remove it.
      redirect: ansible.builtin.path_join
