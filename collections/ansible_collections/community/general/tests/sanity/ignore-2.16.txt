plugins/connection/wsl.py yamllint:unparsable-with-libyaml
plugins/inventory/gitlab_runners.py yamllint:unparsable-with-libyaml
plugins/inventory/iocage.py yamllint:unparsable-with-libyaml
plugins/inventory/linode.py yamllint:unparsable-with-libyaml
plugins/inventory/lxd.py yamllint:unparsable-with-libyaml
plugins/inventory/nmap.py yamllint:unparsable-with-libyaml
plugins/inventory/scaleway.py yamllint:unparsable-with-libyaml
plugins/inventory/virtualbox.py yamllint:unparsable-with-libyaml
plugins/lookup/dependent.py validate-modules:unidiomatic-typecheck
plugins/modules/consul_session.py validate-modules:parameter-state-invalid-choice
plugins/modules/homectl.py import-3.11  # Uses deprecated stdlib library 'crypt'
plugins/modules/homectl.py import-3.12  # Uses deprecated stdlib library 'crypt'
plugins/modules/iptables_state.py validate-modules:undocumented-parameter             # params _back and _timeout used by action plugin
plugins/modules/lxc_container.py validate-modules:use-run-command-not-popen
plugins/modules/osx_defaults.py validate-modules:parameter-state-invalid-choice
plugins/modules/parted.py validate-modules:parameter-state-invalid-choice
plugins/modules/rhevm.py validate-modules:parameter-state-invalid-choice
plugins/modules/udm_user.py import-3.11  # Uses deprecated stdlib library 'crypt'
plugins/modules/udm_user.py import-3.12  # Uses deprecated stdlib library 'crypt'
plugins/modules/xfconf.py validate-modules:return-syntax-error
plugins/test/ansible_type.py yamllint:unparsable-with-libyaml
tests/unit/plugins/modules/test_gio_mime.yaml no-smart-quotes
