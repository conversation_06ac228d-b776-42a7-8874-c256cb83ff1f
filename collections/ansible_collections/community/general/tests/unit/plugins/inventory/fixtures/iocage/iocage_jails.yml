_meta:
  hostvars:
    test_101:
      iocage_basejail: 'yes'
      iocage_boot: 'off'
      iocage_ip4_dict:
        ip4:
          - ifc: vnet0
            ip: **********
            mask: '24'
        msg: ''
      iocage_ip4: **********
      iocage_ip6: '-'
      iocage_jid: '-'
      iocage_release: 13.4-RELEASE-p2
      iocage_state: down
      iocage_template: ansible_client
      iocage_type: jail
    test_102:
      iocage_basejail: 'yes'
      iocage_boot: 'off'
      iocage_ip4_dict:
        ip4:
          - ifc: vnet0
            ip: **********
            mask: '24'
        msg: ''
      iocage_ip4: **********
      iocage_ip6: '-'
      iocage_jid: '-'
      iocage_release: 13.4-RELEASE-p2
      iocage_state: down
      iocage_template: ansible_client
      iocage_type: jail
    test_103:
      iocage_basejail: 'yes'
      iocage_boot: 'off'
      iocage_ip4_dict:
        ip4:
          - ifc: vnet0
            ip: **********
            mask: '24'
        msg: ''
      iocage_ip4: **********
      iocage_ip6: '-'
      iocage_jid: '-'
      iocage_release: 13.4-RELEASE-p2
      iocage_state: down
      iocage_template: ansible_client
      iocage_type: jail
