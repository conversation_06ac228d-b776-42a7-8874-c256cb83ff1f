_meta:
  hostvars:
    test_111:
      iocage_basejail: 'yes'
      iocage_boot: 'off'
      iocage_ip4_dict:
        ip4: []
        msg: 'DHCP (not running)'
      iocage_ip4: '-'
      iocage_ip6: '-'
      iocage_jid: 'None'
      iocage_release: 14.1-RELEASE-p6
      iocage_state: down
      iocage_template: ansible_client
      iocage_type: jail
    test_112:
      iocage_basejail: 'yes'
      iocage_boot: 'off'
      iocage_ip4_dict:
        ip4: []
        msg: 'DHCP (not running)'
      iocage_ip4: '-'
      iocage_ip6: '-'
      iocage_jid: 'None'
      iocage_release: 14.1-RELEASE-p6
      iocage_state: down
      iocage_template: ansible_client
      iocage_type: jail
    test_113:
      iocage_basejail: 'yes'
      iocage_boot: 'off'
      iocage_ip4_dict:
        ip4: []
        msg: 'DHCP (not running)'
      iocage_ip4: '-'
      iocage_ip6: '-'
      iocage_jid: 'None'
      iocage_release: 14.1-RELEASE-p6
      iocage_state: down
      iocage_template: ansible_client
      iocage_type: jail
