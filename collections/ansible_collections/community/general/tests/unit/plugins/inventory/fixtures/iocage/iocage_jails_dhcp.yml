_meta:
  hostvars:
    test_111:
      iocage_basejail: 'yes'
      iocage_boot: 'off'
      iocage_ip4_dict:
        ip4:
          - ifc: epair0b
            ip: **********
            mask: '-'
        msg: ''
      iocage_ip4: **********
      iocage_ip6: '-'
      iocage_jid: '268'
      iocage_release: 14.1-RELEASE-p6
      iocage_state: up
      iocage_template: ansible_client
      iocage_type: jail
    test_112:
      iocage_basejail: 'yes'
      iocage_boot: 'off'
      iocage_ip4_dict:
        ip4:
          - ifc: epair0b
            ip: **********
            mask: '-'
        msg: ''
      iocage_ip4: **********
      iocage_ip6: '-'
      iocage_jid: '269'
      iocage_release: 14.1-RELEASE-p6
      iocage_state: up
      iocage_template: ansible_client
      iocage_type: jail
    test_113:
      iocage_basejail: 'yes'
      iocage_boot: 'off'
      iocage_ip4_dict:
        ip4:
          - ifc: epair0b
            ip: **********
            mask: '-'
        msg: ''
      iocage_ip4: **********
      iocage_ip6: '-'
      iocage_jid: '270'
      iocage_release: 14.1-RELEASE-p6
      iocage_state: up
      iocage_template: ansible_client
      iocage_type: jail
