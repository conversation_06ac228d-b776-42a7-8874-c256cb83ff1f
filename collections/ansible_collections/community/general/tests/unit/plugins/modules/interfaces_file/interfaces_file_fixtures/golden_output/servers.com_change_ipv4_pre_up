 auto aggi
 iface aggi inet static 
 hwaddress ether 22:44:77:88:D5:96
 address ************
 netmask ***************
 mtu 1500
 slaves int1 int2
 bond_mode 4
 bond_miimon 100
 bond_downdelay 200
 bond_updelay 200
 bond_lacp_rate slow
 bond_xmit_hash_policy layer3+4
 post-up /sbin/ethtool -K aggi tx off tso off
 
 auto agge
 iface agge inet manual

 auto br0
 iface br0 inet static
 bridge_ports agge
 hwaddress ether 22:44:77:88:D5:98
 address *************
 netmask ***************
 gateway *************
 slaves ext1 ext2
 bond_mode 4
 bond_miimon 100
 bond_downdelay 200
 bond_updelay 200
 bond_lacp_rate slow
 bond_xmit_hash_policy layer3+4
 post-up /sbin/ethtool -K agge tx off tso off
 
 up route add -net 10.0.0.0/8 gw ************ dev aggi
 up route add -net ***********/16 gw ************ dev aggi
 up route add -net ************/21 gw ************ dev aggi
 
 auto int1
 iface int1 inet manual
 bond-master aggi
 
 auto int2
 iface int2 inet manual
 bond-master aggi
 
 auto ext1
 iface ext1 inet manual
 bond-master agge
 
 auto ext2
 iface ext2 inet manual
 bond-master agge
 
 auto eth1
 iface eth1 inet manual

 auto lo
 iface lo inet loopback
 
source /etc/network/interfaces.d/*.cfg
