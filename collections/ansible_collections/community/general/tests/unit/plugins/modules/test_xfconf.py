# -*- coding: utf-8 -*-
# Author: <PERSON> (<EMAIL>)
# Largely adapted from test_redhat_subscription by
# <PERSON><PERSON> (jhn<PERSON><EMAIL>)
#
# Copyright (c) <PERSON> (<EMAIL>)
# Copyright (c) <PERSON><PERSON> (jhn<PERSON><PERSON>@redhat.com)
#
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

from __future__ import (absolute_import, division, print_function)
__metaclass__ = type


from ansible_collections.community.general.plugins.modules import xfconf
from .uthelper import UTHelper, RunCommandMock


UTHelper.from_module(xfconf, __name__, mocks=[RunCommandMock])
