# -*- coding: utf-8 -*-
# Copyright (c) <PERSON> (<EMAIL>)
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

---
anchors:
  environ: &env-def {environ_update: {LANGUAGE: C, LC_ALL: C}, check_rc: true}
test_cases:
  - id: success
    input:
      settings: whatever.settings
    output:
      version: 5.1.2
    mocks:
      run_command:
        - command: [/testbin/python, -m, django, --version]
          environ: *env-def
          rc: 0
          out: "5.1.2\n"
          err: ''
        - command: [/testbin/python, -m, django, check, --no-color, --settings=whatever.settings]
          environ: *env-def
          rc: 0
          out: "whatever\n"
          err: ''
  - id: multiple_databases
    input:
      settings: whatever.settings
      database:
        - abc
        - def
    output:
      version: 5.1.2
    mocks:
      run_command:
        - command: [/testbin/python, -m, django, --version]
          environ: *env-def
          rc: 0
          out: "5.1.2\n"
          err: ''
        - command: [/testbin/python, -m, django, check, --no-color, --settings=whatever.settings, --database, abc, --database, def]
          environ: *env-def
          rc: 0
          out: "whatever\n"
          err: ''
