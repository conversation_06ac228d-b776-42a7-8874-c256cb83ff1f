# -*- coding: utf-8 -*-
# Copyright (c) <PERSON> (<EMAIL>)
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

---
anchors:
  environ: &env-def {environ_update: {LANGUAGE: C, LC_ALL: C}, check_rc: true}
test_cases:
  - id: test_simple_element_set
    input:
      state: present
      key: /desktop/gnome/background/picture_filename
      value: 200
      value_type: int
    output:
      new_value: '200'
      changed: true
      version: 3.2.6
    mocks:
      run_command:
        - command: [/testbin/gconftool-2, --version]
          environ: *env-def
          rc: 0
          out: "3.2.6\n"
          err: ''
        - command: [/testbin/gconftool-2, --get, /desktop/gnome/background/picture_filename]
          environ: *env-def
          rc: 0
          out: "100\n"
          err: ''
        - command: [/testbin/gconftool-2, --type, int, --set, /desktop/gnome/background/picture_filename, '200']
          environ: *env-def
          rc: 0
          out: ''
          err: ''
        - command: [/testbin/gconftool-2, --get, /desktop/gnome/background/picture_filename]
          environ: *env-def
          rc: 0
          out: "200\n"
          err: ''
  - id: test_simple_element_set_idempotency_int
    input:
      state: present
      key: /desktop/gnome/background/picture_filename
      value: 200
      value_type: int
    output:
      new_value: '200'
      changed: false
      version: 3.2.5
    mocks:
      run_command:
        - command: [/testbin/gconftool-2, --version]
          environ: *env-def
          rc: 0
          out: "3.2.5\n"
          err: ''
        - command: [/testbin/gconftool-2, --get, /desktop/gnome/background/picture_filename]
          environ: *env-def
          rc: 0
          out: "200\n"
          err: ''
        - command: [/testbin/gconftool-2, --type, int, --set, /desktop/gnome/background/picture_filename, '200']
          environ: *env-def
          rc: 0
          out: ''
          err: ''
        - command: [/testbin/gconftool-2, --get, /desktop/gnome/background/picture_filename]
          environ: *env-def
          rc: 0
          out: "200\n"
          err: ''
  - id: test_simple_element_set_idempotency_bool
    input:
      state: present
      key: /apps/gnome_settings_daemon/screensaver/start_screensaver
      value: false
      value_type: bool
    output:
      new_value: 'false'
      changed: false
      version: 3.2.4
    mocks:
      run_command:
        - command: [/testbin/gconftool-2, --version]
          environ: *env-def
          rc: 0
          out: "3.2.4\n"
          err: ''
        - command: [/testbin/gconftool-2, --get, /apps/gnome_settings_daemon/screensaver/start_screensaver]
          environ: *env-def
          rc: 0
          out: "false\n"
          err: ''
        - command: [/testbin/gconftool-2, --type, bool, --set, /apps/gnome_settings_daemon/screensaver/start_screensaver, 'False']
          environ: *env-def
          rc: 0
          out: ''
          err: ''
        - command: [/testbin/gconftool-2, --get, /apps/gnome_settings_daemon/screensaver/start_screensaver]
          environ: *env-def
          rc: 0
          out: "false\n"
          err: ''
  - id: test_simple_element_unset
    input:
      state: absent
      key: /desktop/gnome/background/picture_filename
    output:
      new_value:
      changed: true
    mocks:
      run_command:
        - command: [/testbin/gconftool-2, --version]
          environ: *env-def
          rc: 0
          out: "3.2.4\n"
          err: ''
        - command: [/testbin/gconftool-2, --get, /desktop/gnome/background/picture_filename]
          environ: *env-def
          rc: 0
          out: "200\n"
          err: ''
        - command: [/testbin/gconftool-2, --unset, /desktop/gnome/background/picture_filename]
          environ: *env-def
          rc: 0
          out: ''
          err: ''
  - id: test_simple_element_unset_idempotency
    input:
      state: absent
      key: /apps/gnome_settings_daemon/screensaver/start_screensaver
    output:
      new_value:
      changed: false
    mocks:
      run_command:
        - command: [/testbin/gconftool-2, --version]
          environ: *env-def
          rc: 0
          out: "3.2.4\n"
          err: ''
        - command: [/testbin/gconftool-2, --get, /apps/gnome_settings_daemon/screensaver/start_screensaver]
          environ: *env-def
          rc: 0
          out: ''
          err: ''
        - command: [/testbin/gconftool-2, --unset, /apps/gnome_settings_daemon/screensaver/start_screensaver]
          environ: *env-def
          rc: 0
          out: ''
          err: ''
