{"agge": {"address_family": "inet", "down": [], "method": "manual", "post-up": [], "pre-up": [], "up": []}, "aggi": {"address": "************", "address_family": "inet", "bond_downdelay": "200", "bond_lacp_rate": "slow", "bond_miimon": "100", "bond_mode": "4", "bond_updelay": "200", "bond_xmit_hash_policy": "layer3+4", "down": [], "hwaddress": "ether 22:44:77:88:D5:96", "method": "static", "mtu": "1500", "netmask": "***************", "post-up": ["/sbin/ethtool -K aggi tx off tso off"], "pre-up": [], "slaves": "int1 int2", "up": []}, "br0": {"address": "*************", "address_family": "inet", "bond_downdelay": "200", "bond_lacp_rate": "slow", "bond_miimon": "100", "bond_mode": "4", "bond_updelay": "200", "bond_xmit_hash_policy": "layer3+4", "bridge_ports": "agge", "down": [], "gateway": "*************", "hwaddress": "ether 22:44:77:88:D5:98", "method": "static", "netmask": "***************", "post-up": ["/sbin/ethtool -K agge tx off tso off"], "pre-up": [], "slaves": "ext1 ext2", "up": ["route add -net 10.0.0.0/8 gw ************ dev aggi", "route add -net ***********/16 gw ************ dev aggi", "route add -net ************/21 gw ************ dev aggi"]}, "eth1": {"address_family": "inet", "down": [], "method": "manual", "post-up": [], "pre-up": [], "up": []}, "ext1": {"address_family": "inet", "bond-master": "agge", "down": [], "method": "manual", "post-up": [], "pre-up": [], "up": []}, "ext2": {"address_family": "inet", "bond-master": "agge", "down": [], "method": "manual", "post-up": [], "pre-up": [], "up": []}, "int1": {"address_family": "inet", "bond-master": "aggi", "down": [], "method": "manual", "post-up": [], "pre-up": [], "up": []}, "int2": {"address_family": "inet", "bond-master": "aggi", "down": [], "method": "manual", "post-up": [], "pre-up": [], "up": []}, "lo": {"address_family": "inet", "down": [], "method": "loopback", "post-up": [], "pre-up": [], "up": []}}