{"SR": {"OpaqueRef:f746e964-e0fe-c36d-d60b-6897cfde583f": {"PBDs": [], "VDIs": [], "allowed_operations": ["unplug", "plug", "pbd_create", "update", "pbd_destroy", "vdi_resize", "vdi_clone", "scan", "vdi_snapshot", "vdi_mirror", "vdi_create", "vdi_destroy"], "blobs": {}, "clustered": false, "content_type": "", "current_operations": {}, "introduced_by": "OpaqueRef:NULL", "is_tools_sr": false, "local_cache_enabled": false, "name_description": "", "name_label": "Ansible Test Storage 1", "other_config": {"auto-scan": "false"}, "physical_size": "2521133219840", "physical_utilisation": "1551485632512", "shared": true, "sm_config": {"allocation": "thick", "devserial": "scsi-3600a098038302d353624495242443848", "multipathable": "true", "use_vhd": "true"}, "tags": [], "type": "lvmohba", "uuid": "767b30e4-f8db-a83d-8ba7-f5e6e732e06f", "virtual_allocation": "1556925644800"}}, "VBD": {"OpaqueRef:510e214e-f0ba-3bc9-7834-a4f4d3fa33ef": {"VDI": "OpaqueRef:NULL", "VM": "OpaqueRef:08632af0-473e-5106-f400-7910229e49be", "allowed_operations": ["attach", "unpause", "insert", "pause"], "bootable": false, "current_operations": {}, "currently_attached": true, "device": "xvdd", "empty": true, "metrics": "OpaqueRef:1075bebe-ba71-66ef-ba30-8afbc83bc6b5", "mode": "RO", "other_config": {}, "qos_algorithm_params": {}, "qos_algorithm_type": "", "qos_supported_algorithms": [], "runtime_properties": {}, "status_code": "0", "status_detail": "", "storage_lock": false, "type": "CD", "unpluggable": true, "userdevice": "3", "uuid": "79ee1d8e-944b-3bfd-ba4c-a0c165d84f3d"}, "OpaqueRef:6bc2c353-f132-926d-6e9b-e4d1d55a3760": {"VDI": "OpaqueRef:102bef39-b134-d23a-9a50-490e1dbca8f7", "VM": "OpaqueRef:08632af0-473e-5106-f400-7910229e49be", "allowed_operations": ["attach", "unpause", "pause"], "bootable": true, "current_operations": {}, "currently_attached": true, "device": "xvda", "empty": false, "metrics": "OpaqueRef:1c71ccde-d7e9-10fb-569c-993b880fa790", "mode": "RW", "other_config": {"owner": ""}, "qos_algorithm_params": {}, "qos_algorithm_type": "", "qos_supported_algorithms": [], "runtime_properties": {}, "status_code": "0", "status_detail": "", "storage_lock": false, "type": "Disk", "unpluggable": false, "userdevice": "0", "uuid": "932fdf6d-7ac5-45e8-a48e-694af75726f1"}, "OpaqueRef:9bd6decd-2e55-b55e-387d-c40aa67ff151": {"VDI": "OpaqueRef:87b45ac6-af36-f4fd-6ebd-a08bed9001e4", "VM": "OpaqueRef:08632af0-473e-5106-f400-7910229e49be", "allowed_operations": ["attach", "unpause", "unplug", "unplug_force", "pause"], "bootable": false, "current_operations": {}, "currently_attached": true, "device": "xvdb", "empty": false, "metrics": "OpaqueRef:b8424146-d3ea-4850-db9a-47f0059c10ac", "mode": "RW", "other_config": {}, "qos_algorithm_params": {}, "qos_algorithm_type": "", "qos_supported_algorithms": [], "runtime_properties": {}, "status_code": "0", "status_detail": "", "storage_lock": false, "type": "Disk", "unpluggable": true, "userdevice": "1", "uuid": "c0c1e648-3690-e1fb-9f47-24b4df0cb458"}}, "VDI": {"OpaqueRef:102bef39-b134-d23a-9a50-490e1dbca8f7": {"SR": "OpaqueRef:f746e964-e0fe-c36d-d60b-6897cfde583f", "VBDs": ["OpaqueRef:6bc2c353-f132-926d-6e9b-e4d1d55a3760"], "allow_caching": false, "allowed_operations": ["clone", "snapshot"], "crash_dumps": [], "current_operations": {}, "is_a_snapshot": false, "is_tools_iso": false, "location": "fa1202b8-326f-4235-802e-fafbed66b26b", "managed": true, "metadata_latest": false, "metadata_of_pool": "", "missing": false, "name_description": "/", "name_label": "ansible-test-vm-2-root", "on_boot": "persist", "other_config": {}, "parent": "OpaqueRef:NULL", "physical_utilisation": "10766778368", "read_only": false, "sharable": false, "sm_config": {"host_OpaqueRef:e87be804-57a1-532e-56ac-6c4910957be0": "RW", "read-caching-enabled-on-dff6702e-bcb6-4704-8dd4-952e8c883365": "false", "read-caching-reason-dff6702e-bcb6-4704-8dd4-952e8c883365": "LICENSE_RESTRICTION", "vdi_type": "vhd"}, "snapshot_of": "OpaqueRef:NULL", "snapshot_time": "19700101T00:00:00Z", "snapshots": [], "storage_lock": false, "tags": [], "type": "system", "uuid": "fa1202b8-326f-4235-802e-fafbed66b26b", "virtual_size": "**********0", "xenstore_data": {}}, "OpaqueRef:87b45ac6-af36-f4fd-6ebd-a08bed9001e4": {"SR": "OpaqueRef:f746e964-e0fe-c36d-d60b-6897cfde583f", "VBDs": ["OpaqueRef:9bd6decd-2e55-b55e-387d-c40aa67ff151"], "allow_caching": false, "allowed_operations": ["clone", "snapshot"], "crash_dumps": [], "current_operations": {}, "is_a_snapshot": false, "is_tools_iso": false, "location": "ab3a4d72-f498-4687-86ce-ca937046db76", "managed": true, "metadata_latest": false, "metadata_of_pool": "", "missing": false, "name_description": "/var/lib/mysql", "name_label": "ansible-test-vm-2-mysql", "on_boot": "persist", "other_config": {}, "parent": "OpaqueRef:NULL", "physical_utilisation": "**********", "read_only": false, "sharable": false, "sm_config": {"host_OpaqueRef:e87be804-57a1-532e-56ac-6c4910957be0": "RW", "read-caching-enabled-on-dff6702e-bcb6-4704-8dd4-952e8c883365": "false", "read-caching-reason-dff6702e-bcb6-4704-8dd4-952e8c883365": "LICENSE_RESTRICTION", "vdi_type": "vhd"}, "snapshot_of": "OpaqueRef:NULL", "snapshot_time": "19700101T00:00:00Z", "snapshots": [], "storage_lock": false, "tags": [], "type": "user", "uuid": "ab3a4d72-f498-4687-86ce-ca937046db76", "virtual_size": "**********", "xenstore_data": {}}}, "VIF": {"OpaqueRef:9754a0ed-e100-d224-6a70-a55a9c2cedf9": {"MAC": "16:87:31:70:d6:31", "MAC_autogenerated": false, "MTU": "1500", "VM": "OpaqueRef:08632af0-473e-5106-f400-7910229e49be", "allowed_operations": ["attach", "unplug"], "current_operations": {}, "currently_attached": true, "device": "0", "ipv4_addresses": [], "ipv4_allowed": [], "ipv4_configuration_mode": "None", "ipv4_gateway": "", "ipv6_addresses": [], "ipv6_allowed": [], "ipv6_configuration_mode": "None", "ipv6_gateway": "", "locking_mode": "network_default", "metrics": "OpaqueRef:d74d5f20-f0ab-ee36-9a74-496ffb994232", "network": "OpaqueRef:8a404c5e-5673-ab69-5d6f-5a35a33b8724", "other_config": {}, "qos_algorithm_params": {}, "qos_algorithm_type": "", "qos_supported_algorithms": [], "runtime_properties": {}, "status_code": "0", "status_detail": "", "uuid": "07b70134-9396-94fc-5105-179b430ce4f8"}}, "VM": {"OpaqueRef:08632af0-473e-5106-f400-7910229e49be": {"HVM_boot_params": {"order": "cdn"}, "HVM_boot_policy": "BIOS order", "HVM_shadow_multiplier": 1.0, "PCI_bus": "", "PV_args": "", "PV_bootloader": "", "PV_bootloader_args": "", "PV_kernel": "", "PV_legacy_args": "", "PV_ramdisk": "", "VBDs": ["OpaqueRef:510e214e-f0ba-3bc9-7834-a4f4d3fa33ef", "OpaqueRef:9bd6decd-2e55-b55e-387d-c40aa67ff151", "OpaqueRef:6bc2c353-f132-926d-6e9b-e4d1d55a3760"], "VCPUs_at_startup": "1", "VCPUs_max": "1", "VCPUs_params": {}, "VGPUs": [], "VIFs": ["OpaqueRef:9754a0ed-e100-d224-6a70-a55a9c2cedf9"], "VTPMs": [], "actions_after_crash": "restart", "actions_after_reboot": "restart", "actions_after_shutdown": "destroy", "affinity": "OpaqueRef:e87be804-57a1-532e-56ac-6c4910957be0", "allowed_operations": ["changing_dynamic_range", "migrate_send", "pool_migrate", "changing_VCPUs_live", "suspend", "hard_reboot", "hard_shutdown", "clean_reboot", "clean_shutdown", "pause", "checkpoint", "snapshot"], "appliance": "OpaqueRef:NULL", "attached_PCIs": [], "bios_strings": {"bios-vendor": "<PERSON>en", "bios-version": "", "hp-rombios": "", "oem-1": "<PERSON>en", "oem-2": "MS_VM_CERT/SHA1/bdbeb6e0a816d43fa6d3fe8aaef04c2bad9d3e3d", "system-manufacturer": "<PERSON>en", "system-product-name": "HVM domU", "system-serial-number": "", "system-version": ""}, "blobs": {}, "blocked_operations": {}, "children": [], "consoles": ["OpaqueRef:2a24e023-a856-de30-aea3-2024bacdc71f"], "crash_dumps": [], "current_operations": {}, "domarch": "", "domid": "140", "generation_id": "", "guest_metrics": "OpaqueRef:150d2dfa-b634-7965-92ab-31fc26382683", "ha_always_run": false, "ha_restart_priority": "", "hardware_platform_version": "0", "has_vendor_device": false, "is_a_snapshot": false, "is_a_template": false, "is_control_domain": false, "is_default_template": false, "is_snapshot_from_vmpp": false, "is_vmss_snapshot": false, "last_boot_CPU_flags": {"features": "17cbfbff-f7fa3223-2d93fbff-00000023-00000001-000007ab-00000000-00000000-00001000-0c000000", "vendor": "GenuineIntel"}, "last_booted_record": "", "memory_dynamic_max": "**********", "memory_dynamic_min": "**********", "memory_overhead": "11534336", "memory_static_max": "**********", "memory_static_min": "**********", "memory_target": "**********", "metrics": "OpaqueRef:b56b460b-6476-304d-b143-ce543ffab828", "name_description": "Created by <PERSON><PERSON>", "name_label": "ansible-test-vm-2", "order": "0", "other_config": {"base_template_name": "CentOS 7", "folder": "/Ansible/Test", "import_task": "OpaqueRef:cf1402d3-b6c1-d908-fe62-06502e3b311a", "install-methods": "cdrom,nfs,http,ftp", "instant": "true", "linux_template": "true", "mac_seed": "0ab46664-f519-5383-166e-e4ea485ede7d"}, "parent": "OpaqueRef:NULL", "platform": {"acpi": "1", "apic": "true", "cores-per-socket": "1", "device_id": "0001", "nx": "true", "pae": "true", "timeoffset": "0", "vga": "std", "videoram": "8", "viridian": "false"}, "power_state": "Running", "protection_policy": "OpaqueRef:NULL", "recommendations": "<restrictions><restriction field=\"memory-static-max\" max=\"549755813888\" /><restriction field=\"vcpus-max\" max=\"16\" /><restriction property=\"number-of-vbds\" max=\"16\" /><restriction property=\"number-of-vifs\" max=\"7\" /><restriction field=\"allow-gpu-passthrough\" value=\"0\" /></restrictions>", "reference_label": "", "requires_reboot": false, "resident_on": "OpaqueRef:e87be804-57a1-532e-56ac-6c4910957be0", "shutdown_delay": "0", "snapshot_info": {}, "snapshot_metadata": "", "snapshot_of": "OpaqueRef:NULL", "snapshot_schedule": "OpaqueRef:NULL", "snapshot_time": "19700101T00:00:00Z", "snapshots": [], "start_delay": "0", "suspend_SR": "OpaqueRef:NULL", "suspend_VDI": "OpaqueRef:NULL", "tags": [], "transportable_snapshot_id": "", "user_version": "1", "uuid": "0a05d5ad-3e4b-f0dc-6101-8c56623958bc", "version": "0", "xenstore_data": {"vm-data": "", "vm-data/networks": "", "vm-data/networks/0": "", "vm-data/networks/0/gateway": "********", "vm-data/networks/0/ip": "********", "vm-data/networks/0/mac": "16:87:31:70:d6:31", "vm-data/networks/0/name": "Host internal management network", "vm-data/networks/0/netmask": "*************", "vm-data/networks/0/prefix": "24", "vm-data/networks/0/type": "static"}}}, "VM_guest_metrics": {"OpaqueRef:150d2dfa-b634-7965-92ab-31fc26382683": {"PV_drivers_detected": true, "PV_drivers_up_to_date": true, "PV_drivers_version": {"build": "90977", "major": "6", "micro": "0", "minor": "5"}, "can_use_hotplug_vbd": "unspecified", "can_use_hotplug_vif": "unspecified", "disks": {}, "last_updated": "20190113T19:36:26Z", "live": true, "memory": {}, "networks": {"0/ip": "***********"}, "os_version": {"distro": "centos", "major": "7", "minor": "2", "name": "CentOS Linux release 7.2.1511 (Core)", "uname": "3.10.0-327.22.2.el7.x86_64"}, "other": {"feature-balloon": "1", "feature-shutdown": "1", "feature-suspend": "1", "feature-vcpu-hotplug": "1", "has-vendor-device": "0", "platform-feature-multiprocessor-suspend": "1"}, "other_config": {}, "uuid": "5c9d1be5-7eee-88f2-46c3-df1d44f9cdb5"}}, "VM_metrics": {"OpaqueRef:b56b460b-6476-304d-b143-ce543ffab828": {"VCPUs_CPU": {}, "VCPUs_flags": {}, "VCPUs_number": "1", "VCPUs_params": {}, "VCPUs_utilisation": {}, "hvm": true, "install_time": "20190113T19:32:46Z", "last_updated": "19700101T00:00:00Z", "memory_actual": "1073729536", "nested_virt": false, "nomigrate": false, "other_config": {}, "start_time": "20190113T19:35:15Z", "state": [], "uuid": "876dd44c-aad1-97bf-9ee5-4cd58eac7163"}}, "host": {"OpaqueRef:e87be804-57a1-532e-56ac-6c4910957be0": {"API_version_major": "2", "API_version_minor": "7", "API_version_vendor": "XenSource", "API_version_vendor_implementation": {}, "PBDs": [], "PCIs": [], "PGPUs": [], "PIFs": [], "address": "********", "allowed_operations": ["vm_migrate", "provision", "vm_resume", "evacuate", "vm_start"], "bios_strings": {}, "blobs": {}, "capabilities": ["xen-3.0-x86_64", "xen-3.0-x86_32p", "hvm-3.0-x86_32", "hvm-3.0-x86_32p", "hvm-3.0-x86_64", ""], "chipset_info": {"iommu": "true"}, "control_domain": "OpaqueRef:ffcc92a1-8fde-df6f-a501-44b37811286b", "cpu_configuration": {}, "cpu_info": {"cpu_count": "40", "family": "6", "features": "7ffefbff-bfebfbff-00000021-2c100800", "features_hvm": "17cbfbff-f7fa3223-2d93fbff-00000023-00000001-000007ab-00000000-00000000-00001000-0c000000", "features_pv": "17c9cbf5-f6f83203-2191cbf5-00000023-00000001-00000329-00000000-00000000-00001000-0c000000", "flags": "fpu de tsc msr pae mce cx8 apic sep mca cmov pat clflush acpi mmx fxsr sse sse2 ht syscall nx lm constant_tsc arch_perfmon rep_good nopl nonstop_tsc eagerfpu pni pclmulqdq monitor est ssse3 fma cx16 sse4_1 sse4_2 movbe popcnt aes xsave avx f16c rdrand hypervisor lahf_lm abm ida arat epb pln pts dtherm fsgsbase bmi1 avx2 bmi2 erms xsaveopt cqm_llc cqm_occup_llc", "model": "63", "modelname": "Intel(R) Xeon(R) CPU E5-2660 v3 @ 2.60GHz", "socket_count": "2", "speed": "2597.070", "stepping": "2", "vendor": "GenuineIntel"}, "crash_dump_sr": "OpaqueRef:0b984cec-a36c-ce84-7b34-9f0088352d55", "crashdumps": [], "current_operations": {}, "display": "enabled", "edition": "free", "enabled": true, "external_auth_configuration": {}, "external_auth_service_name": "", "external_auth_type": "", "features": [], "guest_VCPUs_params": {}, "ha_network_peers": [], "ha_statefiles": [], "host_CPUs": ["OpaqueRef:ec3ba9c4-9b57-236b-3eaa-b157affc1621", "OpaqueRef:e6de7ab3-f4ad-f271-e51b-e3d8c041d3fb", "OpaqueRef:e519ef88-bf41-86ac-16b3-c178cb4b78b1", "OpaqueRef:e48f1bc1-98ba-89e5-ab69-821c625f7f82", "OpaqueRef:e2659936-3de6-dbca-cc44-4af50960b2b7", "OpaqueRef:d0da1e31-20ac-4aff-8897-e80df8200648", "OpaqueRef:cec473ba-41a8-439d-b397-be0c60467b5d", "OpaqueRef:ce88014d-b06c-c959-0624-04d79b791885", "OpaqueRef:c656ca58-41fe-3689-d322-174aa5798beb", "OpaqueRef:c0a21f14-8f46-19de-1cf4-530a34c4aa17", "OpaqueRef:bf70c061-7b45-0497-7ef6-65a236e898e8", "OpaqueRef:b7a2ba0f-f11b-3633-ad47-4f5f76a600a8", "OpaqueRef:b4fef1fa-3aae-9790-f47e-6a17f645339c", "OpaqueRef:b4594721-f8f4-4475-61c5-4efeec1733f1", "OpaqueRef:9dcba36f-c29f-478f-f578-d1ea347410a6", "OpaqueRef:987897e8-1184-917e-6a5f-e205d0c739e5", "OpaqueRef:90f06d64-be18-7fdf-36ba-bbd696a26cf3", "OpaqueRef:90150bc1-e604-4cd4-35ad-9cfa8e985de3", "OpaqueRef:838f4ad4-8ad2-0d6c-a74e-26baa461de3d", "OpaqueRef:736fb523-d347-e8c0-089b-c9811d3c1195", "OpaqueRef:7137b479-87d4-9097-a684-e54cc4de5d09", "OpaqueRef:6e08fa1d-7d7b-d9be-1574-ffe95bd515fd", "OpaqueRef:6b9e6ecd-54e5-4248-5aea-ee5b99248818", "OpaqueRef:65d56b24-3445-b444-5125-c91e6966fd29", "OpaqueRef:60908eca-1e5c-c938-5b76-e8ff9d8899ab", "OpaqueRef:46e96878-c076-2164-2373-6cdd108c2436", "OpaqueRef:40ccdaf4-6008-2b83-92cb-ca197f73433f", "OpaqueRef:3bc8133a-ccb2-6790-152f-b3f577517751", "OpaqueRef:38c8edd8-0621-76de-53f6-86bef2a9e05c", "OpaqueRef:342c1bab-a211-a0eb-79a5-780bd5ad1f23", "OpaqueRef:1e20e6d0-5502-0dff-4f17-5d35eb833af1", "OpaqueRef:176baafa-0e63-7000-f754-25e2a6b74959", "OpaqueRef:16cab1a2-0111-b2af-6dfe-3724b79e6b6b", "OpaqueRef:0f213647-8362-9c5e-e99b-0ebaefc609ce", "OpaqueRef:0e019819-b41f-0bfb-d4ee-dd5484fea9b6", "OpaqueRef:0d39212f-82ba-190c-b304-19b3fa491fff", "OpaqueRef:087ce3ad-3b66-ae1e-3130-3ae640dcc638", "OpaqueRef:0730f24c-87ed-8296-8f14-3036e5ad2357", "OpaqueRef:04c27426-4895-39a7-9ade-ef33d3721c26", "OpaqueRef:017b27bf-0270-19e7-049a-5a9b3bb54898"], "hostname": "ansible-test-host-2", "license_params": {"address1": "", "address2": "", "city": "", "company": "", "country": "", "enable_xha": "true", "expiry": "20291231T23:00:00Z", "grace": "no", "license_type": "", "name": "", "platform_filter": "false", "postalcode": "", "productcode": "", "regular_nag_dialog": "false", "restrict_ad": "false", "restrict_batch_hotfix_apply": "true", "restrict_checkpoint": "false", "restrict_cifs": "true", "restrict_connection": "false", "restrict_cpu_masking": "false", "restrict_dmc": "false", "restrict_dr": "false", "restrict_email_alerting": "false", "restrict_equalogic": "false", "restrict_export_resource_data": "true", "restrict_gpu": "false", "restrict_guest_agent_auto_update": "true", "restrict_guest_ip_setting": "false", "restrict_health_check": "false", "restrict_historical_performance": "false", "restrict_hotfix_apply": "false", "restrict_integrated_gpu_passthrough": "false", "restrict_intellicache": "false", "restrict_lab": "false", "restrict_live_patching": "true", "restrict_marathon": "false", "restrict_nested_virt": "true", "restrict_netapp": "false", "restrict_pci_device_for_auto_update": "true", "restrict_pool_attached_storage": "false", "restrict_pooling": "false", "restrict_pvs_proxy": "true", "restrict_qos": "false", "restrict_rbac": "false", "restrict_read_caching": "true", "restrict_set_vcpus_number_live": "true", "restrict_ssl_legacy_switch": "false", "restrict_stage": "false", "restrict_storage_xen_motion": "false", "restrict_storagelink": "false", "restrict_storagelink_site_recovery": "false", "restrict_vgpu": "true", "restrict_vif_locking": "false", "restrict_vlan": "false", "restrict_vm_memory_introspection": "true", "restrict_vmpr": "false", "restrict_vmss": "false", "restrict_vss": "false", "restrict_vswitch_controller": "false", "restrict_web_selfservice": "true", "restrict_web_selfservice_manager": "true", "restrict_wlb": "true", "restrict_xcm": "true", "restrict_xen_motion": "false", "serialnumber": "", "sku_marketing_name": "Citrix XenServer", "sku_type": "free", "sockets": "2", "state": "", "version": ""}, "license_server": {"address": "localhost", "port": "27000"}, "local_cache_sr": "OpaqueRef:0b984cec-a36c-ce84-7b34-9f0088352d55", "logging": {}, "memory_overhead": "**********", "metrics": "OpaqueRef:f55653cb-92eb-8257-f2ee-7a2d1c2d6aef", "name_description": "", "name_label": "ansible-test-host-2", "other_config": {"agent_start_time": "**********.", "boot_time": "**********.", "iscsi_iqn": "iqn.2018-06.com.example:87b7637d", "last_blob_sync_time": "**********.41", "multipathhandle": "dmp", "multipathing": "true"}, "patches": ["OpaqueRef:f5bd18b6-1423-893a-5d7f-7095338e6a2d", "OpaqueRef:eecb0b95-87fb-a53e-651c-9741efd18bb6", "OpaqueRef:e92c9ef3-2e51-1a36-d400-9e237982b782", "OpaqueRef:cc98226c-2c08-799e-5f15-7761a398e4a0", "OpaqueRef:c4f35e66-d064-55a7-6946-7f4b145275a6", "OpaqueRef:c3794494-f894-6141-b811-f37a8fe60094", "OpaqueRef:bcf61af7-63a9-e430-5b7c-a740ba470596", "OpaqueRef:b58ac71e-797e-6f66-71ad-fe298c94fd10", "OpaqueRef:a2ea18fd-5343-f8db-718d-f059c2a8cce0", "OpaqueRef:929db459-6861-c588-158f-70f763331d6d", "OpaqueRef:92962d94-2205-f6e1-12f9-b55a99fd824d", "OpaqueRef:65dfb07a-f90d-dad9-9ab8-1cc2b1e79afb", "OpaqueRef:537a87c4-3bf4-969f-f06a-2dd8d3a018a2", "OpaqueRef:32dd1de3-c9c8-bcbb-27a0-83d4a930876d", "OpaqueRef:30a8ccc8-74a9-b31f-0403-66b117e281b6", "OpaqueRef:24545c44-ffd1-8a28-18c6-3d008bf4d63e", "OpaqueRef:1fcef81b-7c44-a4db-f59a-c4a147da9c49", "OpaqueRef:1e98a240-514b-1863-5518-c771d0ebf579", "OpaqueRef:1632cab2-b268-6ce8-4f7b-ce7fd4bfa1eb"], "power_on_config": {}, "power_on_mode": "", "resident_VMs": [], "sched_policy": "credit", "software_version": {"build_number": "release/falcon/master/8", "date": "2017-05-11", "db_schema": "5.120", "dbv": "2017.0517", "hostname": "f7d02093adae", "linux": "4.4.0+10", "network_backend": "openvswitch", "platform_name": "XCP", "platform_version": "2.3.0", "product_brand": "XenServer", "product_version": "7.2.0", "product_version_text": "7.2", "product_version_text_short": "7.2", "xapi": "1.9", "xen": "4.7.5-2.12", "xencenter_max": "2.7", "xencenter_min": "2.7"}, "ssl_legacy": true, "supported_bootloaders": ["pygrub", "eliloader"], "suspend_image_sr": "OpaqueRef:0b984cec-a36c-ce84-7b34-9f0088352d55", "tags": [], "updates": ["OpaqueRef:7b4b5da1-54af-d0c4-3fea-394b4257bffe", "OpaqueRef:fbaabbfe-88d5-d89b-5b3f-d6374601ca71", "OpaqueRef:507ee5fc-59d3-e635-21d5-98a5cace4bf2", "OpaqueRef:6c9b814c-e1c2-b8be-198f-de358686b10a", "OpaqueRef:a17e721d-faf4-6ad1-c617-dd4899279534", "OpaqueRef:6ac77a0f-f079-8067-85cc-c9ae2f8dcca9", "OpaqueRef:f61edc83-91d9-a161-113f-00c110196238", "OpaqueRef:b71938bf-4c4f-eb17-7e78-588e71297a74", "OpaqueRef:01befb95-412e-e9dd-5b5d-edd50df61cb1", "OpaqueRef:a3f9481e-fe3d-1f00-235f-44d404f51128", "OpaqueRef:0760c608-b02e-743a-18a1-fa8f205374d6", "OpaqueRef:204558d7-dce0-2304-bdc5-80ec5fd7e3c3", "OpaqueRef:9eccc765-9726-d220-96b1-2e85adf77ecc", "OpaqueRef:91cfa47b-52f9-a4e3-4e78-52e3eb3e5141", "OpaqueRef:3fffd7c7-f4d1-6b03-a5b8-d75211bb7b8f", "OpaqueRef:7efce157-9b93-d116-f3f8-7eb0c6fb1a79", "OpaqueRef:e2209ae9-5362-3a20-f691-9294144e49f2", "OpaqueRef:1ced32ca-fec4-8b44-0e8f-753c97f2d93f", "OpaqueRef:65b14ae7-f440-0c4d-4af9-c7946b90fd2f"], "updates_requiring_reboot": [], "uuid": "dff6702e-bcb6-4704-8dd4-952e8c883365", "virtual_hardware_platform_versions": ["0", "1", "2"]}}, "network": {"OpaqueRef:8a404c5e-5673-ab69-5d6f-5a35a33b8724": {"MTU": "1500", "PIFs": [], "VIFs": [], "allowed_operations": [], "assigned_ips": {"OpaqueRef:8171dad1-f902-ec00-7ba2-9f92d8aa75ab": "***********", "OpaqueRef:9754a0ed-e100-d224-6a70-a55a9c2cedf9": "***********"}, "blobs": {}, "bridge": "xena<PERSON>", "current_operations": {}, "default_locking_mode": "unlocked", "managed": true, "name_description": "Network on which guests will be assigned a private link-local IP address which can be used to talk XenAPI", "name_label": "Host internal management network", "other_config": {"ip_begin": "***********", "ip_end": "***************", "is_guest_installer_network": "true", "is_host_internal_management_network": "true", "netmask": "***********"}, "tags": [], "uuid": "dbb96525-944f-0d1a-54ed-e65cb6d07450"}}}