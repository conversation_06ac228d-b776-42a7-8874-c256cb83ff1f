{"cdrom": {"type": "none"}, "customization_agent": "native", "disks": [{"name": "ansible-test-vm-1-C", "name_desc": "C:\\", "os_device": "xvda", "size": 42949672960, "sr": "Ansible Test Storage 1", "sr_uuid": "767b30e4-f8db-a83d-8ba7-f5e6e732e06f", "vbd_userdevice": "0"}], "domid": "143", "folder": "/Ansible/Test", "hardware": {"memory_mb": 2048, "num_cpu_cores_per_socket": 2, "num_cpus": 2}, "home_server": "", "is_template": false, "name": "ansible-test-vm-1", "name_desc": "Created by <PERSON><PERSON>", "networks": [{"gateway": "********", "gateway6": "", "ip": "********", "ip6": ["fe80:0000:0000:0000:11e1:12c9:ef3b:75a0"], "mac": "7a:a6:48:1e:31:46", "mtu": "1500", "name": "Host internal management network", "netmask": "*************", "prefix": "24", "prefix6": "", "vif_device": "0"}], "other_config": {"base_template_name": "Windows Server 2016 (64-bit)", "folder": "/Ansible/Test", "import_task": "OpaqueRef:e43eb71c-45d6-5351-09ff-96e4fb7d0fa5", "install-methods": "cdrom", "instant": "true", "mac_seed": "366fe8e0-878b-4320-8731-90d1ed3c0b93"}, "platform": {"acpi": "1", "apic": "true", "cores-per-socket": "2", "device_id": "0002", "hpet": "true", "nx": "true", "pae": "true", "timeoffset": "-28800", "vga": "std", "videoram": "8", "viridian": "true", "viridian_reference_tsc": "true", "viridian_time_ref_count": "true"}, "state": "<PERSON><PERSON>", "uuid": "81c373d7-a407-322f-911b-31386eb5215d", "xenstore_data": {"vm-data": ""}}