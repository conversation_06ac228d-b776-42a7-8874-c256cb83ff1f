{"SR": {"OpaqueRef:f746e964-e0fe-c36d-d60b-6897cfde583f": {"PBDs": [], "VDIs": [], "allowed_operations": ["unplug", "plug", "pbd_create", "update", "pbd_destroy", "vdi_resize", "vdi_clone", "scan", "vdi_snapshot", "vdi_mirror", "vdi_create", "vdi_destroy"], "blobs": {}, "clustered": false, "content_type": "", "current_operations": {}, "introduced_by": "OpaqueRef:NULL", "is_tools_sr": false, "local_cache_enabled": false, "name_description": "", "name_label": "Ansible Test Storage 1", "other_config": {"auto-scan": "false"}, "physical_size": "2521133219840", "physical_utilisation": "1551485632512", "shared": true, "sm_config": {"allocation": "thick", "devserial": "scsi-3600a098038302d353624495242443848", "multipathable": "true", "use_vhd": "true"}, "tags": [], "type": "lvmohba", "uuid": "767b30e4-f8db-a83d-8ba7-f5e6e732e06f", "virtual_allocation": "1556925644800"}}, "VBD": {"OpaqueRef:1c0a7c6d-09e5-9b2c-bbe3-9a73aadcff9f": {"VDI": "OpaqueRef:NULL", "VM": "OpaqueRef:43a1b8d4-da96-cb08-10f5-fb368abed19c", "allowed_operations": ["attach", "unpause", "insert", "pause"], "bootable": false, "current_operations": {}, "currently_attached": true, "device": "xvdd", "empty": true, "metrics": "OpaqueRef:1a36eae4-87c8-0945-cee9-c85a71fd843f", "mode": "RO", "other_config": {}, "qos_algorithm_params": {}, "qos_algorithm_type": "", "qos_supported_algorithms": [], "runtime_properties": {}, "status_code": "0", "status_detail": "", "storage_lock": false, "type": "CD", "unpluggable": true, "userdevice": "3", "uuid": "e6aacd53-a2c8-649f-b405-93fcb811411a"}, "OpaqueRef:ea4a4088-19c3-6db6-ebdf-c3c0ee4405a3": {"VDI": "OpaqueRef:fd20510d-e9ca-b966-3b98-4ae547dacf9a", "VM": "OpaqueRef:43a1b8d4-da96-cb08-10f5-fb368abed19c", "allowed_operations": ["attach", "unpause", "unplug", "unplug_force", "pause"], "bootable": true, "current_operations": {}, "currently_attached": true, "device": "xvda", "empty": false, "metrics": "OpaqueRef:ddbd70d4-7dde-b51e-6208-eb434b300009", "mode": "RW", "other_config": {"owner": "true"}, "qos_algorithm_params": {}, "qos_algorithm_type": "", "qos_supported_algorithms": [], "runtime_properties": {}, "status_code": "0", "status_detail": "", "storage_lock": false, "type": "Disk", "unpluggable": true, "userdevice": "0", "uuid": "ffd6de9c-c416-1d52-3e9d-3bcbf567245e"}}, "VDI": {"OpaqueRef:fd20510d-e9ca-b966-3b98-4ae547dacf9a": {"SR": "OpaqueRef:f746e964-e0fe-c36d-d60b-6897cfde583f", "VBDs": ["OpaqueRef:ea4a4088-19c3-6db6-ebdf-c3c0ee4405a3"], "allow_caching": false, "allowed_operations": ["clone", "snapshot"], "crash_dumps": [], "current_operations": {}, "is_a_snapshot": false, "is_tools_iso": false, "location": "b807f67b-3f37-4a6e-ad6c-033f812ab093", "managed": true, "metadata_latest": false, "metadata_of_pool": "", "missing": false, "name_description": "C:\\", "name_label": "ansible-test-vm-1-C", "on_boot": "persist", "other_config": {}, "parent": "OpaqueRef:NULL", "physical_utilisation": "43041947648", "read_only": false, "sharable": false, "sm_config": {"host_OpaqueRef:07a8da76-f1cf-f3b5-a531-6b751384f770": "RW", "read-caching-enabled-on-92ac8132-276b-4d0f-9d3a-54db51e4a438": "false", "read-caching-reason-92ac8132-276b-4d0f-9d3a-54db51e4a438": "LICENSE_RESTRICTION", "vdi_type": "vhd"}, "snapshot_of": "OpaqueRef:NULL", "snapshot_time": "19700101T00:00:00Z", "snapshots": [], "storage_lock": false, "tags": [], "type": "system", "uuid": "b807f67b-3f37-4a6e-ad6c-033f812ab093", "virtual_size": "42949672960", "xenstore_data": {}}}, "VIF": {"OpaqueRef:38da2120-**************-ab0a53ede42a": {"MAC": "7a:a6:48:1e:31:46", "MAC_autogenerated": false, "MTU": "1500", "VM": "OpaqueRef:43a1b8d4-da96-cb08-10f5-fb368abed19c", "allowed_operations": ["attach", "unplug"], "current_operations": {}, "currently_attached": true, "device": "0", "ipv4_addresses": ["********/24"], "ipv4_allowed": [], "ipv4_configuration_mode": "Static", "ipv4_gateway": "********", "ipv6_addresses": [""], "ipv6_allowed": [], "ipv6_configuration_mode": "None", "ipv6_gateway": "", "locking_mode": "network_default", "metrics": "OpaqueRef:15502939-df0f-0095-1ce3-e51367199d27", "network": "OpaqueRef:8a404c5e-5673-ab69-5d6f-5a35a33b8724", "other_config": {}, "qos_algorithm_params": {}, "qos_algorithm_type": "", "qos_supported_algorithms": [], "runtime_properties": {}, "status_code": "0", "status_detail": "", "uuid": "bd108d25-488a-f9b5-4c7b-02d40f1e38a8"}}, "VM": {"OpaqueRef:43a1b8d4-da96-cb08-10f5-fb368abed19c": {"HVM_boot_params": {"order": "dc"}, "HVM_boot_policy": "BIOS order", "HVM_shadow_multiplier": 1.0, "PCI_bus": "", "PV_args": "", "PV_bootloader": "", "PV_bootloader_args": "", "PV_kernel": "", "PV_legacy_args": "", "PV_ramdisk": "", "VBDs": ["OpaqueRef:1c0a7c6d-09e5-9b2c-bbe3-9a73aadcff9f", "OpaqueRef:ea4a4088-19c3-6db6-ebdf-c3c0ee4405a3"], "VCPUs_at_startup": "2", "VCPUs_max": "2", "VCPUs_params": {}, "VGPUs": [], "VIFs": ["OpaqueRef:38da2120-**************-ab0a53ede42a"], "VTPMs": [], "actions_after_crash": "restart", "actions_after_reboot": "restart", "actions_after_shutdown": "destroy", "affinity": "OpaqueRef:NULL", "allowed_operations": ["changing_dynamic_range", "migrate_send", "pool_migrate", "changing_VCPUs_live", "suspend", "hard_reboot", "hard_shutdown", "clean_reboot", "clean_shutdown", "pause", "checkpoint", "snapshot"], "appliance": "OpaqueRef:NULL", "attached_PCIs": [], "bios_strings": {"bios-vendor": "<PERSON>en", "bios-version": "", "hp-rombios": "", "oem-1": "<PERSON>en", "oem-2": "MS_VM_CERT/SHA1/bdbeb6e0a816d43fa6d3fe8aaef04c2bad9d3e3d", "system-manufacturer": "<PERSON>en", "system-product-name": "HVM domU", "system-serial-number": "", "system-version": ""}, "blobs": {}, "blocked_operations": {}, "children": [], "consoles": ["OpaqueRef:4fa7d34e-1fb6-9e88-1b21-41a3c6550d8b"], "crash_dumps": [], "current_operations": {}, "domarch": "", "domid": "143", "generation_id": "3274224479562869847:6952848762503845513", "guest_metrics": "OpaqueRef:453f21be-954d-2ca8-e38e-09741e91350c", "ha_always_run": false, "ha_restart_priority": "", "hardware_platform_version": "0", "has_vendor_device": false, "is_a_snapshot": false, "is_a_template": false, "is_control_domain": false, "is_default_template": false, "is_snapshot_from_vmpp": false, "is_vmss_snapshot": false, "last_boot_CPU_flags": {"features": "17cbfbff-f7fa3223-2d93fbff-00000023-00000001-000007ab-00000000-00000000-00001000-0c000000", "vendor": "GenuineIntel"}, "last_booted_record": "", "memory_dynamic_max": "2147483648", "memory_dynamic_min": "2147483648", "memory_overhead": "20971520", "memory_static_max": "2147483648", "memory_static_min": "1073741824", "memory_target": "2147483648", "metrics": "OpaqueRef:6eede779-4e55-7cfb-8b8a-e4b9becf770b", "name_description": "Created by <PERSON><PERSON>", "name_label": "ansible-test-vm-1", "order": "0", "other_config": {"base_template_name": "Windows Server 2016 (64-bit)", "folder": "/Ansible/Test", "import_task": "OpaqueRef:e43eb71c-45d6-5351-09ff-96e4fb7d0fa5", "install-methods": "cdrom", "instant": "true", "mac_seed": "366fe8e0-878b-4320-8731-90d1ed3c0b93"}, "parent": "OpaqueRef:NULL", "platform": {"acpi": "1", "apic": "true", "cores-per-socket": "2", "device_id": "0002", "hpet": "true", "nx": "true", "pae": "true", "timeoffset": "-28800", "vga": "std", "videoram": "8", "viridian": "true", "viridian_reference_tsc": "true", "viridian_time_ref_count": "true"}, "power_state": "Running", "protection_policy": "OpaqueRef:NULL", "recommendations": "<restrictions><restriction field=\"memory-static-max\" max=\"1649267441664\"/><restriction field=\"vcpus-max\" max=\"32\"/><restriction field=\"has-vendor-device\" value=\"true\"/><restriction max=\"255\" property=\"number-of-vbds\"/><restriction max=\"7\" property=\"number-of-vifs\"/></restrictions>", "reference_label": "windows-server-2016-64bit", "requires_reboot": false, "resident_on": "OpaqueRef:07a8da76-f1cf-f3b5-a531-6b751384f770", "shutdown_delay": "0", "snapshot_info": {}, "snapshot_metadata": "", "snapshot_of": "OpaqueRef:NULL", "snapshot_schedule": "OpaqueRef:NULL", "snapshot_time": "19700101T00:00:00Z", "snapshots": [], "start_delay": "0", "suspend_SR": "OpaqueRef:NULL", "suspend_VDI": "OpaqueRef:NULL", "tags": [], "transportable_snapshot_id": "", "user_version": "1", "uuid": "81c373d7-a407-322f-911b-31386eb5215d", "version": "0", "xenstore_data": {"vm-data": ""}}}, "VM_guest_metrics": {"OpaqueRef:453f21be-954d-2ca8-e38e-09741e91350c": {"PV_drivers_detected": true, "PV_drivers_up_to_date": true, "PV_drivers_version": {"build": "1020", "major": "7", "micro": "0", "minor": "1"}, "can_use_hotplug_vbd": "yes", "can_use_hotplug_vif": "yes", "disks": {}, "last_updated": "20190113T19:40:34Z", "live": true, "memory": {}, "networks": {"0/ip": "********", "0/ipv6/0": "fe80:0000:0000:0000:11e1:12c9:ef3b:75a0"}, "os_version": {"distro": "windows", "major": "6", "minor": "2", "name": "Microsoft Windows Server 2016 Standard|C:\\Windows|\\Device\\Harddisk0\\Partition2", "spmajor": "0", "spminor": "0"}, "other": {"data-ts": "1", "error": "WTSQueryUserToken : 1008 failed.", "feature-balloon": "1", "feature-poweroff": "1", "feature-reboot": "1", "feature-s3": "1", "feature-s4": "1", "feature-setcomputername": "1", "feature-static-ip-setting": "1", "feature-suspend": "1", "feature-ts": "1", "feature-ts2": "1", "feature-xs-batcmd": "1", "has-vendor-device": "0", "platform-feature-multiprocessor-suspend": "1"}, "other_config": {}, "uuid": "9ea6803f-12ca-3d6a-47b7-c90a33b67b98"}}, "VM_metrics": {"OpaqueRef:6eede779-4e55-7cfb-8b8a-e4b9becf770b": {"VCPUs_CPU": {}, "VCPUs_flags": {}, "VCPUs_number": "2", "VCPUs_params": {}, "VCPUs_utilisation": {}, "hvm": true, "install_time": "20190113T19:31:47Z", "last_updated": "19700101T00:00:00Z", "memory_actual": "2147475456", "nested_virt": false, "nomigrate": false, "other_config": {}, "start_time": "20190113T19:38:59Z", "state": [], "uuid": "c67fadf7-8143-0c92-c772-cd3901c18e70"}}, "host": {"OpaqueRef:07a8da76-f1cf-f3b5-a531-6b751384f770": {"API_version_major": "2", "API_version_minor": "7", "API_version_vendor": "XenSource", "API_version_vendor_implementation": {}, "PBDs": [], "PCIs": [], "PGPUs": [], "PIFs": [], "address": "********", "allowed_operations": ["vm_migrate", "provision", "vm_resume", "evacuate", "vm_start"], "bios_strings": {}, "blobs": {}, "capabilities": ["xen-3.0-x86_64", "xen-3.0-x86_32p", "hvm-3.0-x86_32", "hvm-3.0-x86_32p", "hvm-3.0-x86_64", ""], "chipset_info": {"iommu": "true"}, "control_domain": "OpaqueRef:a2a31555-f232-822b-8f36-10d75d44b79c", "cpu_configuration": {}, "cpu_info": {"cpu_count": "40", "family": "6", "features": "7ffefbff-bfebfbff-00000021-2c100800", "features_hvm": "17cbfbff-f7fa3223-2d93fbff-00000023-00000001-000007ab-00000000-00000000-00001000-0c000000", "features_pv": "17c9cbf5-f6f83203-2191cbf5-00000023-00000001-00000329-00000000-00000000-00001000-0c000000", "flags": "fpu de tsc msr pae mce cx8 apic sep mca cmov pat clflush acpi mmx fxsr sse sse2 ht syscall nx lm constant_tsc arch_perfmon rep_good nopl nonstop_tsc eagerfpu pni pclmulqdq monitor est ssse3 fma cx16 sse4_1 sse4_2 movbe popcnt aes xsave avx f16c rdrand hypervisor lahf_lm abm ida arat epb pln pts dtherm fsgsbase bmi1 avx2 bmi2 erms xsaveopt cqm_llc cqm_occup_llc", "model": "63", "modelname": "Intel(R) Xeon(R) CPU E5-2660 v3 @ 2.60GHz", "socket_count": "2", "speed": "2597.064", "stepping": "2", "vendor": "GenuineIntel"}, "crash_dump_sr": "OpaqueRef:ed72d7bf-4e53-67fc-17f5-e27b203042ba", "crashdumps": [], "current_operations": {}, "display": "enabled", "edition": "free", "enabled": true, "external_auth_configuration": {}, "external_auth_service_name": "", "external_auth_type": "", "features": [], "guest_VCPUs_params": {}, "ha_network_peers": [], "ha_statefiles": [], "host_CPUs": ["OpaqueRef:f7e744f6-a6f9-c460-999a-c27e1395e2e0", "OpaqueRef:f6e5dcf0-0453-8f3f-88c1-7ad6e2ef3dd1", "OpaqueRef:f27a52fb-5feb-173d-1a07-d1735a83c2cc", "OpaqueRef:ed65327a-508a-ccfc-dba6-2a0175cb2432", "OpaqueRef:e41d2f2a-fe9e-72cb-8104-b22d6d314b13", "OpaqueRef:e1988469-b814-5d10-17a6-bfd7c62d2b5f", "OpaqueRef:d73967dc-b8d8-b47b-39f4-d599fdcabf55", "OpaqueRef:cba9ebd9-40dc-0611-d1bb-aa661bd0bf70", "OpaqueRef:c53d3110-4085-60af-8300-d879818789f7", "OpaqueRef:bee0cf87-7df6-79a6-94e8-36f98e69ad20", "OpaqueRef:bde28e83-213f-0e65-b6ad-0ae1ecebb98d", "OpaqueRef:bbfefe67-f65f-98cb-c3fc-cb8ea0588006", "OpaqueRef:b38ac595-afea-0ca0-49a0-9f5ef2368e3b", "OpaqueRef:b14ef333-78b1-193d-02da-dc9bfed36912", "OpaqueRef:afd478bf-57b9-0c79-f257-50aeb81504f1", "OpaqueRef:a307cd3a-2132-2e42-4ebc-cc1c7780736d", "OpaqueRef:a1a9df7d-88ba-64fd-a55c-0f6472e1753f", "OpaqueRef:a0e39c9c-3e0b-fa03-e5d0-93a09aa77393", "OpaqueRef:9fd5719b-36ab-8e25-7756-20a496ccb331", "OpaqueRef:9ac4195d-ac07-cfe2-bc19-27ee54cf91fb", "OpaqueRef:98c5c00c-1e2d-e22b-842e-79e85ce07873", "OpaqueRef:961129bf-e695-f206-7297-64f9007a64f3", "OpaqueRef:64368b4c-3488-2808-f0b3-42f2a656df2b", "OpaqueRef:620dabc0-d7c5-0dc8-52df-3be25194c2fb", "OpaqueRef:5cee2759-dd8e-7e1a-0727-21e196584030", "OpaqueRef:58f70163-863d-5787-ffbb-2416cb16ca1e", "OpaqueRef:4462f848-f396-653d-67f9-2bed13be2c58", "OpaqueRef:40e800c2-19db-7cd8-c045-5ae93f908cae", "OpaqueRef:3f84278b-dec6-ded0-1a33-4daa0ce75a2f", "OpaqueRef:3ef14992-62f6-e1f0-5715-0ee02a834a9c", "OpaqueRef:3e274c24-c55b-06f5-2c8f-415421043ab2", "OpaqueRef:35ff27da-f286-7b70-adc1-a200880bb79f", "OpaqueRef:2511aa53-8660-e442-3cd2-305982d1f751", "OpaqueRef:21d234e3-138c-81ca-9ed8-febc81b874e9", "OpaqueRef:1f9b4ee3-dcc7-114e-b401-dc3e94c07efa", "OpaqueRef:1b94a981-d340-dd07-41c2-b3ff3c545fed", "OpaqueRef:197ad104-64a8-5af3-8c7a-95f3d301aadd", "OpaqueRef:1672e747-dc4b-737b-ddcf-0a373f966012", "OpaqueRef:12ced494-a225-7584-456b-739331bb5114", "OpaqueRef:0139ff72-62ac-1a6a-8f6f-cb01d8a4ee92"], "hostname": "ansible-test-host-1", "license_params": {"address1": "", "address2": "", "city": "", "company": "", "country": "", "enable_xha": "true", "expiry": "20291231T23:00:00Z", "grace": "no", "license_type": "", "name": "", "platform_filter": "false", "postalcode": "", "productcode": "", "regular_nag_dialog": "false", "restrict_ad": "false", "restrict_batch_hotfix_apply": "true", "restrict_checkpoint": "false", "restrict_cifs": "true", "restrict_connection": "false", "restrict_cpu_masking": "false", "restrict_dmc": "false", "restrict_dr": "false", "restrict_email_alerting": "false", "restrict_equalogic": "false", "restrict_export_resource_data": "true", "restrict_gpu": "false", "restrict_guest_agent_auto_update": "true", "restrict_guest_ip_setting": "false", "restrict_health_check": "false", "restrict_historical_performance": "false", "restrict_hotfix_apply": "false", "restrict_integrated_gpu_passthrough": "false", "restrict_intellicache": "false", "restrict_lab": "false", "restrict_live_patching": "true", "restrict_marathon": "false", "restrict_nested_virt": "true", "restrict_netapp": "false", "restrict_pci_device_for_auto_update": "true", "restrict_pool_attached_storage": "false", "restrict_pooling": "false", "restrict_pvs_proxy": "true", "restrict_qos": "false", "restrict_rbac": "false", "restrict_read_caching": "true", "restrict_set_vcpus_number_live": "true", "restrict_ssl_legacy_switch": "false", "restrict_stage": "false", "restrict_storage_xen_motion": "false", "restrict_storagelink": "false", "restrict_storagelink_site_recovery": "false", "restrict_vgpu": "true", "restrict_vif_locking": "false", "restrict_vlan": "false", "restrict_vm_memory_introspection": "true", "restrict_vmpr": "false", "restrict_vmss": "false", "restrict_vss": "false", "restrict_vswitch_controller": "false", "restrict_web_selfservice": "true", "restrict_web_selfservice_manager": "true", "restrict_wlb": "true", "restrict_xcm": "true", "restrict_xen_motion": "false", "serialnumber": "", "sku_marketing_name": "Citrix XenServer", "sku_type": "free", "sockets": "2", "state": "", "version": ""}, "license_server": {"address": "localhost", "port": "27000"}, "local_cache_sr": "OpaqueRef:ed72d7bf-4e53-67fc-17f5-e27b203042ba", "logging": {}, "memory_overhead": "**********", "metrics": "OpaqueRef:82b6937a-60c2-96d8-4e78-9f9a1143033f", "name_description": "", "name_label": "ansible-test-host-1", "other_config": {"agent_start_time": "**********.", "boot_time": "**********.", "iscsi_iqn": "iqn.2018-06.com.example:c8bac750", "last_blob_sync_time": "**********.36", "multipathhandle": "dmp", "multipathing": "true"}, "patches": ["OpaqueRef:f74ca18d-cfb7-e4fe-e5c4-819843de11e2", "OpaqueRef:f53ff05e-8dd8-3a15-d3b0-8dcf6004fbe2", "OpaqueRef:ed7f38da-1a50-a48b-60bf-933cabe8d7bc", "OpaqueRef:e7bb1462-51a5-1aaf-3b56-11b8ebd83a94", "OpaqueRef:d87b343b-6ba3-db8b-b80e-e02319ba5924", "OpaqueRef:ccb00450-ed04-4eaa-e6d7-130ef3722374", "OpaqueRef:b79b8864-11d9-1d5f-09e5-a66d7b64b9e2", "OpaqueRef:9bebcc7d-61ae-126b-3be0-9156026e586f", "OpaqueRef:740a1156-b991-00b8-ef50-fdbb22a4d911", "OpaqueRef:71def430-754b-2bfb-6c93-ec3b67b754e4", "OpaqueRef:6c73b00d-df66-1740-9578-2b14e46297ba", "OpaqueRef:6a53d2ae-3d6b-32ed-705f-fd53f1304470", "OpaqueRef:35a67684-b094-1c77-beff-8237d87c7a27", "OpaqueRef:33da42c2-c421-9859-79b7-ce9b6c394a1b", "OpaqueRef:2baa6b4b-9bbe-c1b2-23ce-c8c831ac581d", "OpaqueRef:2ac3beea-dee2-44e7-9f67-5fd216e593a0", "OpaqueRef:1bd8f24b-3190-6e7a-b36e-e2998197d062", "OpaqueRef:1694ea26-4930-6ca1-036e-273438375de9", "OpaqueRef:09813f03-0c6f-a6af-768f-ef4cdde2c641"], "power_on_config": {}, "power_on_mode": "", "resident_VMs": [], "sched_policy": "credit", "software_version": {"build_number": "release/falcon/master/8", "date": "2017-05-11", "db_schema": "5.120", "dbv": "2017.0517", "hostname": "f7d02093adae", "linux": "4.4.0+10", "network_backend": "openvswitch", "platform_name": "XCP", "platform_version": "2.3.0", "product_brand": "XenServer", "product_version": "7.2.0", "product_version_text": "7.2", "product_version_text_short": "7.2", "xapi": "1.9", "xen": "4.7.5-2.12", "xencenter_max": "2.7", "xencenter_min": "2.7"}, "ssl_legacy": true, "supported_bootloaders": ["pygrub", "eliloader"], "suspend_image_sr": "OpaqueRef:ed72d7bf-4e53-67fc-17f5-e27b203042ba", "tags": [], "updates": ["OpaqueRef:b71938bf-4c4f-eb17-7e78-588e71297a74", "OpaqueRef:91cfa47b-52f9-a4e3-4e78-52e3eb3e5141", "OpaqueRef:e2209ae9-5362-3a20-f691-9294144e49f2", "OpaqueRef:6ac77a0f-f079-8067-85cc-c9ae2f8dcca9", "OpaqueRef:a17e721d-faf4-6ad1-c617-dd4899279534", "OpaqueRef:6c9b814c-e1c2-b8be-198f-de358686b10a", "OpaqueRef:fbaabbfe-88d5-d89b-5b3f-d6374601ca71", "OpaqueRef:9eccc765-9726-d220-96b1-2e85adf77ecc", "OpaqueRef:204558d7-dce0-2304-bdc5-80ec5fd7e3c3", "OpaqueRef:65b14ae7-f440-0c4d-4af9-c7946b90fd2f", "OpaqueRef:0760c608-b02e-743a-18a1-fa8f205374d6", "OpaqueRef:1ced32ca-fec4-8b44-0e8f-753c97f2d93f", "OpaqueRef:3fffd7c7-f4d1-6b03-a5b8-d75211bb7b8f", "OpaqueRef:01befb95-412e-e9dd-5b5d-edd50df61cb1", "OpaqueRef:a3f9481e-fe3d-1f00-235f-44d404f51128", "OpaqueRef:507ee5fc-59d3-e635-21d5-98a5cace4bf2", "OpaqueRef:7b4b5da1-54af-d0c4-3fea-394b4257bffe", "OpaqueRef:f61edc83-91d9-a161-113f-00c110196238", "OpaqueRef:7efce157-9b93-d116-f3f8-7eb0c6fb1a79"], "updates_requiring_reboot": [], "uuid": "92ac8132-276b-4d0f-9d3a-54db51e4a438", "virtual_hardware_platform_versions": ["0", "1", "2"]}}, "network": {"OpaqueRef:8a404c5e-5673-ab69-5d6f-5a35a33b8724": {"MTU": "1500", "PIFs": [], "VIFs": [], "allowed_operations": [], "assigned_ips": {"OpaqueRef:8171dad1-f902-ec00-7ba2-9f92d8aa75ab": "***********", "OpaqueRef:9754a0ed-e100-d224-6a70-a55a9c2cedf9": "***********"}, "blobs": {}, "bridge": "xena<PERSON>", "current_operations": {}, "default_locking_mode": "unlocked", "managed": true, "name_description": "Network on which guests will be assigned a private link-local IP address which can be used to talk XenAPI", "name_label": "Host internal management network", "other_config": {"ip_begin": "***********", "ip_end": "***************", "is_guest_installer_network": "true", "is_host_internal_management_network": "true", "netmask": "***********"}, "tags": [], "uuid": "dbb96525-944f-0d1a-54ed-e65cb6d07450"}}}