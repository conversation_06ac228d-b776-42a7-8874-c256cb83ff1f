{"SR": {"OpaqueRef:f746e964-e0fe-c36d-d60b-6897cfde583f": {"PBDs": [], "VDIs": [], "allowed_operations": ["unplug", "plug", "pbd_create", "update", "pbd_destroy", "vdi_resize", "vdi_clone", "scan", "vdi_snapshot", "vdi_mirror", "vdi_create", "vdi_destroy"], "blobs": {}, "clustered": false, "content_type": "", "current_operations": {}, "introduced_by": "OpaqueRef:NULL", "is_tools_sr": false, "local_cache_enabled": false, "name_description": "", "name_label": "Ansible Test Storage 1", "other_config": {"auto-scan": "false"}, "physical_size": "2521133219840", "physical_utilisation": "1551485632512", "shared": true, "sm_config": {"allocation": "thick", "devserial": "scsi-3600a098038302d353624495242443848", "multipathable": "true", "use_vhd": "true"}, "tags": [], "type": "lvmohba", "uuid": "767b30e4-f8db-a83d-8ba7-f5e6e732e06f", "virtual_allocation": "1556925644800"}}, "VBD": {"OpaqueRef:024b722e-8d0f-65e6-359e-f301a009b683": {"VDI": "OpaqueRef:NULL", "VM": "OpaqueRef:957f576a-2347-1789-80db-4beb50466bc2", "allowed_operations": ["attach", "insert"], "bootable": false, "current_operations": {}, "currently_attached": false, "device": "", "empty": true, "metrics": "OpaqueRef:81509584-b22f-bc71-3c4e-e6c3bdca71f0", "mode": "RO", "other_config": {}, "qos_algorithm_params": {}, "qos_algorithm_type": "", "qos_supported_algorithms": [], "runtime_properties": {}, "status_code": "0", "status_detail": "", "storage_lock": false, "type": "CD", "unpluggable": true, "userdevice": "3", "uuid": "38d850d0-c402-490e-6b97-1d23558c4e0e"}, "OpaqueRef:235f4f04-1dc9-9fa5-c229-a1df187ba48c": {"VDI": "OpaqueRef:4d3e9fc7-ae61-b312-e0a8-b53bee06282e", "VM": "OpaqueRef:957f576a-2347-1789-80db-4beb50466bc2", "allowed_operations": ["attach"], "bootable": true, "current_operations": {}, "currently_attached": false, "device": "xvda", "empty": false, "metrics": "OpaqueRef:529f6071-5627-28c5-1f41-ee8c0733f1da", "mode": "RW", "other_config": {"owner": ""}, "qos_algorithm_params": {}, "qos_algorithm_type": "", "qos_supported_algorithms": [], "runtime_properties": {}, "status_code": "0", "status_detail": "", "storage_lock": false, "type": "Disk", "unpluggable": false, "userdevice": "0", "uuid": "3fd7d35c-cb9d-f0c4-726b-e188ef0dc446"}}, "VDI": {"OpaqueRef:4d3e9fc7-ae61-b312-e0a8-b53bee06282e": {"SR": "OpaqueRef:f746e964-e0fe-c36d-d60b-6897cfde583f", "VBDs": ["OpaqueRef:235f4f04-1dc9-9fa5-c229-a1df187ba48c"], "allow_caching": false, "allowed_operations": ["forget", "generate_config", "update", "resize", "destroy", "clone", "copy", "snapshot"], "crash_dumps": [], "current_operations": {}, "is_a_snapshot": false, "is_tools_iso": false, "location": "bdd0baeb-5447-4963-9e71-a5ff6e85fa59", "managed": true, "metadata_latest": false, "metadata_of_pool": "", "missing": false, "name_description": "/", "name_label": "ansible-test-vm-3-root", "on_boot": "persist", "other_config": {"content_id": "cd8e8b2b-f158-c519-02f0-81d130fe83c5"}, "parent": "OpaqueRef:NULL", "physical_utilisation": "8615100416", "read_only": false, "sharable": false, "sm_config": {"vdi_type": "vhd"}, "snapshot_of": "OpaqueRef:NULL", "snapshot_time": "19700101T00:00:00Z", "snapshots": [], "storage_lock": false, "tags": [], "type": "system", "uuid": "bdd0baeb-5447-4963-9e71-a5ff6e85fa59", "virtual_size": "8589934592", "xenstore_data": {}}}, "VIF": {"OpaqueRef:8171dad1-f902-ec00-7ba2-9f92d8aa75ab": {"MAC": "72:fb:c7:ac:b9:97", "MAC_autogenerated": true, "MTU": "1500", "VM": "OpaqueRef:957f576a-2347-1789-80db-4beb50466bc2", "allowed_operations": ["attach"], "current_operations": {}, "currently_attached": false, "device": "0", "ipv4_addresses": [], "ipv4_allowed": [], "ipv4_configuration_mode": "None", "ipv4_gateway": "", "ipv6_addresses": [], "ipv6_allowed": [], "ipv6_configuration_mode": "None", "ipv6_gateway": "", "locking_mode": "network_default", "metrics": "OpaqueRef:e5b53fb1-3e99-4bf5-6b00-95fdba1f2610", "network": "OpaqueRef:8a404c5e-5673-ab69-5d6f-5a35a33b8724", "other_config": {}, "qos_algorithm_params": {}, "qos_algorithm_type": "", "qos_supported_algorithms": [], "runtime_properties": {}, "status_code": "0", "status_detail": "", "uuid": "94bd4913-4940-437c-a1c3-50f7eb354c55"}}, "VM": {"OpaqueRef:957f576a-2347-1789-80db-4beb50466bc2": {"HVM_boot_params": {"order": ""}, "HVM_boot_policy": "", "HVM_shadow_multiplier": 1.0, "PCI_bus": "", "PV_args": "graphical utf8", "PV_bootloader": "pygrub", "PV_bootloader_args": "", "PV_kernel": "", "PV_legacy_args": "", "PV_ramdisk": "", "VBDs": ["OpaqueRef:235f4f04-1dc9-9fa5-c229-a1df187ba48c", "OpaqueRef:024b722e-8d0f-65e6-359e-f301a009b683"], "VCPUs_at_startup": "1", "VCPUs_max": "1", "VCPUs_params": {}, "VGPUs": [], "VIFs": ["OpaqueRef:8171dad1-f902-ec00-7ba2-9f92d8aa75ab"], "VTPMs": [], "actions_after_crash": "restart", "actions_after_reboot": "restart", "actions_after_shutdown": "destroy", "affinity": "OpaqueRef:NULL", "allowed_operations": ["changing_dynamic_range", "changing_shadow_memory", "changing_static_range", "make_into_template", "migrate_send", "destroy", "export", "start_on", "start", "clone", "copy", "snapshot"], "appliance": "OpaqueRef:NULL", "attached_PCIs": [], "bios_strings": {"bios-vendor": "<PERSON>en", "bios-version": "", "hp-rombios": "", "oem-1": "<PERSON>en", "oem-2": "MS_VM_CERT/SHA1/bdbeb6e0a816d43fa6d3fe8aaef04c2bad9d3e3d", "system-manufacturer": "<PERSON>en", "system-product-name": "HVM domU", "system-serial-number": "", "system-version": ""}, "blobs": {}, "blocked_operations": {}, "children": [], "consoles": [], "crash_dumps": [], "current_operations": {}, "domarch": "", "domid": "-1", "generation_id": "", "guest_metrics": "OpaqueRef:6a8acd85-4cab-4e52-27d5-5f4a51c1bf69", "ha_always_run": false, "ha_restart_priority": "", "hardware_platform_version": "0", "has_vendor_device": false, "is_a_snapshot": false, "is_a_template": false, "is_control_domain": false, "is_default_template": false, "is_snapshot_from_vmpp": false, "is_vmss_snapshot": false, "last_boot_CPU_flags": {"features": "17c9cbf5-f6f83203-2191cbf5-00000023-00000001-00000329-00000000-00000000-00001000-0c000000", "vendor": "GenuineIntel"}, "last_booted_record": "", "memory_dynamic_max": "1073741824", "memory_dynamic_min": "1073741824", "memory_overhead": "10485760", "memory_static_max": "1073741824", "memory_static_min": "536870912", "memory_target": "0", "metrics": "OpaqueRef:87fc5829-478b-1dcd-989f-50e8ba58a87d", "name_description": "Created by <PERSON><PERSON>", "name_label": "ansible-test-vm-3", "order": "0", "other_config": {"auto_poweron": "true", "base_template_name": "zatemplate", "import_task": "OpaqueRef:9948fd82-6d79-8882-2f01-4edc8795e361", "install-methods": "cdrom,nfs,http,ftp", "install-repository": "http://mirror.centos.org/centos-6/6.2/os/x86_64/", "instant": "true", "last_shutdown_action": "Destroy", "last_shutdown_initiator": "external", "last_shutdown_reason": "halted", "last_shutdown_time": "20140314T21:16:41Z", "linux_template": "true", "mac_seed": "06e27068-70c2-4c69-614b-7c54b5a4a781", "rhel6": "true"}, "parent": "OpaqueRef:NULL", "platform": {"acpi": "true", "apic": "true", "cores-per-socket": "1", "nx": "false", "pae": "true", "viridian": "true"}, "power_state": "Halted", "protection_policy": "OpaqueRef:NULL", "recommendations": "<restrictions><restriction field=\"memory-static-max\" max=\"17179869184\" /><restriction field=\"vcpus-max\" max=\"8\" /><restriction property=\"number-of-vbds\" max=\"7\" /><restriction property=\"number-of-vifs\" max=\"7\" /></restrictions>", "reference_label": "", "requires_reboot": false, "resident_on": "OpaqueRef:NULL", "shutdown_delay": "0", "snapshot_info": {}, "snapshot_metadata": "", "snapshot_of": "OpaqueRef:NULL", "snapshot_schedule": "OpaqueRef:NULL", "snapshot_time": "19700101T00:00:00Z", "snapshots": [], "start_delay": "0", "suspend_SR": "OpaqueRef:NULL", "suspend_VDI": "OpaqueRef:NULL", "tags": ["web-frontend"], "transportable_snapshot_id": "", "user_version": "1", "uuid": "8f5bc97c-42fa-d619-aba4-d25eced735e0", "version": "0", "xenstore_data": {"vm-data": "", "vm-data/networks": "", "vm-data/networks/0": "", "vm-data/networks/0/mac": "72:fb:c7:ac:b9:97", "vm-data/networks/0/name": "Host internal management network"}}}, "VM_guest_metrics": {"OpaqueRef:6a8acd85-4cab-4e52-27d5-5f4a51c1bf69": {"PV_drivers_detected": true, "PV_drivers_up_to_date": true, "PV_drivers_version": {"build": "46676", "major": "5", "micro": "100", "minor": "6"}, "can_use_hotplug_vbd": "unspecified", "can_use_hotplug_vif": "unspecified", "disks": {}, "last_updated": "20190113T19:36:07Z", "live": true, "memory": {}, "networks": {"0/ip": "***********"}, "os_version": {"distro": "centos", "major": "6", "minor": "10", "name": "CentOS release 6.10 (Final)", "uname": "2.6.32-754.6.3.el6.x86_64"}, "other": {"feature-balloon": "1", "has-vendor-device": "0", "platform-feature-multiprocessor-suspend": "1"}, "other_config": {}, "uuid": "3928a6a4-1acd-c134-ed35-eb0ccfaed65c"}}, "VM_metrics": {"OpaqueRef:87fc5829-478b-1dcd-989f-50e8ba58a87d": {"VCPUs_CPU": {}, "VCPUs_flags": {}, "VCPUs_number": "0", "VCPUs_params": {}, "VCPUs_utilisation": {"0": 0.0}, "hvm": false, "install_time": "20190113T19:35:05Z", "last_updated": "19700101T00:00:00Z", "memory_actual": "1073741824", "nested_virt": false, "nomigrate": false, "other_config": {}, "start_time": "19700101T00:00:00Z", "state": [], "uuid": "6cb05fe9-b83e-34c8-29e0-3b793e1da661"}}, "host": {}, "network": {"OpaqueRef:8a404c5e-5673-ab69-5d6f-5a35a33b8724": {"MTU": "1500", "PIFs": [], "VIFs": [], "allowed_operations": [], "assigned_ips": {"OpaqueRef:8171dad1-f902-ec00-7ba2-9f92d8aa75ab": "***********", "OpaqueRef:9754a0ed-e100-d224-6a70-a55a9c2cedf9": "***********"}, "blobs": {}, "bridge": "xena<PERSON>", "current_operations": {}, "default_locking_mode": "unlocked", "managed": true, "name_description": "Network on which guests will be assigned a private link-local IP address which can be used to talk XenAPI", "name_label": "Host internal management network", "other_config": {"ip_begin": "***********", "ip_end": "***************", "is_guest_installer_network": "true", "is_host_internal_management_network": "true", "netmask": "***********"}, "tags": [], "uuid": "dbb96525-944f-0d1a-54ed-e65cb6d07450"}}}