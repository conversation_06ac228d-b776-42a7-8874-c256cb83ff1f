---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Get volume information and register it in a variable
  scaleway_volume_info:
    region: par1
  register: volumes

- name: Display volumes variable
  debug:
    var: volumes

- name: Ensure retrieval of volumes info is success
  assert:
    that:
      - volumes is success

- name: Get volume information and register it in a variable (AMS1)
  scaleway_volume_info:
    region: ams1
  register: ams1_volumes

- name: Display volumes variable
  debug:
    var: ams1_volumes

- name: Ensure retrieval of volumes info is success
  assert:
    that:
      - ams1_volumes is success
