---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

url: http://localhost:8080/auth
admin_realm: master
admin_user: admin
admin_password: password
realm: myrealm
client_id: myclient
role: myrole
description_1: desc 1
description_2: desc 2

auth_args:
  auth_keycloak_url: "{{ url }}"
  auth_realm: "{{ admin_realm }}"
  auth_username: "{{ admin_user }}"
  auth_password: "{{ admin_password }}"

redirect_uris1:
  - "http://example.c.com/"
  - "http://example.b.com/"
  - "http://example.a.com/"

client_attributes1: {"backchannel.logout.session.required": true, "backchannel.logout.revoke.offline.tokens": false, "client.secret.creation.time": 0}

protocol_mappers1:
  - name: 'email'
    protocol: 'openid-connect'
    protocolMapper: 'oidc-usermodel-property-mapper'
    config:
      "claim.name": "email"
      "user.attribute": "email"
      "jsonType.label": "String"
      "id.token.claim": true
      "access.token.claim": true
      "userinfo.token.claim": true

  - name: 'email_verified'
    protocol: 'openid-connect'
    protocolMapper: 'oidc-usermodel-property-mapper'
    config:
      "claim.name": "email_verified"
      "user.attribute": "emailVerified"
      "jsonType.label": "boolean"
      "id.token.claim": true
      "access.token.claim": true
      "userinfo.token.claim": true

  - name: 'family_name'
    protocol: 'openid-connect'
    protocolMapper: 'oidc-usermodel-property-mapper'
    config:
      "claim.name": "family_name"
      "user.attribute": "lastName"
      "jsonType.label": "String"
      "id.token.claim": "true"
      "access.token.claim": "true"
      "userinfo.token.claim": "true"
