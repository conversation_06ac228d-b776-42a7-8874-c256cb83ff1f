---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# ------
# set up : set gitconfig with value
- name: delete file config
  file:
    path: "{{ remote_tmp_dir }}/gitconfig_file"
    state: absent

- name: setup global config
  copy:
    src: gitconfig
    dest: ~/.gitconfig
