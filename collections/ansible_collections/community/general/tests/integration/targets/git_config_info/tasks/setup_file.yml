---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# ------
# set up : set gitconfig with value
- name: delete global config
  file:
    path: ~/.gitconfig
    state: absent

- name: set up file config
  copy:
    src: gitconfig
    dest: "{{ remote_tmp_dir }}/gitconfig_file"
