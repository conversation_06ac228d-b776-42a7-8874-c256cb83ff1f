---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# test code for the git_config_info module
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: setup
  import_tasks: setup.yml

- block:
    - include_tasks: get_simple_value.yml
      loop:
        - import_file: setup_global.yml
          git_scope: 'global'
        - import_file: setup_file.yml
          git_scope: 'file'
          git_file: "{{ remote_tmp_dir }}/gitconfig_file"

    - include_tasks: get_multi_value.yml
      loop:
        - import_file: setup_global.yml
          git_scope: 'global'
        - import_file: setup_file.yml
          git_scope: 'file'
          git_file: "{{ remote_tmp_dir }}/gitconfig_file"

    - include_tasks: get_all_values.yml
      loop:
        - import_file: setup_global.yml
          git_scope: 'global'
        - import_file: setup_file.yml
          git_scope: 'file'
          git_file: "{{ remote_tmp_dir }}/gitconfig_file"

    - include_tasks: error_handling.yml
  when: git_installed is succeeded and git_version.stdout is version(git_version_supporting_includes, ">=")
...
