---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- include_tasks: "{{ item.import_file }}"

- name: getting simple file value1
  git_config_info:
    name: user.name
    scope: "{{ item.git_scope }}"
    path: "{{ item.git_file | default(omit) }}"
  register: get_result1

- name: getting simple file value2
  git_config_info:
    name: "credential.https://some.com.username"
    scope: "{{ item.git_scope }}"
    path: "{{ item.git_file | default(omit) }}"
  register: get_result2

- name: getting not existing value
  git_config_info:
    name: "user.email"
    scope: "{{ item.git_scope }}"
    path: "{{ item.git_file | default(omit) }}"
  register: get_result3

- name: assert value is correct
  assert:
    that:
      - get_result1.config_value == "foobar"
      - 'get_result1.config_values == {"user.name": ["foobar"]}'
      - get_result2.config_value == "yolo"
      - 'get_result2.config_values == {"credential.https://some.com.username": ["yolo"]}'
      - get_result3.config_value == ""
      - 'get_result3.config_values == {"user.email": []}'
...
