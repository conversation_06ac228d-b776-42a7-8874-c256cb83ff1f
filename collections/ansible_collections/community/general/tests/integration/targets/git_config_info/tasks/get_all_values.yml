---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- include_tasks: "{{ item.import_file }}"

- name: getting all values (as list) for a file config
  git_config_info:
    scope: "{{ item.git_scope }}"
    path: "{{ item.git_file | default(omit) }}"
  register: get_result

- name: assert value is correct
  assert:
    that:
      - get_result.config_value == ""
      - 'get_result.config_values == {"credential.https://some.com.username": ["yolo"], "user.name": ["foobar"], "push.pushoption": ["merge_request.create", "merge_request.draft"]}'
...
