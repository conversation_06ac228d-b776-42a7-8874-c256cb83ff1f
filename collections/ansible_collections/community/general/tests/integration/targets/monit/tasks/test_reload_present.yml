---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: reload monit when process is missing
  monit:
    name: httpd_echo
    state: reloaded
  register: result

- name: check that state is changed
  assert:
    that:
      - result is success
      - result is changed

- name: test process not present
  monit:
    name: httpd_echo
    state: present
    timeout: 5
  register: result
  failed_when: result is not skip and result is success

- name: test monitor missing process
  monit:
    name: httpd_echo
    state: monitored
  register: result
  failed_when: result is not skip and result is success

- name: start process
  shell: "{{ process_run_cmd }}"

- import_tasks: check_state.yml
  vars:
    reason: verify service running
    service_state: "up"

- name: add process config
  blockinfile:
    path: "{{ monitrc }}"
    block: |
      check process httpd_echo with matching "httpd_echo"
          start program = "{{ process_run_cmd }}"
          stop program = "/bin/sh -c 'kill `pgrep -f httpd_echo`'"
          if failed host localhost port 8082 then restart

- name: restart monit
  service:
    name: monit
    state: restarted

- name: test process present again
  monit:
    name: httpd_echo
    state: present
  register: result

- name: check that state is unchanged
  assert:
    that:
      - result is success
      - result is not changed
