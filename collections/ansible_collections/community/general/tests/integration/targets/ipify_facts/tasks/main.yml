---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Test code for the ipify_facts
# Copyright (c) 2017, A<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- debug: var=ansible_distribution
- debug: var=ansible_distribution_version

- set_fact:
    validate_certs: false
  when: (ansible_distribution == "MacOSX" and ansible_distribution_version == "10.11.1")

- name: get information about current IP using ipify facts
  ipify_facts:
    timeout: 30
    validate_certs: "{{ validate_certs }}"
  register: external_ip
  until: external_ip is successful
  retries: 5
  delay: 10

- name: check if task was successful
  assert:
    that:
      - external_ip is not changed
      - external_ip.ansible_facts is defined
      - external_ip.ansible_facts.ipify_public_ip is defined
