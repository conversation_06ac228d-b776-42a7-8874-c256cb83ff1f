dn: ou=users,dc=example,dc=com
objectClass: organizationalUnit
objectClass: top
ou: users

dn: uid=ldaptest,ou=users,dc=example,dc=com
uid: ldaptest
uidNumber: 1111
gidNUmber: 100
objectClass: top
objectClass: posixAccount
objectClass: shadowAccount
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
loginShell: /bin/sh
homeDirectory: /home/<USER>
cn: LDAP Test
gecos: LDAP Test
displayName: LDAP Test
userPassword: test1pass!
mail: <EMAIL>
sn: Test

dn: uid=second,ou=users,dc=example,dc=com
uid: second
uidNumber: 1112
gidNUmber: 102
objectClass: top
objectClass: posixAccount
objectClass: shadowAccount
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
loginShell: /bin/sh
homeDirectory: /home/<USER>
cn: Second Test
gecos: Second Test
displayName: Second Test
mail: <EMAIL>
sn: Test
