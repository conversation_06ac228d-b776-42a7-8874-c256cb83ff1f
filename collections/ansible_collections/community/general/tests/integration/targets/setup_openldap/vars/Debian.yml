---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

python_ldap_package_name: python-ldap
python_ldap_package_name_python3: python3-ldap
openldap_packages_name:
  - slapd
  - ldap-utils
openldap_service_name: slapd
openldap_debconfs:
  - question: "shared/organization"
    value: "Example Organization"
    vtype: "string"
  - question: "slapd/allow_ldap_v2"
    value: "false"
    vtype: "boolean"
  - question: "slapd/backend"
    value: "MDB"
    vtype: "select"
  - question: "slapd/domain"
    value: "example.com"
    vtype: "string"
  - question: "slapd/dump_database"
    value: "when needed"
    vtype: "select"
  - question: "slapd/dump_database_destdir"
    value: "/var/backups/slapd-VERSION"
    vtype: "string"
  - question: "slapd/internal/adminpw"
    value: "Test1234!"
    vtype: "password"
  - question: "slapd/internal/generated_adminpw"
    value: "Test1234!"
    vtype: "password"
  - question: "slapd/invalid_config"
    value: "true"
    vtype: "boolean"
  - question: "slapd/move_old_database"
    value: "true"
    vtype: "boolean"
  - question: "slapd/no_configuration"
    value: "false"
    vtype: "boolean"
  - question: "slapd/password1"
    value: "Test1234!"
    vtype: "password"
  - question: "slapd/password2"
    value: "Test1234!"
    vtype: "password"
  - question: "slapd/password_mismatch"
    value: ""
    vtype: "note"
  - question: "slapd/purge_database"
    value: "false"
    vtype: "boolean"
  - question: "slapd/upgrade_slapcat_failure"
    value: ""
    vtype: "error"
