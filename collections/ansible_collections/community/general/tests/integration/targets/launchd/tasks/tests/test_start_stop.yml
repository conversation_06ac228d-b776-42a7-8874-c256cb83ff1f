---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# -----------------------------------------------------------

- name: "[{{ item }}] Given a started service in check mode"
  launchd:
    name: "{{ launchd_service_name }}"
    state: started
  become: true
  register: "test_1_launchd_start_result_check_mode"
  check_mode: true


- name: "[{{ item }}] Validate that service was started in check mode"
  assert:
    that:
      - test_1_launchd_start_result_check_mode is success
      - test_1_launchd_start_result_check_mode is changed


- name: "[{{ item }}] Given a started service..."
  launchd:
    name: "{{ launchd_service_name }}"
    state: started
  become: true
  register: "test_1_launchd_start_result"


- name: "[{{ item }}] Validate that service was started"
  assert:
    that:
      - test_1_launchd_start_result is success
      - test_1_launchd_start_result is changed
      - test_1_launchd_start_result.status.previous_pid == '-'
      - test_1_launchd_start_result.status.previous_state == 'unloaded'
      - test_1_launchd_start_result.status.current_state == 'started'
      - test_1_launchd_start_result.status.current_pid != '-'
      - test_1_launchd_start_result.status.status_code == '0'

# -----------------------------------------------------------

- name: "[{{ item }}] Given a stopped service..."
  launchd:
    name: "{{ launchd_service_name }}"
    state: stopped
  become: true
  register: "test_2_launchd_stop_result"

- name: "[{{ item }}] Validate that service was stopped after it was started"
  assert:
    that:
      - test_2_launchd_stop_result is success
      - test_2_launchd_stop_result is changed
      - test_2_launchd_stop_result.status.previous_pid == test_1_launchd_start_result.status.current_pid
      - test_2_launchd_stop_result.status.previous_state == test_1_launchd_start_result.status.current_state
      - test_2_launchd_stop_result.status.current_state == 'stopped'
      - test_2_launchd_stop_result.status.current_pid == '-'

# -----------------------------------------------------------

- name: "[{{ item }}] Given a stopped service..."
  launchd:
    name: "{{ launchd_service_name }}"
    state: stopped
  become: true
  register: "test_3_launchd_stop_result"

- name: "[{{ item }}] Validate that service can be stopped after being already stopped"
  assert:
    that:
      - test_3_launchd_stop_result is success
      - not test_3_launchd_stop_result is changed
      - test_3_launchd_stop_result.status.previous_pid == '-'
      - test_3_launchd_stop_result.status.previous_state == 'stopped'
      - test_3_launchd_stop_result.status.current_state == 'stopped'
      - test_3_launchd_stop_result.status.current_pid == '-'

# -----------------------------------------------------------

- name: "[{{ item }}] Given a started service..."
  launchd:
    name: "{{ launchd_service_name }}"
    state: started
  become: true
  register: "test_4_launchd_start_result"

- name: "[{{ item }}] Validate that service was started..."
  assert:
    that:
      - test_4_launchd_start_result is success
      - test_4_launchd_start_result is changed
      - test_4_launchd_start_result.status.previous_pid == '-'
      - test_4_launchd_start_result.status.previous_state == 'stopped'
      - test_4_launchd_start_result.status.current_state == 'started'
      - test_4_launchd_start_result.status.current_pid != '-'
      - test_4_launchd_start_result.status.status_code == '0'

- name: "[{{ item }}] And when service is started again..."
  launchd:
    name: "{{ launchd_service_name }}"
    state: started
  become: true
  register: "test_5_launchd_start_result"

- name: "[{{ item }}] Validate that service is still in the same state as before"
  assert:
    that:
      - test_5_launchd_start_result is success
      - not test_5_launchd_start_result is changed
      - test_5_launchd_start_result.status.previous_pid == test_4_launchd_start_result.status.current_pid
      - test_5_launchd_start_result.status.previous_state == test_4_launchd_start_result.status.current_state
      - test_5_launchd_start_result.status.status_code == '0'
