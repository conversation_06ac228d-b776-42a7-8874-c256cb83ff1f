---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# -----------------------------------------------------------

- name: "[{{ item }}] Given a started service in check_mode"
  launchd:
    name: "{{ launchd_service_name }}"
    state: started
  become: true
  register: "test_1_launchd_start_result_check_mode"
  check_mode: true

- name: "[{{ item }}] Assert that everything work in check mode"
  assert:
    that:
      - test_1_launchd_start_result_check_mode is success
      - test_1_launchd_start_result_check_mode is changed

- name: "[{{ item }}] Given a started service..."
  launchd:
    name: "{{ launchd_service_name }}"
    state: started
  become: true
  register: "test_1_launchd_start_result"


- name: "[{{ item }}] The started service should run on port 21212"
  wait_for:
    port: 21212
    delay: 5
    timeout: 10

- name: "[{{ item }}] Deploy a new test service configuration with a new port 21213"
  template:
    src: "modified.{{ launchd_service_name }}.plist.j2"
    dest: "{{ launchd_plist_location }}"
  become: true

- name: "[{{ item }}] When reloading the service..."
  launchd:
    name: "{{ launchd_service_name }}"
    state: reloaded
  become: true
  register: "test_1_launchd_reload_result"

- name: "[{{ item }}] Validate that service was reloaded"
  assert:
    that:
      - test_1_launchd_reload_result is success
      - test_1_launchd_reload_result is changed
      - test_1_launchd_reload_result.status.previous_pid == test_1_launchd_start_result.status.current_pid
      - test_1_launchd_reload_result.status.previous_state == test_1_launchd_start_result.status.current_state
      - test_1_launchd_reload_result.status.current_state == 'stopped'
      - test_1_launchd_reload_result.status.current_pid == '-'

- name: "[{{ item }}] Start the service with the new configuration..."
  launchd:
    name: "{{ launchd_service_name }}"
    state: started
  become: true
  register: "test_1_launchd_start_result"


- name: "[{{ item }}] The started service should run on port 21213"
  wait_for:
    port: 21213
    delay: 5
    timeout: 10
