---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Test launchd module
  block:
    - name: Expect that launchctl exists
      stat:
        path: /bin/launchctl
      register: launchctl_check
      failed_when:
        - not launchctl_check.stat.exists

    - name: Run tests
      include_tasks: test.yml
      with_items:
        - test_unknown
        - test_start_stop
        - test_restart
        - test_unload
        - test_reload
        - test_runatload

  when: ansible_os_family == 'Darwin'
