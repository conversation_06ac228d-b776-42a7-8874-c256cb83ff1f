---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# -----------------------------------------------------------

- name: "[{{ item }}] Given a started service..."
  launchd:
    name: "{{ launchd_service_name }}"
    state: started
  become: true
  register: "test_1_launchd_start_result"


- name: "[{{ item }}] When unloading the service in check mode"
  launchd:
    name: "{{ launchd_service_name }}"
    state: unloaded
  become: true
  register: "test_1_launchd_unloaded_result_check_mode"
  check_mode: true

- name: "[{{ item }}] Validate that service was unloaded in check mode"
  assert:
    that:
      - test_1_launchd_unloaded_result_check_mode is success
      - test_1_launchd_unloaded_result_check_mode is changed


- name: "[{{ item }}] When unloading the service..."
  launchd:
    name: "{{ launchd_service_name }}"
    state: unloaded
  become: true
  register: "test_1_launchd_unloaded_result"

- name: "[{{ item }}] Validate that service was unloaded"
  assert:
    that:
      - test_1_launchd_unloaded_result is success
      - test_1_launchd_unloaded_result is changed
      - test_1_launchd_unloaded_result.status.previous_pid == test_1_launchd_start_result.status.current_pid
      - test_1_launchd_unloaded_result.status.previous_state == test_1_launchd_start_result.status.current_state
      - test_1_launchd_unloaded_result.status.current_state == 'unloaded'
      - test_1_launchd_unloaded_result.status.current_pid == '-'

# -----------------------------------------------------------

- name: "[{{ item }}] Given an unloaded service on an unloaded service..."
  launchd:
    name: "{{ launchd_service_name }}"
    state: unloaded
  become: true
  register: "test_2_launchd_unloaded_result"

- name: "[{{ item }}] Validate that service did not change and is still unloaded"
  assert:
    that:
      - test_2_launchd_unloaded_result is success
      - not test_2_launchd_unloaded_result is changed
      - test_2_launchd_unloaded_result.status.previous_pid == '-'
      - test_2_launchd_unloaded_result.status.previous_state == 'unloaded'
      - test_2_launchd_unloaded_result.status.current_state == 'unloaded'
      - test_2_launchd_unloaded_result.status.current_pid == '-'
