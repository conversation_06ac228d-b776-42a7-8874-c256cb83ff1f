####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Test code for iso_create module
# Copyright (c) 2020, <PERSON> (Tomorrow9) <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later
- name: install pycdlib
  pip:
    name: pycdlib
    extra_args: "-c {{ remote_constraints }}"
  register: install_pycdlib
- debug: var=install_pycdlib

- set_fact:
    output_test_dir: '{{ remote_tmp_dir }}/test_iso_create'

# - include_tasks: prepare_dest_dir.yml

- name: Copy files and directories
  copy:
    src: '{{ item }}'
    dest: '{{ remote_tmp_dir }}/{{ item }}'
  loop:
    - test1.cfg
    - test_dir

- name: Test check mode
  iso_create:
    src_files:
      - "{{ remote_tmp_dir }}/test1.cfg"
    dest_iso: "{{ output_test_dir }}/test.iso"
    interchange_level: 3
  register: iso_result
  check_mode: true
- debug: var=iso_result

- name: Check if iso file created
  stat:
    path: "{{ output_test_dir }}/test.iso"
  register: iso_file
- debug: var=iso_file
- assert:
    that:
      - iso_result is changed
      - iso_file.stat.exists == False

- name: Create iso file with a specified file
  iso_create:
    src_files:
      - "{{ remote_tmp_dir }}/test1.cfg"
    dest_iso: "{{ output_test_dir }}/test.iso"
    interchange_level: 3
  register: iso_result
- debug: var=iso_result

- name: Check if iso file created
  stat:
    path: "{{ output_test_dir }}/test.iso"
  register: iso_file

- assert:
    that:
      - iso_result is changed
      - iso_file.stat.exists == True

- name: Create iso file with a specified file and folder
  iso_create:
    src_files:
      - "{{ remote_tmp_dir }}/test1.cfg"
      - "{{ remote_tmp_dir }}/test_dir"
    dest_iso: "{{ output_test_dir }}/test1.iso"
    interchange_level: 3
  register: iso_result
- debug: var=iso_result

- name: Check if iso file created
  stat:
    path: "{{ output_test_dir }}/test1.iso"
  register: iso_file

- assert:
    that:
      - iso_result is changed
      - iso_file.stat.exists == True

- name: Create iso file with volume identification string
  iso_create:
    src_files:
      - "{{ remote_tmp_dir }}/test1.cfg"
    dest_iso: "{{ output_test_dir }}/test2.iso"
    vol_ident: "OEMDRV"
  register: iso_result
- debug: var=iso_result

- name: Check if iso file created
  stat:
    path: "{{ output_test_dir }}/test2.iso"
  register: iso_file

- assert:
    that:
      - iso_result is changed
      - iso_file.stat.exists == True

- name: Create iso file with Rock Ridge extension
  iso_create:
    src_files:
      - "{{ remote_tmp_dir }}/test1.cfg"
    dest_iso: "{{ output_test_dir }}/test3.iso"
    rock_ridge: "1.09"
  register: iso_result
- debug: var=iso_result

- name: Check if iso file created
  stat:
    path: "{{ output_test_dir }}/test3.iso"
  register: iso_file

- assert:
    that:
      - iso_result is changed
      - iso_file.stat.exists == True

- name: Create iso file with Joliet extension
  iso_create:
    src_files:
      - "{{ remote_tmp_dir }}/test1.cfg"
    dest_iso: "{{ output_test_dir }}/test4.iso"
    joliet: 3
  register: iso_result
- debug: var=iso_result

- name: Check if iso file created
  stat:
    path: "{{ output_test_dir }}/test4.iso"
  register: iso_file

- assert:
    that:
      - iso_result is changed
      - iso_file.stat.exists == True

- name: Create iso file with UDF enabled
  iso_create:
    src_files:
      - "{{ remote_tmp_dir }}/test1.cfg"
    dest_iso: "{{ output_test_dir }}/test5.iso"
    udf: true
  register: iso_result
- debug: var=iso_result

- name: Check if iso file created
  stat:
    path: "{{ output_test_dir }}/test5.iso"
  register: iso_file

- assert:
    that:
      - iso_result is changed
      - iso_file.stat.exists == True
