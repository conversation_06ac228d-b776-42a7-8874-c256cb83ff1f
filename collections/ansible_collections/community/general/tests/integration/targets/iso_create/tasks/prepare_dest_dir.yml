# Test code for iso_create module
# Copyright (c) 2020, <PERSON> (Tomorrow9) <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later
- name: Make sure our testing sub-directory does not exist
  file:
    path: '{{ output_test_dir }}'
    state: absent

- name: Create our testing sub-directory
  file:
    path: '{{ output_test_dir }}'
    state: directory
