---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Test code for ipwcli_dns
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: variables username, password, container, tld must be set
  fail:
    msg: 'Please set the variables: username, password, container and tld.'
  when: username is not defined or password is not defined or container is not defined or tld is not defined

- name: add a new A record
  ipwcli_dns:
    dnsname: example.{{ tld }}
    type: A
    container: '{{ container }}'
    address: 127.0.0.1
    ttl: 100
    username: '{{ username }}'
    password: '{{ password }}'
  register: result

- name: assert the new A record is added
  assert:
    that:
      - result is not failed
      - result is changed
      - result.record == 'arecord example.{{ tld }} 127.0.0.1 -set ttl=100;container={{ container }}'

- name: delete the A record
  ipwcli_dns:
    dnsname: example.{{ tld }}
    type: A
    container: '{{ container }}'
    address: 127.0.0.1
    ttl: 100
    username: '{{ username }}'
    password: '{{ password }}'
    state: absent
  register: result

- name: assert the new A record is deleted
  assert:
    that:
      - result is not failed
      - result is changed
      - result.record == 'arecord example.{{ tld }} 127.0.0.1 -set ttl=100;container={{ container }}'

- name: delete not existing SRV record
  ipwcli_dns:
    dnsname: _sip._tcp.test.example.{{ tld }}
    type: SRV
    container: '{{ container }}'
    target: example.{{ tld }}
    port: 5060
    username: '{{ username }}'
    password: '{{ password }}'
    state: absent
  register: result

- name: assert the new a record
  assert:
    that:
      - result is not failed
      - result is not changed
      - result.record ==
        'srvrecord _sip._tcp.test.example.{{ tld }} -set ttl=3600;container={{ container }};priority=10;weight=10;port=5060;target=example.{{ tld }}'

- name: add a SRV record with weight > 65535 against RFC 2782
  ipwcli_dns:
    dnsname: _sip._tcp.test.example.{{ tld }}
    type: SRV
    container: '{{ container }}'
    ttl: 100
    target: example.{{ tld }}
    port: 5060
    weight: 65536
    username: '{{ username }}'
    password: '{{ password }}'
  register: result
  ignore_errors: true

- name: assert the failure of the new SRV record
  assert:
    that:
      - result is failed
      - result is not changed
      - "'Out of UINT16 range' in result.stderr"

- name: add NAPTR record (check_mode)
  ipwcli_dns:
    dnsname: test.example.{{ tld }}
    type: NAPTR
    preference: 10
    container: '{{ container }}'
    ttl: 100
    order: 10
    service: 'SIP+D2T'
    replacement: '_sip._tcp.test.example.{{ tld }}.'
    flags: S
    username: '{{ username }}'
    password: '{{ password }}'
  check_mode: true
  register: result

- name: assert the NAPTR check_mode
  assert:
    that:
      - result is not failed
      - result is changed
