---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Stop wildfly (jboss)
  systemd:
    name: wildfly
    state: stopped
  ignore_errors: true

- name: Remove files
  file:
    path: '{{ item }}'
    state: absent
  loop:
    - '{{ wf_service_file_path }}'
    - '{{ default_deploy_root }}'
