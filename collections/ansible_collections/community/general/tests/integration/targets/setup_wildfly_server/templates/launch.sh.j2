#!/usr/bin/env bash
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

if [ "x$WILDFLY_HOME" = "x" ]; then
    WILDFLY_HOME="{{ wf_homedir }}"
fi

if [[ "$1" == "domain" ]]; then
    $WILDFLY_HOME/bin/domain.sh -c "$2" -b "$3"
else
    $WILDFLY_HOME/bin/standalone.sh -c "$2" -b "$3"
fi
