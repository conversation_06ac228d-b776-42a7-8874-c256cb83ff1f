---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- hosts: localhost
  tasks:
    - name: Test collection_version
      assert:
        that:
          # Collection that does not exist
          - query('community.general.collection_version', 'foo.bar') == [none]
          - lookup('community.general.collection_version', 'foo.bar', result_not_found='foo') == 'foo'
          # Collection that exists
          - lookup('community.general.collection_version', 'community.general') is string
          # Local collection
          - lookup('community.general.collection_version', 'testns.testcoll') == '0.0.1'
          # Local collection with no version
          - lookup('community.general.collection_version', 'testns.testcoll_nv') == '*'
          - lookup('community.general.collection_version', 'testns.testcoll_nv', result_no_version='') == ''
          # Local collection with MANIFEST.json
          - lookup('community.general.collection_version', 'testns.testcoll_mf') == '0.0.1'
          # Local collection with no galaxy.yml and no MANIFEST.json
          - lookup('community.general.collection_version', 'testns.testcoll_nothing') == '*'
          - lookup('community.general.collection_version', 'testns.testcoll_nothing', result_no_version='0.0.0') == '0.0.0'
          # Multiple collection names at once
          - lookup('community.general.collection_version', 'testns.testcoll', 'testns.testcoll_nv', 'testns.testcoll_nv', 'testns.testcoll_mf', 'foo.bar')
            == ['0.0.1', '*', '*', '0.0.1', none]

    - name: Invalid FQCN
      set_fact:
        test: "{{ query('community.general.collection_version', 'foo.bar.baz') }}"
      ignore_errors: true
      register: invalid_fqcn

    - name: Validate error message
      assert:
        that:
          - >
            '"foo.bar.baz" is not a FQCN' in invalid_fqcn.msg
