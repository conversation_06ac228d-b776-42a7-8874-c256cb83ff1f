---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Get ip information and register it in a variable
  scaleway_ip_info:
    region: par1
  register: ips

- name: Display ips variable
  debug:
    var: ips

- name: Ensure retrieval of ips info is success
  assert:
    that:
      - ips is success

- name: Get ip information and register it in a variable
  scaleway_ip_info:
    region: ams1
  register: ips_ams1

- name: Display ips variable
  debug:
    var: ips_ams1

- name: Ensure retrieval of ips info is success
  assert:
    that:
      - ips_ams1 is success
