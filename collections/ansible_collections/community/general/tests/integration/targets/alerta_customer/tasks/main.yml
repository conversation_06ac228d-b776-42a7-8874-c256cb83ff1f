---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Create customer (check mode)
  alerta_customer:
    alerta_url: "{{ alerta_url }}"
    api_username: "{{ alerta_user }}"
    api_password: "{{ alerta_password }}"
    customer: customer1
    match: <EMAIL>
  check_mode: true
  register: result

- name: Check result (check mode)
  assert:
    that:
      - result is changed

- name: Create customer
  alerta_customer:
    alerta_url: "{{ alerta_url }}"
    api_username: "{{ alerta_user }}"
    api_password: "{{ alerta_password }}"
    customer: customer1
    match: <EMAIL>
  register: result

- name: Check customer creation
  assert:
    that:
      - result is changed

- name: Test customer creation idempotency
  alerta_customer:
    alerta_url: "{{ alerta_url }}"
    api_username: "{{ alerta_user }}"
    api_password: "{{ alerta_password }}"
    customer: customer1
    match: <EMAIL>
  register: result

- name: Check customer creation idempotency
  assert:
    that:
      - result is not changed

- name: Delete customer (check mode)
  alerta_customer:
    alerta_url: "{{ alerta_url }}"
    api_username: "{{ alerta_user }}"
    api_password: "{{ alerta_password }}"
    customer: customer1
    match: <EMAIL>
    state: absent
  check_mode: true
  register: result

- name: Check customer deletion (check mode)
  assert:
    that:
      - result is changed

- name: Delete customer
  alerta_customer:
    alerta_url: "{{ alerta_url }}"
    api_username: "{{ alerta_user }}"
    api_password: "{{ alerta_password }}"
    customer: customer1
    match: <EMAIL>
    state: absent
  register: result

- name: Check customer deletion
  assert:
    that:
      - result is changed

- name: Test customer deletion idempotency
  alerta_customer:
    alerta_url: "{{ alerta_url }}"
    api_username: "{{ alerta_user }}"
    api_password: "{{ alerta_password }}"
    customer: customer1
    match: <EMAIL>
    state: absent
  register: result

- name: Check customer deletion idempotency
  assert:
    that:
      - result is not changed

- name: Delete non-existing customer (check mode)
  alerta_customer:
    alerta_url: "{{ alerta_url }}"
    api_username: "{{ alerta_user }}"
    api_password: "{{ alerta_password }}"
    customer: customer1
    match: <EMAIL>
    state: absent
  check_mode: true
  register: result

- name: Check non-existing customer deletion (check mode)
  assert:
    that:
      - result is not changed

- name: Create customer with api key
  alerta_customer:
    alerta_url: "{{ alerta_url }}"
    api_key: "{{ alerta_key }}"
    customer: customer1
    match: <EMAIL>
  register: result

- name: Check customer creation with api key
  assert:
    that:
      - result is changed

- name: Delete customer with api key
  alerta_customer:
    alerta_url: "{{ alerta_url }}"
    api_key: "{{ alerta_key }}"
    customer: customer1
    match: <EMAIL>
    state: absent
  register: result

- name: Check customer deletion with api key
  assert:
    that:
      - result is changed

- name: Use wrong api key
  alerta_customer:
    alerta_url: "{{ alerta_url }}"
    api_key: wrong_key
    customer: customer1
    match: <EMAIL>
  register: result
  ignore_errors: true

- name: Check customer creation with api key
  assert:
    that:
      - result is not changed
      - result is failed
