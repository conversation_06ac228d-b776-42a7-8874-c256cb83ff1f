---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Make sure setuptools is installed
  pip:
    name: setuptools
    state: present
  when: ansible_facts.distribution == 'MacOSX' and ansible_distribution_version is version('15', '>=')

- name: run python_requirements_info module
  python_requirements_info:
  register: basic_info

- name: ensure python_requirements_info returns desired info
  assert:
    that:
      - "'python' in basic_info"
      - "'python_version' in basic_info"
      - basic_info.python_version_info == ansible_python.version

- name: run python_requirements_info module
  python_requirements_info:
    dependencies:
      - notreal<1
      - pip>1
  register: dep_info

- name: ensure python_requirements_info returns desired info
  assert:
    that:
      - "'installed' in dep_info.valid.pip"
      - "'notreal' in dep_info.not_found"

- name: wrong specs
  python_requirements_info:
    dependencies:
      - ansible<
  register: wrong_spec1
  ignore_errors: true

- name: ensure wrong specs return error
  assert:
    that:
      - wrong_spec1 is failed
