####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Test code for the github_issue module.
#
# Copyright (c) 2017-2018, A<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Check if GitHub issue is closed or not
  github_issue:
    organization: "{{ organization }}"
    repo: "{{ repo }}"
    issue: "{{ issue }}"
    action: get_status
  register: get_status_0002

- assert:
    that:
      - get_status_0002 is changed
      - get_status_0002.issue_status == 'closed'

- name: Check if GitHub issue is closed or not
  github_issue:
    organization: "{{ organization }}"
    repo: "{{ repo }}"
    issue: "{{ non_existent_issue }}"
    action: get_status
  register: get_status_0003
  ignore_errors: true

- assert:
    that:
      - get_status_0003 is not changed
      - get_status_0003 is failed
      - "'Failed' in get_status_0003.msg"
