---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Because hwclock usually isn't available inside Docker containers in Shippable
# these tasks will detect if hwclock works and only run hwclock tests if it is
# supported. That is why it is recommended to run these tests locally with
# `--docker-privileged` on centos6, centos7 and ubuntu1404 images. Example
# command to run on centos6:
#
#   ansible-test integration --docker centos6 --docker-privileged -v timezone

##
## set path to timezone config files
##

- name: set config file path on Debian
  set_fact:
    timezone_config_file: '/etc/timezone'
  when: ansible_os_family == 'Debian'

- name: set config file path on RedHat
  set_fact:
    timezone_config_file: '/etc/sysconfig/clock'
  when: ansible_os_family == 'RedHat'

##
## set path to hwclock config files
##

- name: set config file path on Debian
  set_fact:
    hwclock_config_file: '/etc/default/rcS'
  when: ansible_os_family == 'Debian'

- name: set config file path on RedHat
  set_fact:
    hwclock_config_file: '/etc/sysconfig/clock'
  when: ansible_os_family == 'RedHat'

####
#### timezone tests
####

- name: make sure diffutils are installed on ArchLinux
  package:
    name: diffutils
    state: present
  when: ansible_distribution == 'Archlinux'

- name: make sure tzdata is installed on Alpine
  package:
    name: tzdata
    state: present
  when: ansible_distribution == 'Alpine'

- name: make sure hwclock is installed in Ubuntu 24.04
  package:
    name: util-linux-extra
    state: present
  when:
    - ansible_distribution == 'Ubuntu'
    - ansible_facts.distribution_major_version is version('24', '>=')

- name: make sure the dbus service is started under systemd
  systemd:
    name: dbus
    state: started
  when:
    - ansible_service_mgr == 'systemd'
    - ansible_distribution == 'Fedora'
    - ansible_facts.distribution_major_version is version('31', '<')


- name: Run tests
  # Skip tests on Fedora 31 and 32 because dbus fails to start unless the container is run in privileged mode.
  # Even then, it starts unreliably. This may be due to the move to cgroup v2 in Fedora 31 and 32.
  # https://www.redhat.com/sysadmin/fedora-31-control-group-v2
  when:
    - ansible_facts.distribution ~ ansible_facts.distribution_major_version not in ['Fedora31', 'Fedora32']
    - not (ansible_os_family == 'Alpine')  # TODO
    - not (ansible_distribution == 'Archlinux')  # TODO
  block:
    - name: set timezone to Etc/UTC
      timezone:
        name: Etc/UTC
      register: original_timezone

    - name: Value of original_timezone
      debug:
        msg: "{{ original_timezone }}"

    - block:
        - include_tasks: test.yml
      always:
        - name: Restore original system timezone - {{ original_timezone.diff.before.name }}
          timezone:
            name: "{{ original_timezone.diff.before.name }}"
          when: original_timezone is changed and original_timezone.diff.before.name != 'n/a'
