---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: collect repo configuration before test
  shell: "grep . /etc/zypp/repos.d/*"
  register: before

- name: ensure zypper ref works
  command: zypper -n ref

- block:
    - include_tasks: 'zypper_repository.yml'
  always:
    - name: remove repositories added during test
      community.general.zypper_repository:
        name: "{{item}}"
        state: absent
      with_items:
        - chrome1
        - chrome2
        - test
        - testrefresh
        - testprio
        - Apache_PHP_Modules
        - systemsmanagement_Uyuni_Stable
        - systemsmanagement_Uyuni_Utils

    - name: collect repo configuration after test
      shell: "grep . /etc/zypp/repos.d/*"
      register: after

    - name: verify repo configuration has been restored
      assert:
        that:
          - before.stdout == after.stdout

    - name: ensure zypper ref still works
      command: zypper -n ref

- block:
    - include_tasks: 'zypper_repository.yml'
  always:
    - name: remove repositories added during test
      community.general.zypper_repository:
        name: "test"
        state: absent