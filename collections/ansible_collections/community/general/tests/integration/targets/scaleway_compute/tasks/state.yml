---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Create a server (Check)
  check_mode: true
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: present
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'

  register: server_creation_check_task

- debug: var=server_creation_check_task

- assert:
    that:
      - server_creation_check_task is success
      - server_creation_check_task is changed

- name: Create a server
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: present
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    wait: true

  register: server_creation_task

- debug: var=server_creation_task

- assert:
    that:
      - server_creation_task is success
      - server_creation_task is changed

- name: Create a server (Confirmation)
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: present
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    wait: true

  register: server_creation_confirmation_task

- debug: var=server_creation_confirmation_task

- assert:
    that:
      - server_creation_confirmation_task is success
      - server_creation_confirmation_task is not changed

- name: Patch server tags (Check)
  check_mode: true
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: present
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    tags:
      - test
      - www
  register: server_patching_check_task

- debug: var=server_patching_check_task

- assert:
    that:
      - server_patching_check_task is success
      - server_patching_check_task is changed

- name: Patch server tags
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: present
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    wait: true
    tags:
      - test
      - www
  register: server_patching_task

- debug: var=server_patching_task

- assert:
    that:
      - server_patching_task is success
      - server_patching_task is changed

- name: Patch server tags (Confirmation)
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: present
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    wait: true
    tags:
      - test
      - www
  register: server_patching_confirmation_task

- debug: var=server_patching_confirmation_task

- assert:
    that:
      - server_patching_confirmation_task is success
      - server_patching_confirmation_task is not changed

- name: Run it (Check mode)
  check_mode: true
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: running
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    tags:
      - test
      - www
  register: server_run_check_task

- debug: var=server_run_check_task

- assert:
    that:
      - server_run_check_task is success
      - server_run_check_task is changed

- name: Run it
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: running
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    wait: true
    tags:
      - test
      - www
  register: server_run_task

- debug: var=server_run_task

- assert:
    that:
      - server_run_task is success
      - server_run_task is changed

- name: Run it
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: running
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    wait: true
    tags:
      - test
      - www
  register: server_run_confirmation_task

- debug: var=server_run_confirmation_task

- assert:
    that:
      - server_run_confirmation_task is success
      - server_run_confirmation_task is not changed

- name: Reboot it (Check mode)
  check_mode: true
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: restarted
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    tags:
      - test
      - www
  register: server_reboot_check_task

- debug: var=server_reboot_check_task

- assert:
    that:
      - server_reboot_check_task is success
      - server_reboot_check_task is changed

- name: Reboot it
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: restarted
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    wait: true
    tags:
      - test
      - www
  register: server_reboot_task

- debug: var=server_reboot_task

- assert:
    that:
      - server_reboot_task is success
      - server_reboot_task is changed

- name: Stop it (Check mode)
  check_mode: true
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: stopped
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    tags:
      - test
      - www
  register: server_stop_check_task

- debug: var=server_stop_check_task

- assert:
    that:
      - server_stop_check_task is success
      - server_stop_check_task is changed

- name: Stop it
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: stopped
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    wait: true
    tags:
      - test
      - www
  register: server_stop_task

- debug: var=server_stop_task

- assert:
    that:
      - server_stop_task is success
      - server_stop_task is changed

- name: Stop it (Confirmation)
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: stopped
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    wait: true
    tags:
      - test
      - www
  register: server_stop_confirmation_task

- debug: var=server_stop_confirmation_task

- assert:
    that:
      - server_stop_confirmation_task is success
      - server_stop_confirmation_task is not changed

- name: Destroy it (Check mode)
  check_mode: true
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: absent
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    tags:
      - test
      - www
  register: server_destroy_check_task

- debug: var=server_destroy_check_task

- assert:
    that:
      - server_destroy_check_task is success
      - server_destroy_check_task is changed

- name: Destroy it
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: absent
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    wait: true
    tags:
      - test
      - www
  register: server_destroy_task

- debug: var=server_destroy_task

- assert:
    that:
      - server_destroy_task is success
      - server_destroy_task is changed

- name: Destroy it (Confirmation)
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: absent
    image: '{{ scaleway_image_id }}'
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
    wait: true
    tags:
      - test
      - www
  register: server_destroy_confirmation_task

- debug: var=server_destroy_confirmation_task

- assert:
    that:
      - server_destroy_confirmation_task is success
      - server_destroy_confirmation_task is not changed

- name: Testing for unauthorized organization
  ignore_errors: true
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: present
    image: '{{ scaleway_image_id }}'
    organization: this-organization-does-not-exists
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
  register: unauthorized_organization_task

- debug: var=unauthorized_organization_task

- assert:
    that:
      - unauthorized_organization_task is not success
      - unauthorized_organization_task is not changed

- name: Testing for unexisting image
  ignore_errors: true
  scaleway_compute:
    name: '{{ scaleway_name }}'
    state: present
    image: this-image-does-not-exists
    organization: '{{ scaleway_organization }}'
    region: '{{ scaleway_region }}'
    commercial_type: '{{ scaleway_commerial_type }}'
  register: unexisting_image_check

- debug: var=unexisting_image_check

- assert:
    that:
      - unexisting_image_check is not success
      - unexisting_image_check is not changed
      - unexisting_image_check.msg == "Error in getting image this-image-does-not-exists on https://cp-{{scaleway_region}}.scaleway.com"
