---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Configuring a handler with missing set parameters should fail
  sensu_handler:
    name: "set"
    type: "set"
  register: failure
  ignore_errors: true

- name: Assert that configuring a handler with missing set parameters fails
  assert:
    that:
      - failure is failed
      - "'the following are missing: handlers' in failure['msg']"

- name: Configure a set handler
  sensu_handler:
    name: "set"
    type: "set"
    handlers:
      - anotherhandler
  register: handler

- name: Retrieve configuration file
  slurp:
    src: "{{ handler['file'] }}"
  register: handler_config

- name: Validate set handler return data
  assert:
    that:
      - "handler is successful"
      - "handler is changed"
      - "handler['name'] == 'set'"
      - "handler['file'] == '/etc/sensu/conf.d/handlers/set.json'"
      - "handler['config']['type'] == 'set'"
      - "'anotherhandler' in handler['config']['handlers']"
      - "handler['config']['handle_flapping'] == false"
      - "handler['config']['handle_silenced'] == false"

- name: Assert that the handler configuration file is actually configured properly
  vars:
    config: "{{ handler_config.content | b64decode | from_json }}"
  assert:
    that:
      - "'set' in config['handlers']"
      - "config['handlers']['set']['type'] == 'set'"
      - "'anotherhandler' in config['handlers']['set']['handlers']"
      - "config['handlers']['set']['handle_flapping'] == false"
      - "config['handlers']['set']['handle_silenced'] == false"
