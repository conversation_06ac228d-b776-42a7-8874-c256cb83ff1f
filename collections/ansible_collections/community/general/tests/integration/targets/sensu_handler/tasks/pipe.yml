---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Note: Pipe handlers are also tested and used as part of basic main.yml coverage
- name: Configuring a handler with missing pipe parameters should fail
  sensu_handler:
    name: "pipe"
    type: "pipe"
  register: failure
  ignore_errors: true

- name: Assert that configuring a handler with missing pipe parameters fails
  assert:
    that:
      - failure is failed
      - "'the following are missing: command' in failure['msg']"

- name: Configure a handler with pipe parameters
  sensu_handler:
    name: "pipe"
    type: "pipe"
    command: "/bin/bash"
  register: handler
