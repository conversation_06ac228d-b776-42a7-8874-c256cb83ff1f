---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Configuring a handler with missing tcp parameters should fail
  sensu_handler:
    name: "tcp"
    type: "tcp"
  register: failure
  ignore_errors: true

- name: Assert that configuring a handler with missing tcp parameters fails
  assert:
    that:
      - failure is failed
      - "'the following are missing: socket' in failure['msg']"

- name: Configure a tcp handler
  sensu_handler:
    name: "tcp"
    type: "tcp"
    socket:
      host: 127.0.0.1
      port: 8000
  register: handler

- name: Retrieve configuration file
  slurp:
    src: "{{ handler['file'] }}"
  register: handler_config

- name: Validate tcp handler return data
  assert:
    that:
      - "handler is successful"
      - "handler is changed"
      - "handler['name'] == 'tcp'"
      - "handler['file'] == '/etc/sensu/conf.d/handlers/tcp.json'"
      - "handler['config']['type'] == 'tcp'"
      - "handler['config']['socket']['host'] == '127.0.0.1'"
      - "handler['config']['socket']['port'] == 8000"
      - "handler['config']['handle_flapping'] == false"
      - "handler['config']['handle_silenced'] == false"

- name: Assert that the handler configuration file is actually configured properly
  vars:
    config: "{{ handler_config.content | b64decode | from_json }}"
  assert:
    that:
      - "'tcp' in config['handlers']"
      - "config['handlers']['tcp']['type'] == 'tcp'"
      - "config['handlers']['tcp']['socket']['host'] == '127.0.0.1'"
      - "config['handlers']['tcp']['socket']['port'] == 8000"
      - "config['handlers']['tcp']['handle_flapping'] == false"
      - "config['handlers']['tcp']['handle_silenced'] == false"
