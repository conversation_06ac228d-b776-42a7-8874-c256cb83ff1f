---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- when: ansible_os_family == 'Archlinux'
  block:
    # Add more tests here by including more task files:
    - include_tasks: 'basic.yml'
    - include_tasks: 'package_urls.yml'
    - include_tasks: 'remove_nosave.yml'
    - include_tasks: 'update_cache.yml'
    - include_tasks: 'locally_installed_package.yml'
    - include_tasks: 'reason.yml'
    - include_tasks: 'yay-become.yml'
