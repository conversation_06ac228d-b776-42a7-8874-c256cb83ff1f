---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- debug:
    msg: Running tests/schema.yml

####################################################################
## Search ##########################################################
####################################################################
- name: Test for ldap schema output
  ldap_search:
    dn: "ou=users,dc=example,dc=com"
    scope: "onelevel"
    schema: true
  ignore_errors: true
  register: output

- name: Assert that the schema output is correct
  assert:
    that:
      - output is not failed
      - output.results | length >= 1
      - "{{ 'displayName' in output.results.0.attrs }}"
