---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- debug:
    msg: Running tests/pages.yml

####################################################################
## Search ##########################################################
####################################################################
- name: Test paged search for all users
  ldap_search:
    dn: "ou=users,dc=example,dc=com"
    scope: "onelevel"
    page_size: 1
  ignore_errors: true
  register: output

- name: assert that the right number of results are returned
  assert:
    that:
      - output is not failed
      - output.results | length == 2
