---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: 'Create directory for Node'
  file:
    path: /usr/local/lib/nodejs
    state: directory

- name: 'Download Nodejs'
  unarchive:
    src: 'https://ansible-ci-files.s3.amazonaws.com/test/integration/targets/yarn/{{ nodejs_path }}.tar.gz'
    dest: '{{ remote_tmp_dir }}'
    remote_src: true
    creates: '{{ remote_tmp_dir }}/{{ nodejs_path }}.tar.gz'

- name: 'Download Yarn'
  unarchive:
    src: 'https://ansible-ci-files.s3.amazonaws.com/test/integration/targets/yarn/yarn-v{{yarn_version}}.tar.gz'
    dest: '{{ remote_tmp_dir }}'
    remote_src: true
    creates: '{{ remote_tmp_dir }}/yarn-v{{yarn_version}}_pkg.tar.gz'

- name: 'Copy node to directory created earlier'
  command: "mv {{ remote_tmp_dir }}/{{ nodejs_path }} /usr/local/lib/nodejs/{{nodejs_path}}"

# Clean up before running tests
- name: Remove any previous Nodejs modules
  file:
    path: '{{remote_tmp_dir}}/node_modules'
    state: absent

# Set vars for our test harness
- vars:
    # node_bin_path: "/usr/local/lib/nodejs/node-v{{nodejs_version}}/bin"
    node_bin_path: "/usr/local/lib/nodejs/{{ nodejs_path }}/bin"
    yarn_bin_path: "{{ remote_tmp_dir }}/yarn-v{{ yarn_version }}/bin"
    package: 'iconv-lite'
  environment:
    PATH: "{{ node_bin_path }}:{{ansible_env.PATH}}"
    YARN_IGNORE_ENGINES: true
  block:

    # Get the version of Yarn and register to a variable
    - shell: '{{ yarn_bin_path }}/yarn --version'
      environment:
        PATH: '{{ node_bin_path }}:{{ ansible_env.PATH }}'
      register: yarn_version

    - name: 'Create dummy package.json'
      template:
        src: package.j2
        dest: '{{ remote_tmp_dir }}/package.json'

    - name: 'Install all packages.'
      yarn:
        path: '{{ remote_tmp_dir }}'
        executable: '{{ yarn_bin_path }}/yarn'
        state: present
      environment:
        PATH: '{{ node_bin_path }}:{{ ansible_env.PATH }}'

    - name: 'Install the same package from package.json again.'
      yarn:
        path: '{{ remote_tmp_dir }}'
        executable: '{{ yarn_bin_path }}/yarn'
        name: '{{ package }}'
        state: present
      environment:
        PATH: '{{ node_bin_path }}:{{ ansible_env.PATH }}'
      register: yarn_install

    - assert:
        that:
          - not (yarn_install is changed)

    - name: 'Install all packages in check mode.'
      yarn:
        path: '{{ remote_tmp_dir }}'
        executable: '{{ yarn_bin_path }}/yarn'
        state: present
      environment:
        PATH: '{{ node_bin_path }}:{{ ansible_env.PATH }}'
      check_mode: true
      register: yarn_install_check

    - name: verify test yarn global installation in check mode
      assert:
        that:
          - yarn_install_check.err is defined
          - yarn_install_check.out is defined
          - yarn_install_check.err is none
          - yarn_install_check.out is none

    - name: 'Install package with explicit version (older version of package)'
      yarn:
        path: '{{ remote_tmp_dir }}'
        executable: '{{ yarn_bin_path }}/yarn'
        name: left-pad
        version: 1.1.0
        state: present
      environment:
        PATH: '{{ node_bin_path }}:{{ ansible_env.PATH }}'
      register: yarn_install_old_package

    - assert:
        that:
          - yarn_install_old_package is changed

    - name: 'Again but without explicit executable path'
      yarn:
        path: '{{ remote_tmp_dir }}'
        name: left-pad
        version: 1.1.0
        state: present
      environment:
        PATH: '{{ yarn_bin_path }}:{{ node_bin_path }}:{{ ansible_env.PATH }}'

    - name: 'Upgrade old package'
      yarn:
        path: '{{ remote_tmp_dir }}'
        executable: '{{ yarn_bin_path }}/yarn'
        name: left-pad
        state: latest
      environment:
        PATH: '{{ node_bin_path }}:{{ ansible_env.PATH }}'
      register: yarn_update_old_package

    - assert:
        that:
          - yarn_update_old_package is changed

    - name: 'Remove a package'
      yarn:
        path: '{{ remote_tmp_dir }}'
        executable: '{{ yarn_bin_path }}/yarn'
        name: '{{ package }}'
        state: absent
      environment:
        PATH: '{{ node_bin_path }}:{{ ansible_env.PATH }}'
      register: yarn_uninstall_package

    - name: 'Assert package removed'
      assert:
        that:
          - yarn_uninstall_package is changed

    - name: 'Global install binary with explicit version (older version of package)'
      yarn:
        global: true
        executable: '{{ yarn_bin_path }}/yarn'
        name: prettier
        version: 2.0.0
        state: present
      environment:
        PATH: '{{ node_bin_path }}:{{ ansible_env.PATH }}'
      register: yarn_global_install_old_binary

    - assert:
        that:
          - yarn_global_install_old_binary is changed

    - name: 'Global upgrade old binary'
      yarn:
        global: true
        executable: '{{ yarn_bin_path }}/yarn'
        name: prettier
        state: latest
      environment:
        PATH: '{{ node_bin_path }}:{{ ansible_env.PATH }}'
      register: yarn_global_update_old_binary

    - assert:
        that:
          - yarn_global_update_old_binary is changed

    - name: 'Global remove a binary'
      yarn:
        global: true
        executable: '{{ yarn_bin_path }}/yarn'
        name: prettier
        state: absent
      environment:
        PATH: '{{ node_bin_path }}:{{ ansible_env.PATH }}'
      register: yarn_global_uninstall_binary

    - assert:
        that:
          - yarn_global_uninstall_binary is changed

    - name: 'Global install package with no binary with explicit version (older version of package)'
      yarn:
        global: true
        executable: '{{ yarn_bin_path }}/yarn'
        name: left-pad
        version: 1.1.0
        state: present
      environment:
        PATH: '{{ node_bin_path }}:{{ ansible_env.PATH }}'
      register: yarn_global_install_old_package

    - assert:
        that:
          - yarn_global_install_old_package is changed

    - name: 'Global upgrade old package with no binary'
      yarn:
        global: true
        executable: '{{ yarn_bin_path }}/yarn'
        name: left-pad
        state: latest
      environment:
        PATH: '{{ node_bin_path }}:{{ ansible_env.PATH }}'
      register: yarn_global_update_old_package

    - assert:
        that:
          - yarn_global_update_old_package is changed

    - name: 'Global remove a package with no binary'
      yarn:
        global: true
        executable: '{{ yarn_bin_path }}/yarn'
        name: left-pad
        state: absent
      environment:
        PATH: '{{ node_bin_path }}:{{ ansible_env.PATH }}'
      register: yarn_global_uninstall_package

    - assert:
        that:
          - yarn_global_uninstall_package is changed
