####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) 2022, Ansible Project
# Copyright (c) 2022, VMware, Inc. All Rights Reserved.
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Skip some platforms which does not support ansible.posix.mount
  meta: end_play
  when: ansible_distribution in ['Alpine']

- set_fact:
    test_dir: '{{ remote_tmp_dir }}/test_iso_customize'

- include_tasks: prepare.yml

- name: Create iso file with a specified file and directory
  community.general.iso_create:
    src_files:
      - "{{ test_dir }}/test01.cfg"
      - "{{ test_dir }}/test02.cfg"
    dest_iso: "{{ test_dir }}/test.iso"
    interchange_level: 3

- include_tasks: iso_customize.yml
  vars:
    dest_iso_name: "iso_customize.iso"

- name: Create an ISO file with Rock Ridge extension
  community.general.iso_create:
    src_files:
      - "{{ test_dir }}/test01.cfg"
      - "{{ test_dir }}/test02.cfg"
    dest_iso: "{{ test_dir }}/test.iso"
    rock_ridge: "1.09"

- include_tasks: iso_customize.yml
  vars:
    dest_iso_name: "iso_customize_rr.iso"

- name: Create an ISO file with Joliet support
  community.general.iso_create:
    src_files:
      - "{{ test_dir }}/test01.cfg"
      - "{{ test_dir }}/test02.cfg"
    dest_iso: "{{ test_dir }}/test.iso"
    interchange_level: 3
    joliet: 3
    vol_ident: AUTOINSTALL

- include_tasks: iso_customize.yml
  vars:
    dest_iso_name: "iso_customize_joliet.iso"

- name: Create iso file with UDF enabled
  community.general.iso_create:
    src_files:
      - "{{ test_dir }}/test01.cfg"
      - "{{ test_dir }}/test02.cfg"
    dest_iso: "{{ test_dir }}/test.iso"
    udf: true

- include_tasks: iso_customize.yml
  vars:
    dest_iso_name: "iso_customize_udf.iso"

# Create initial iso for customzing with only option add_files/delete_files
- name: Create iso file with a specified file and directory
  community.general.iso_create:
    src_files:
      - "{{ test_dir }}/test01.cfg"
    dest_iso: "{{ test_dir }}/test1.iso"
    interchange_level: 3

- include_tasks: iso_customize_add_files.yml
  vars:
    dest_iso_name: "iso_customize_add.iso"

- include_tasks: iso_customize_delete_files.yml
  vars:
    dest_iso_name: "iso_customize_delete.iso"

# Test: misc exception
- include_tasks: iso_customize_exception.yml
  vars:
    dest_iso_name: "iso_customize_exception.iso"

- name: Delete testing sub-directory
  ansible.builtin.file:
    path: '{{ test_dir }}'
    state: absent
