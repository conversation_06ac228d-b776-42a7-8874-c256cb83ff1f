---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Get organization information and register it in a variable
  scaleway_organization_info:
  register: organizations

- name: Display organizations variable
  debug:
    var: organizations

- name: Ensure retrieval of organizations info is success
  assert:
    that:
      - organizations is success
