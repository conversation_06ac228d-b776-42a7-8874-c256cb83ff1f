---
# Copyright (c) 2020 <PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: test remove a dns record in check mode
  community.general.gandi_livedns:
    api_key: "{{ gandi_api_key }}"
    record: "{{ item.record }}"
    domain: "{{ gandi_livedns_domain_name }}"
    values: "{{ item.update_values | default(item['values']) }}"
    type: "{{ item.type }}"
    state: absent
  check_mode: true
  register: result
- name: verify test remove a dns record in check mode
  assert:
    that:
      - result is changed

- name: test remove a dns record
  community.general.gandi_livedns:
    api_key: "{{ gandi_api_key }}"
    record: "{{ item.record }}"
    domain: "{{ gandi_livedns_domain_name }}"
    values: "{{ item.update_values | default(item['values']) }}"
    type: "{{ item.type }}"
    state: absent
  register: result
- name: verify test remove a dns record
  assert:
    that:
      - result is changed

- name: test remove a dns record idempotence
  community.general.gandi_livedns:
    api_key: "{{ gandi_api_key }}"
    record: "{{ item.record }}"
    domain: "{{ gandi_livedns_domain_name }}"
    values: "{{ item.update_values | default(item['values']) }}"
    type: "{{ item.type }}"
    state: absent
  register: result
- name: verify test remove a dns record idempotence
  assert:
    that:
      - result is not changed

- name: test remove second dns record idempotence
  community.general.gandi_livedns:
    api_key: "{{ gandi_api_key }}"
    record: "{{ item.record }}"
    domain: "{{ gandi_livedns_domain_name }}"
    values: "{{ item['values'] }}"
    type: "{{ item.type }}"
    state: absent
  register: result
- name: verify test remove a dns record idempotence
  assert:
    that:
      - result is not changed
