---
# Copyright (c) 2020 <PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: test update or add another dns record in check mode
  community.general.gandi_livedns:
    api_key: "{{ gandi_api_key }}"
    record: "{{ item.record }}"
    domain: "{{ gandi_livedns_domain_name }}"
    values: "{{ item.update_values | default(item['values']) }}"
    ttl: "{{ item.update_ttl | default(item.ttl) }}"
    type: "{{ item.type }}"
  check_mode: true
  register: result
- name: verify test update in check mode
  assert:
    that:
      - result is changed
      - result.record['values'] == (item.update_values | default(item['values']))
      - result.record.record == item.record
      - result.record.type == item.type
      - result.record.ttl == (item.update_ttl | default(item.ttl))

- name: test update or add another dns record
  community.general.gandi_livedns:
    api_key: "{{ gandi_api_key }}"
    record: "{{ item.record }}"
    domain: "{{ gandi_livedns_domain_name }}"
    values: "{{ item.update_values | default(item['values']) }}"
    ttl: "{{ item.update_ttl | default(item.ttl) }}"
    type: "{{ item.type }}"
  register: result
- name: verify test update a dns record
  assert:
    that:
      - result is changed
      - result.record['values'] == (item.update_values | default(item['values']))
      - result.record.record == item.record
      - result.record.ttl == (item.update_ttl | default(item.ttl))
      - result.record.type == item.type

- name: test update or add another dns record idempotence
  community.general.gandi_livedns:
    api_key: "{{ gandi_api_key }}"
    record: "{{ item.record }}"
    domain: "{{ gandi_livedns_domain_name }}"
    values: "{{ item.update_values | default(item['values']) }}"
    ttl: "{{ item.update_ttl | default(item.ttl) }}"
    type: "{{ item.type }}"
  register: result
- name: verify test update a dns record idempotence
  assert:
    that:
      - result is not changed
      - result.record['values'] == (item.update_values | default(item['values']))
      - result.record.record == item.record
      - result.record.ttl == (item.update_ttl | default(item.ttl))
      - result.record.type == item.type
