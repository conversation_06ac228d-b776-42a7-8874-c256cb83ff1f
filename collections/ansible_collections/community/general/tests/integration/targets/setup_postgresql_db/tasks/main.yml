---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Exit when Suse because it causes CI problems
- meta: end_play
  when: ansible_os_family == 'Suse'

# To avoid hangings on service start/stop postgres during CI runs:
- meta: end_play
  when: ansible_facts.distribution == 'CentOS' and ansible_facts.distribution_major_version == '8'

# Temporary disable Fedora 34
- meta: end_play
  when: ansible_facts.distribution == 'Fedora' and ansible_facts.distribution_major_version == '34'

- name: python 2
  set_fact:
    python_suffix: ''
  when: ansible_python_version is version('3', '<')

- name: python 3
  set_fact:
    python_suffix: -py3
  when: ansible_python_version is version('3', '>=')

- name: Include distribution and Python version specific variables
  include_vars: '{{ lookup(''first_found'', params) }}'
  vars:
    params:
      files:
        - '{{ ansible_distribution }}-{{ ansible_distribution_major_version }}{{ python_suffix }}.yml'
        - '{{ ansible_distribution }}-{{ ansible_distribution_version }}{{ python_suffix }}.yml'
        - '{{ ansible_os_family }}{{ python_suffix }}.yml'
        - default{{ python_suffix }}.yml
      paths:
        - '{{ role_path }}/vars'

- name: make sure the dbus service is started under systemd
  systemd:
    name: dbus
    state: started
  when: ansible_service_mgr == 'systemd' and ansible_distribution == 'Fedora'

- name: Kill all postgres processes
  shell: 'pkill -u {{ pg_user }}'
  become: true
  when: ansible_facts.distribution == 'CentOS' and ansible_facts.distribution_major_version == '8'
  ignore_errors: true

- name: stop postgresql service
  service: name={{ postgresql_service }} state=stopped
  ignore_errors: true

- name: remove old db (RedHat or Suse)
  file:
    path: '{{ pg_dir }}'
    state: absent
  ignore_errors: true
  when: ansible_os_family == "RedHat" or ansible_os_family == "Suse"

- name: remove old db (FreeBSD)
  file:
    path: '{{ pg_dir }}'
    state: absent
  ignore_errors: true
  when: ansible_os_family == "FreeBSD"

- name: remove old db config and files (debian)
  file:
    path: '{{ loop_item }}'
    state: absent
  ignore_errors: true
  when: ansible_os_family == "Debian"
  loop:
    - /etc/postgresql
    - /var/lib/postgresql
  loop_control:
    loop_var: loop_item

- name: install dependencies for postgresql test
  package:
    name: '{{ postgresql_package_item }}'
    state: present
  with_items: '{{ postgresql_packages }}'
  loop_control:
    loop_var: postgresql_package_item

- name: initialize postgres (FreeBSD)
  command: /usr/local/etc/rc.d/postgresql oneinitdb
  when: ansible_os_family == "FreeBSD"

- name: Initialize postgres (RedHat systemd)
  command: postgresql-setup initdb
  when: ansible_os_family == "RedHat" and ansible_service_mgr == "systemd"

- name: Initialize postgres (RedHat sysv)
  command: /sbin/service postgresql initdb
  when: ansible_os_family == "RedHat" and ansible_service_mgr != "systemd"

- name: Initialize postgres (Archlinux)
  command: su - postgres -c "initdb --locale en_US.UTF-8 -D '/var/lib/postgres/data'"
  when: ansible_os_family == "Archlinux"

- name: Initialize postgres (Alpine)
  command: su - postgres -c "initdb --locale en_US.UTF-8 -D '/var/lib/postgresql/data'"
  when: ansible_os_family == "Alpine"

- name: Initialize postgres (Debian)
  shell: . /usr/share/postgresql-common/maintscripts-functions && set_system_locale && /usr/bin/pg_createcluster -u postgres {{ pg_ver }} main
  args:
    creates: /etc/postgresql/{{ pg_ver }}/
  when: ansible_os_family == 'Debian'

- name: Initialize postgres (Suse)
  service: name=postgresql state=stopped
  when: ansible_os_family == 'Suse'

- name: Pause between stop and start postgresql
  pause:
    seconds: 5
  when: ansible_os_family == 'Suse'

- name: Make installable on Arch
  community.general.ini_file:
    path: /usr/lib/systemd/system/postgresql.service
    section: Service
    option: "{{ item }}"
    state: absent
  loop:
    - PrivateTmp
    - ProtectHome
    - ProtectSystem
    - NoNewPrivileges
    - ProtectControlGroups
    - ProtectKernelModules
    - ProtectKernelTunables
    - PrivateDevices
    - RestrictAddressFamilies
    - RestrictNamespaces
    - RestrictRealtime
    - SystemCallArchitectures
  when: ansible_distribution == 'Archlinux'

- name: Make installable on Arch
  systemd:
    daemon_reload: true
  when: ansible_distribution == 'Archlinux'

- name: Initialize postgres (Suse)
  service: name=postgresql state=started
  when: ansible_os_family == 'Suse'

- name: Copy pg_hba into place
  template:
    src: files/pg_hba.conf
    dest: '{{ pg_hba_location }}'
    owner: '{{ pg_user }}'
    group: '{{ pg_group }}'
    mode: '0644'

- name: Generate locales (Debian)
  locale_gen:
    name: '{{ item }}'
    state: present
  with_items:
    - pt_BR
    - es_ES
  when: ansible_os_family == 'Debian'

- block:
    - name: Install langpacks (RHEL8)
      yum:
        name:
          - glibc-langpack-es
          - glibc-langpack-pt
          - glibc-all-langpacks
        state: present
      when: ansible_distribution_major_version is version('8', '>=')

    - name: Check if locales need to be generated (RedHat)
      shell: localedef --list-archive | grep -a -q '^{{ locale }}$'
      register: locale_present
      ignore_errors: true
      with_items:
        - es_ES
        - pt_BR
      loop_control:
        loop_var: locale

    - name: Reinstall internationalization files
      shell: yum -y reinstall glibc-common || yum -y install glibc-common
      when: locale_present is failed

    - name: Generate locale (RedHat)
      command: localedef -f ISO-8859-1 -i {{ item.locale }} {{ item.locale }}
      when: item is failed
      with_items: '{{ locale_present.results }}'
  when: ansible_os_family == 'RedHat' and ansible_distribution != 'Fedora'

- name: Install glibc langpacks (Fedora >= 24)
  package:
    name: '{{ item }}'
    state: latest
  with_items:
    - glibc-langpack-es
    - glibc-langpack-pt
  when: ansible_distribution == 'Fedora' and ansible_distribution_major_version is version('24', '>=')

- name: enable postgresql service (FreeBSD)
  lineinfile:
    path: /etc/rc.conf
    line: postgresql_enable="YES"
  when: ansible_os_family == "FreeBSD"

- name: start postgresql service
  service: name={{ postgresql_service }} state=started

- name: Pause between start and stop
  pause:
    seconds: 5

- name: Kill all postgres processes
  shell: 'pkill -u {{ pg_user }}'
  become: true
  when: ansible_facts.distribution == 'CentOS' and ansible_facts.distribution_major_version == '8'
  ignore_errors: true
  register: terminate

- name: Stop postgresql service
  service: name={{ postgresql_service }} state=stopped
  when: terminate is not succeeded

- name: Pause between stop and start
  pause:
    seconds: 5

- name: Start postgresql service
  service: name={{ postgresql_service }} state=started

- name: copy control file for dummy ext
  copy:
    src: dummy.control
    dest: /usr/share/postgresql/{{ pg_ver }}/extension/dummy.control
    mode: '0444'
  when: ansible_os_family == 'Debian'

- name: copy version files for dummy ext
  copy:
    src: '{{ item }}'
    dest: /usr/share/postgresql/{{ pg_ver }}/extension/{{ item }}
    mode: '0444'
  with_items:
    - dummy--1.0.sql
    - dummy--2.0.sql
    - dummy--3.0.sql
  when: ansible_os_family == 'Debian'

- name: add update paths
  file:
    path: /usr/share/postgresql/{{ pg_ver }}/extension/{{ item }}
    mode: '0444'
    state: touch
  with_items:
    - dummy--1.0--2.0.sql
    - dummy--2.0--3.0.sql
  when: ansible_os_family == 'Debian'

- name: Get PostgreSQL version
  become_user: '{{ pg_user }}'
  become: true
  shell: echo 'SHOW SERVER_VERSION' | psql --tuples-only --no-align --dbname postgres
  register: postgres_version_resp

- name: Print PostgreSQL server version
  debug:
    msg: '{{ postgres_version_resp.stdout }}'
