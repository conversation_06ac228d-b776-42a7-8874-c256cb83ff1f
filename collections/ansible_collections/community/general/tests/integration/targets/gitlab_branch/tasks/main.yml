---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Install required libs
  pip:
    name: python-gitlab
    state: present

- name: Create {{ gitlab_project_name }}
  gitlab_project:
    server_url: "{{ gitlab_host }}"
    validate_certs: false
    login_token: "{{ gitlab_login_token }}"
    name: "{{ gitlab_project_name }}"
    initialize_with_readme: true
    state: present

- name: Create branch {{ gitlab_branch }}
  community.general.gitlab_branch:
    api_url: https://gitlab.com
    api_token: secret_access_token
    project: "{{ gitlab_project_name }}"
    branch: "{{ gitlab_branch }}"
    ref_branch: main
    state: present

- name: Create branch {{ gitlab_branch }} ( Idempotency test )
  community.general.gitlab_branch:
    api_url: https://gitlab.com
    api_token: secret_access_token
    project: "{{ gitlab_project_name }}"
    branch: "{{ gitlab_branch }}"
    ref_branch: main
    state: present
  register: create_branch

- name: Test module is idempotent
  assert:
    that:
      - create_branch is not changed

- name: Cleanup branch {{ gitlab_branch }}
  community.general.gitlab_branch:
    api_url: https://gitlab.com
    api_token: secret_access_token
    project: "{{ gitlab_project_name }}"
    branch: "{{ gitlab_branch }}"
    state: absent
  register: delete_branch

- name: Test module is idempotent
  assert:
    that:
      - delete_branch is changed

- name: Clean up {{ gitlab_project_name }}
  gitlab_project:
    server_url: "{{ gitlab_host }}"
    validate_certs: false
    login_token: "{{ gitlab_login_token }}"
    name: "{{ gitlab_project_name }}"
    state: absent
