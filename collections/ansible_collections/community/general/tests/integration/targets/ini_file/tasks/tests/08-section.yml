---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

## testing section selection

- name: test-section 1 - Create starting ini file
  copy:
    content: |
      [drinks]
      fav = lemonade
      beverage = orange juice

      [drinks]
      fav = lemonade
      beverage = pineapple juice

    dest: "{{ output_file }}"

- name: test-section 1 - Modify starting ini file
  ini_file:
    dest: "{{ output_file }}"
    section: drinks
    option: car
    value: volvo
    state: present
  register: result1

- name: test-section 1 - Read modified file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-section 1 - Create expected result
  set_fact:
    expected1: |
      [drinks]
      fav = lemonade
      beverage = orange juice
      car = volvo

      [drinks]
      fav = lemonade
      beverage = pineapple juice
    output1: "{{ output_content.content | b64decode }}"

- name: test-section 1 - Option was added to first section
  assert:
    that:
      - result1 is changed
      - result1.msg == 'option added'
      - output1 == expected1

# ----------------

- name: test-section 2 - Create starting ini file
  copy:
    content: |
      [drinks]
      fav = lemonade
      beverage = orange juice

      [drinks]
      fav = lemonade
      beverage = pineapple juice

    dest: "{{ output_file }}"

- name: test-section 2 - Modify starting ini file
  ini_file:
    dest: "{{ output_file }}"
    section: drinks
    section_has_values:
      - option: beverage
        value: pineapple juice
    option: car
    value: volvo
    state: present
  register: result1

- name: test-section 2 - Read modified file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-section 2 - Create expected result
  set_fact:
    expected1: |
      [drinks]
      fav = lemonade
      beverage = orange juice

      [drinks]
      fav = lemonade
      beverage = pineapple juice
      car = volvo
    output1: "{{ output_content.content | b64decode }}"

- name: test-section 2 - Option added to second section specified with section_has_values
  assert:
    that:
      - result1 is changed
      - result1.msg == 'option added'
      - output1 == expected1

# ----------------

- name: test-section 3 - Create starting ini file
  copy:
    content: |
      [drinks]
      fav = lemonade
      beverage = orange juice

      [drinks]
      fav = lemonade
      beverage = pineapple juice

    dest: "{{ output_file }}"

- name: test-section 3 - Modify starting ini file
  ini_file:
    dest: "{{ output_file }}"
    section: drinks
    section_has_values:
      - option: beverage
        value: pineapple juice
    option: fav
    value: lemonade
    state: absent
  register: result1

- name: test-section 3 - Read modified file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-section 3 - Create expected result
  set_fact:
    expected1: |
      [drinks]
      fav = lemonade
      beverage = orange juice

      [drinks]
      beverage = pineapple juice
    output1: "{{ output_content.content | b64decode }}"

- name: test-section 3 - Option was removed from specified section
  assert:
    that:
      - result1 is changed
      - result1.msg == 'option changed'
      - output1 == expected1

# ----------------

- name: test-section 4 - Create starting ini file
  copy:
    content: |
      [drinks]
      fav = lemonade
      beverage = orange juice

      [drinks]
      fav = lemonade
      beverage = pineapple juice

    dest: "{{ output_file }}"

- name: test-section 4 - Modify starting ini file
  ini_file:
    dest: "{{ output_file }}"
    section: drinks
    section_has_values:
      - option: beverage
        value: alligator slime
    option: fav
    value: tea
    state: present
  register: result1

- name: test-section 4 - Read modified file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-section 4 - Create expected result
  set_fact:
    expected1: |
      [drinks]
      fav = lemonade
      beverage = orange juice

      [drinks]
      fav = lemonade
      beverage = pineapple juice
      [drinks]
      beverage = alligator slime
      fav = tea
    output1: "{{ output_content.content | b64decode }}"

- name: test-section 4 - New section created, including required values
  assert:
    that:
      - result1 is changed
      - result1.msg == 'section and option added'
      - output1 == expected1

# ----------------

- name: test-section 5 - Modify test-section 4 result file
  ini_file:
    dest: "{{ output_file }}"
    section: drinks
    section_has_values:
      - option: fav
        value: lemonade
      - option: beverage
        value: pineapple juice
    state: absent
  register: result1

- name: test-section 5 - Read modified file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-section 5 - Create expected result
  set_fact:
    expected1: |
      [drinks]
      fav = lemonade
      beverage = orange juice

      [drinks]
      beverage = alligator slime
      fav = tea
    output1: "{{ output_content.content | b64decode }}"

- name: test-section 5 - Section removed as specified
  assert:
    that:
      - result1 is changed
      - result1.msg == 'section removed'
      - output1 == expected1

# ----------------

- name: test-section 6 - Modify test-section 5 result file with multiple values
  ini_file:
    dest: "{{ output_file }}"
    section: drinks
    section_has_values:
      - option: fav
        values:
          - cherry
          - lemon
          - vanilla
      - option: beverage
        value: pineapple juice
    state: present
    option: fav
    values:
      - vanilla
      - grape
    exclusive: false
  register: result1

- name: test-section 6 - Read modified file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-section 6 - Create expected result
  set_fact:
    expected1: |
      [drinks]
      fav = lemonade
      beverage = orange juice

      [drinks]
      beverage = alligator slime
      fav = tea
      [drinks]
      beverage = pineapple juice
      fav = vanilla
      fav = grape
      fav = cherry
      fav = lemon
    output1: "{{ output_content.content | b64decode }}"

- name: test-section 6 - New section added
  assert:
    that:
      - result1 is changed
      - result1.msg == 'section and option added'
      - output1 == expected1

# ----------------

- name: test-section 7 - Modify test-section 6 result file with exclusive value
  ini_file:
    dest: "{{ output_file }}"
    section: drinks
    section_has_values:
      - option: fav
        value: vanilla
    state: present
    option: fav
    value: cherry
    exclusive: true
  register: result1

- name: test-section 7 - Read modified file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-section 7 - Create expected result
  set_fact:
    expected1: |
      [drinks]
      fav = lemonade
      beverage = orange juice

      [drinks]
      beverage = alligator slime
      fav = tea
      [drinks]
      beverage = pineapple juice
      fav = cherry
    output1: "{{ output_content.content | b64decode }}"

- name: test-section 7 - Option changed
  assert:
    that:
      - result1 is changed
      - result1.msg == 'option changed'
      - output1 == expected1
