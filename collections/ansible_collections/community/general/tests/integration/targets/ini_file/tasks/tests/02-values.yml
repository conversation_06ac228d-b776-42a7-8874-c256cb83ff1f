---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

## testing values

- name: "test-values 1 - set 'state=present' and 'values=[]' and 'allow_no_value=false' and fail"
  ini_file:
    path: "{{ output_file }}"
    section: cars
    option: audi
    values: []
    allow_no_value: false
  register: result1
  ignore_errors: true

- name: test-values 1 - verify error message
  assert:
    that:
      - result1 is not changed
      - result1 is failed
      - result1.msg == "Parameter 'value(s)' must be defined if state=present and allow_no_value=False."


- name: "test-values 2 - set 'state=present' and omit 'values' and 'allow_no_value=false' and fail"
  ini_file:
    path: "{{ output_file }}"
    section: cars
    option: audi
    allow_no_value: false
  register: result2
  ignore_errors: true

- name: test-values 2 - verify error message
  assert:
    that:
      - result2 is not changed
      - result2 is failed
      - result2.msg == "Parameter 'value(s)' must be defined if state=present and allow_no_value=False."


- name: "test-values 3 - ensure 'fav=lemonade' and 'fav=cocktail' is 'present' in section '[drinks]' in specified file"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    values:
      - lemonade
      - cocktail
    state: present
  register: result3

- name: test-values 3 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 3 - set expected content and get current ini file content
  set_fact:
    expected3: |

      [drinks]
      fav = lemonade
      fav = cocktail
    content3: "{{ output_content.content | b64decode }}"

- name: test-values 3 - Verify content of ini file is as expected and ini_file 'changed' is true
  assert:
    that:
      - result3 is changed
      - result3.msg == 'section and option added'
      - content3 == expected3


- name: "test-values 4 - remove option 'fav=lemonade' from section '[drinks]' in specified file"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    values:
      - lemonade
    state: absent
    exclusive: false
  register: result4

- name: test-values 4 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 4 - set expected content and get current ini file content
  set_fact:
    expected4: |

      [drinks]
      fav = cocktail
    content4: "{{ output_content.content | b64decode }}"

- name: test-values 4 - Verify content of ini file is as expected and ini_file 'changed' is true
  assert:
    that:
      - result4 is changed
      - result4.msg == 'option changed'
      - content4 == expected4


- name: "test-values 5 - add option 'fav=lemonade' in section '[drinks]' in specified file"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    values:
      - lemonade
    state: present
    exclusive: false
  register: result5

- name: test-values 5 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 5 - set expected content and get current ini file content
  set_fact:
    expected5: |

      [drinks]
      fav = cocktail
      fav = lemonade
    content5: "{{ output_content.content | b64decode }}"

- name: test-values 5 - Verify content of ini file is as expected and ini_file 'changed' is true
  assert:
    that:
      - result5 is changed
      - result5.msg == 'option added'
      - content5 == expected5


- name: "test-values 6 - ensure 'fav=lemonade' and 'fav=cocktail' is 'present' in section '[drinks]' and check for idempotency"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    values:
      - lemonade
      - cocktail
    state: present
  register: result6

- name: test-values 6 - Ensure unchanged
  assert:
    that:
      - result6 is not changed
      - result6.msg == 'OK'


- name: "test-values 7 - ensure 'fav=cocktail' and 'fav=lemonade' (list reverse order) is 'present' in section '[drinks]' and check for idempotency"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    values:
      - cocktail
      - lemonade
    state: present
  register: result7

- name: test-values 7 - Ensure unchanged
  assert:
    that:
      - result7 is not changed
      - result7.msg == 'OK'


- name: "test-values 8 - add option 'fav=lemonade' in section '[drinks]' again and ensure idempotency"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    values:
      - lemonade
    state: present
    exclusive: false
  register: result8

- name: test-values 8 - Ensure unchanged
  assert:
    that:
      - result8 is not changed
      - result8.msg == 'OK'


- name: "test-values 9 - ensure only 'fav=lemonade' is 'present' in section '[drinks]' in specified file"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    values:
      - lemonade
    state: present
  register: result9

- name: test-values 9 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 9 - set expected content and get current ini file content
  set_fact:
    expected9: |

      [drinks]
      fav = lemonade
    content9: "{{ output_content.content | b64decode }}"

- name: test-values 9 - Verify content of ini file is as expected and ini_file 'changed' is true
  assert:
    that:
      - result9 is changed
      - result9.msg == 'option changed'
      - content9 == expected9


- name: "test-values 10 - remove non-existent 'fav=cocktail' from section '[drinks]' in specified file"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    values:
      - cocktail
    state: absent
  register: result10

- name: test-values 10 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 10 - set expected content and get current ini file content
  set_fact:
    expected10: |

      [drinks]
    content10: "{{ output_content.content | b64decode }}"


- name: test-values 10 - Ensure unchanged
  assert:
    that:
      - result10 is changed
      - result10.msg == 'option changed'
      - content10 == expected10


- name: "test-values 11 - Ensure 'fav=lemonade' and 'beverage=coke' is 'present' in section '[drinks]'"
  block:
    - name: "test-values 11 - resetting ini_fie: Ensure 'fav=lemonade' is 'present' in section '[drinks]'"
      ini_file:
        path: "{{ output_file }}"
        section: drinks
        option: fav
        values:
          - lemonade
        state: present
    - name: "test-values 11 - Ensure 'beverage=coke' is 'present' in section '[drinks]'"
      ini_file:
        path: "{{ output_file }}"
        section: drinks
        option: beverage
        values:
          - coke
        state: present
      register: result11

- name: test-values 11 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 11 - set expected content and get current ini file content
  set_fact:
    expected11: |

      [drinks]
      fav = lemonade
      beverage = coke
    content11: "{{ output_content.content | b64decode }}"

- name: test-values 11 - assert 'changed' is true and content is OK
  assert:
    that:
      - result11 is changed
      - result11.msg == 'option added'
      - content11 == expected11


- name: "test-values 12 - add option 'fav=lemonade' in section '[drinks]' again and ensure idempotency"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    values:
      - lemonade
    state: present
    exclusive: false
  register: result12

- name: test-values 12 - Ensure unchanged
  assert:
    that:
      - result12 is not changed
      - result12.msg == 'OK'


- name: "test-values 13 - add option 'fav=cocktail' in section '[drinks]' in specified file"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    values:
      - cocktail
    state: present
    exclusive: false
  register: result13

- name: test-values 13 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 13 - set expected content and get current ini file content
  set_fact:
    expected13: |

      [drinks]
      fav = lemonade
      beverage = coke
      fav = cocktail
    content13: "{{ output_content.content | b64decode }}"

- name: test-values 13 - Verify content of ini file is as expected and ini_file 'changed' is true
  assert:
    that:
      - result13 is changed
      - result13.msg == 'option added'
      - content13 == expected13


- name: "test-values 14 - Ensure 'refreshment=[water, juice, soft drink]' is 'present' in section '[drinks]'"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: refreshment
    values:
      - water
      - juice
      - soft drink
    state: present
  register: result14

- name: test-values 14 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 14 - set expected content and get current ini file content
  set_fact:
    expected14: |

      [drinks]
      fav = lemonade
      beverage = coke
      fav = cocktail
      refreshment = water
      refreshment = juice
      refreshment = soft drink
    content14: "{{ output_content.content | b64decode }}"

- name: test-values 14 - assert 'changed' is true and content is OK
  assert:
    that:
      - result14 is changed
      - result14.msg == 'option added'
      - content14 == expected14


- name: "test-values 15 - ensure 'fav=lemonade' and 'fav=cocktail' is 'present' in section '[drinks]' and check for idempotency"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    values:
      - lemonade
      - cocktail
    state: present
  register: result15

- name: test-values 15 - Ensure unchanged
  assert:
    that:
      - result15 is not changed
      - result15.msg == 'OK'


- name: "test-values 16 - ensure 'fav=cocktail' and 'fav=lemonade' (list reverse order) is 'present' in section '[drinks]' and check for idempotency"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    values:
      - cocktail
      - lemonade
    state: present
  register: result16

- name: test-values 16 - Ensure unchanged
  assert:
    that:
      - result16 is not changed
      - result16.msg == 'OK'


- name: "test-values 17 - Ensure option 'refreshment' is 'absent' in section '[drinks]'"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: refreshment
    state: absent
  register: result17

- name: test-values 17 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 17 - set expected content and get current ini file content
  set_fact:
    expected17: |

      [drinks]
      fav = lemonade
      beverage = coke
      fav = cocktail
    content17: "{{ output_content.content | b64decode }}"

- name: test-values 17 - assert 'changed' is true and content is as expected
  assert:
    that:
      - result17 is changed
      - result17.msg == 'option changed'
      - content17 == expected17


- name: "test-values 18 - Ensure 'beverage=coke' is 'absent' in section '[drinks]'"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: beverage
    state: absent
  register: result18

- name: test-values 18 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 18 - set expected content and get current ini file content
  set_fact:
    expected18: |

      [drinks]
      fav = lemonade
      fav = cocktail
    content18: "{{ output_content.content | b64decode }}"

- name: test-values 18 - assert 'changed' is true and content is as expected
  assert:
    that:
      - result18 is changed
      - result18.msg == 'option changed'
      - content18 == expected18


- name: "test-values 19 - Ensure non-existent 'beverage=coke' is 'absent' in section '[drinks]'"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: beverage
    values:
      - coke
    state: absent
  register: result19

- name: test-values 19 - Ensure unchanged
  assert:
    that:
      - result19 is not changed
      - result19.msg == 'OK'


- name: test-values 20 - remove section 'drinks'
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    state: absent
  register: result20

- name: test-values 20 - remove section 'drinks' again to ensure idempotency"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    state: absent
  register: result20_remove_again

- name: test-values 20 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 20 - get current ini file content
  set_fact:
    content20: "{{ output_content.content | b64decode }}"

- name: test-values 20 - assert 'changed' is true and content is empty
  assert:
    that:
      - result20 is changed
      - result20_remove_again is not changed
      - result20.msg == 'section removed'
      - content20 == "\n"


- name: "test-values 21 - Ensure 'refreshment=[water, juice, soft drink, juice]' (duplicates removed) is 'present' in section '[drinks]'"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: refreshment
    values:
      - water
      - juice
      - soft drink
      - juice
    state: present
  register: result21

- name: test-values 21 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 21 - set expected content and get current ini file content
  set_fact:
    expected21: |

      [drinks]
      refreshment = water
      refreshment = juice
      refreshment = soft drink
    content21: "{{ output_content.content | b64decode }}"

- name: test-values 21 - assert 'changed' is true and content is OK
  assert:
    that:
      - result21 is changed
      - result21.msg == 'section and option added'
      - content21 == expected21


- name: test-values 22 - Create starting ini file
  copy:
    content: |

      # Some comment to test
      [mysqld]
      connect_timeout = 300
      max_connections = 1000
      [section1]
      var1 = aaa
      # comment in section
      # var2 = some value
      # comment after section

      [section2]
      var3 = ccc
      # comment after section
    dest: "{{ output_file }}"

- name: "test-values 22 - Ensure 'skip-name' with 'allow_no_value' is 'present' in section '[mysqld]' test allow_no_value"
  ini_file:
    path: "{{ output_file }}"
    section: mysqld
    option: skip-name
    allow_no_value: true
    state: present
  register: result22

- name: test-values 22 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 22 - set expected content and get current ini file content
  set_fact:
    expected22: |

      # Some comment to test
      [mysqld]
      connect_timeout = 300
      max_connections = 1000
      skip-name
      [section1]
      var1 = aaa
      # comment in section
      # var2 = some value
      # comment after section

      [section2]
      var3 = ccc
      # comment after section
    content22: "{{ output_content.content | b64decode }}"

- name: test-values 22 - assert 'changed' is true and content is OK and option added
  assert:
    that:
      - result22 is changed
      - result22.msg == 'option added'
      - content22 == expected22


- name: "test-values 23 - Ensure 'var2=foo' is 'present' in section '[section1]', replacing commented option 'var2=some value'"
  ini_file:
    path: "{{ output_file }}"
    section: section1
    option: var2
    values:
      - foo
    state: present
  register: result23

- name: test-values 23 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 23 - set expected content and get current ini file content
  set_fact:
    expected23: |

      # Some comment to test
      [mysqld]
      connect_timeout = 300
      max_connections = 1000
      skip-name
      [section1]
      var1 = aaa
      # comment in section
      var2 = foo
      # comment after section

      [section2]
      var3 = ccc
      # comment after section
    content23: "{{ output_content.content | b64decode }}"

- name: test-values 23 - assert 'changed' and msg 'option changed' and content is as expected
  assert:
    that:
      - result23 is changed
      - result23.msg == 'option changed'
      - content23 == expected23


- name: "test-values 24 - Ensure 'var2=[foo, foobar]' is 'present' in section '[section1]'"
  ini_file:
    path: "{{ output_file }}"
    section: section1
    option: var2
    values:
      - foo
      - foobar
    state: present
  register: result24

- name: test-values 24 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 24 - set expected content and get current ini file content
  set_fact:
    expected24: |

      # Some comment to test
      [mysqld]
      connect_timeout = 300
      max_connections = 1000
      skip-name
      [section1]
      var1 = aaa
      # comment in section
      var2 = foo
      var2 = foobar
      # comment after section

      [section2]
      var3 = ccc
      # comment after section
    content24: "{{ output_content.content | b64decode }}"

- name: test-values 24 - assert 'changed' and msg 'option added' and content is as expected
  assert:
    that:
      - result24 is changed
      - result24.msg == 'option added'
      - content24 == expected24


- name: test-values 25 - Clean test file
  copy:
    content: ""
    dest: "{{ output_file }}"
    force: true

- name: "test-values 25 - Ensure 'beverage=[coke, pepsi]' is created within no section"
  ini_file:
    section:
    path: "{{ output_file }}"
    option: beverage
    values:
      - coke
      - pepsi
    state: present
  register: result25

- name: test-values 25 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 25 - set expected content and get current ini file content
  set_fact:
    expected25: |+
      beverage = coke
      beverage = pepsi

    content25: "{{ output_content.content | b64decode }}"

- name: test-values 25 - assert 'changed' is true and content is OK (no section)
  assert:
    that:
      - result25 is changed
      - result25.msg == 'option added'
      - content25 == expected25


- name: "test-values 26 - Ensure 'beverage=coke' and 'beverage=pepsi' are modified within no section"
  ini_file:
    path: "{{ output_file }}"
    option: beverage
    values:
      - water
      - orange juice
    section:
    state: present
    exclusive: true
  register: result26

- name: test-values 26 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 26 - set expected content and get current ini file content
  set_fact:
    expected26: |+
      beverage = water
      beverage = orange juice

    content26: "{{ output_content.content | b64decode }}"

- name: test-values 26 - assert 'changed' is true and content is OK (no section)
  assert:
    that:
      - result26 is changed
      - result26.msg == 'option changed'
      - content26 == expected26


- name: "test-values 27 - ensure option 'beverage' is 'absent' within no section"
  ini_file:
    section:
    path: "{{ output_file }}"
    option: beverage
    state: absent
  register: result27

- name: test-values 27 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 27 - get current ini file content
  set_fact:
    content27: "{{ output_content.content | b64decode }}"

- name: test-values 27 - assert changed (no section)
  assert:
    that:
      - result27 is changed
      - result27.msg == 'option changed'
      - content27 == "\n"


- name: "test-values 28 - Ensure option 'present' without section before existing section"
  block:
    - name: test-values 28 - ensure option present within section
      ini_file:
        path: "{{ output_file }}"
        section: drinks
        option: beverage
        values:
          - water
          - orange juice
        state: present

    - name: test-values 28 - ensure option present without section
      ini_file:
        path: "{{ output_file }}"
        section:
        option: like
        values:
          - tea
          - coffee
        state: present

- name: test-values 28 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-values 28 - set expected content and get current ini file content
  set_fact:
    expected28: |
      like = tea
      like = coffee

      [drinks]
      beverage = water
      beverage = orange juice
    content28: "{{ output_content.content | b64decode }}"

- name: test-values 28 - Verify content of ini file is as expected
  assert:
    that:
      - content28 == expected28


- name: test-value 29 - Create starting ini file
  copy:
    content: |
      [drinks]
      fav = cocktail
      beverage = water
      fav = lemonade
      beverage = orange juice
    dest: "{{ output_file }}"

- name: "test-value 29 - Test 'state=absent' with 'exclusive=true' with multiple options in ini_file"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    values:
      - cocktail
    state: absent
  register: result29

- name: test-value 29 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-value 29 - set expected content and get current ini file content
  set_fact:
    expected29: |
      [drinks]
      beverage = water
      beverage = orange juice
    content29: "{{ output_content.content | b64decode }}"

- name: test-value 29 - Verify content of ini file is as expected
  assert:
    that:
      - result29 is changed
      - result29.msg == 'option changed'
      - content29 == expected29


- name: test-value 30 - Create starting ini file
  copy:
    content: |
      [drinks]
      fav = cocktail
      beverage = water
      fav = lemonade
      beverage = orange juice
    dest: "{{ output_file }}"

- name: "test-value 30 - Test 'state=absent' with 'exclusive=false' with multiple options in ini_file"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    values:
      - cocktail
    state: absent
    exclusive: false
  register: result30

- name: test-value 30 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-value 30 - set expected content and get current ini file content
  set_fact:
    expected30: |
      [drinks]
      beverage = water
      fav = lemonade
      beverage = orange juice
    content30: "{{ output_content.content | b64decode }}"

- name: test-value 30 - Verify content of ini file is as expected
  assert:
    that:
      - result30 is changed
      - result30.msg == 'option changed'
      - content30 == expected30


- name: test-value 31 - Create starting ini file
  copy:
    content: |
      [drinks]
      fav = cocktail
      beverage = water
      fav = lemonade
      beverage = orange juice
    dest: "{{ output_file }}"

- name: "test-value 31 - Test 'state=absent' with 'exclusive=true' and no value given with multiple options in ini_file"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    state: absent
  register: result31

- name: test-value 31 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-value 31 - set expected content and get current ini file content
  set_fact:
    expected31: |
      [drinks]
      beverage = water
      beverage = orange juice
    content31: "{{ output_content.content | b64decode }}"

- name: test-value 31 - Verify content of ini file is as expected
  assert:
    that:
      - result31 is changed
      - result31.msg == 'option changed'
      - content31 == expected31


- name: test-value 32 - Create starting ini file
  copy:
    content: |
      [drinks]
      fav = cocktail
      beverage = water
      fav = lemonade
      beverage = orange juice
    dest: "{{ output_file }}"

- name: "test-value 32 - Test 'state=absent' with 'exclusive=false' and no value given with multiple options in ini_file"
  ini_file:
    path: "{{ output_file }}"
    section: drinks
    option: fav
    state: absent
    exclusive: false
  register: result32
  diff: true

- name: test-value 32 - read content from output file
  slurp:
    src: "{{ output_file }}"
  register: output_content

- name: test-value 32 - set expected content and get current ini file content
  set_fact:
    expected32: |
      [drinks]
      fav = cocktail
      beverage = water
      fav = lemonade
      beverage = orange juice
    content32: "{{ output_content.content | b64decode }}"

- name: test-value 32 - Verify content of ini file is as expected
  assert:
    that:
      - result32 is not changed
      - result32.msg == 'OK'
      - content32 == expected32
