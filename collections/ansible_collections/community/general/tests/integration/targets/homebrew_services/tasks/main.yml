---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Don't run this test for non-MacOS systems.
- meta: end_play
  when: ansible_facts.distribution != 'MacOSX'

- name: MACOS | Find brew binary
  command: which brew
  register: brew_which

- name: MACOS | Get owner of brew binary
  stat:
    path: "{{ brew_which.stdout }}"
  register: brew_stat

- name: Homebrew Services test block
  become: true
  become_user: "{{ brew_stat.stat.pw_name }}"
  block:
    - name: MACOS | Install black
      community.general.homebrew:
        name: black
        state: present
      register: install_result
      notify:
        - uninstall black

    - name: Check the black service is installed
      assert:
        that:
          - install_result is success

    - name: Start the black service
      community.general.homebrew_services:
        name: black
        state: present
      register: start_result
      environment:
        HOMEBREW_NO_ENV_HINTS: "1"

    - name: Check the black service is running
      assert:
        that:
          - start_result is success

    - name: Start the black service when already started
      community.general.homebrew_services:
        name: black
        state: present
      register: start_result
      environment:
        HOMEBREW_NO_ENV_HINTS: "1"

    - name: Check for idempotency
      assert:
        that:
          - start_result.changed == 0

    - name: Restart the black service
      community.general.homebrew_services:
        name: black
        state: restarted
      register: restart_result
      environment:
        HOMEBREW_NO_ENV_HINTS: "1"

    - name: Check the black service is restarted
      assert:
        that:
          - restart_result is success

    - name: Stop the black service
      community.general.homebrew_services:
        name: black
        state: present
      register: stop_result
      environment:
        HOMEBREW_NO_ENV_HINTS: "1"

    - name: Check the black service is stopped
      assert:
        that:
          - stop_result is success
