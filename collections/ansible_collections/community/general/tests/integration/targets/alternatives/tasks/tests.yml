---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- block:
    - include_tasks: remove_links.yml
    - include_tasks: setup_test.yml
    # at least two iterations:
    # - first will use 'link currently absent',
    # - second will receive 'link currently points to'
    - include_tasks: test.yml
      with_sequence: start=1 end=2
  vars:
    with_link: '{{ test_conf[0] }}'
    with_alternatives: '{{ test_conf[1] }}'
    mode: '{{ test_conf[2] }}'
  # update-alternatives included in Fedora 26 (1.10) & Red Hat 7.4 (1.8) doesn't provide
  # '--query' switch, 'link' is mandatory for these distributions.
  when: ansible_os_family != 'RedHat' or test_conf[0]
