---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- debug:
    msg: ' with_alternatives: {{ with_alternatives }}, mode: {{ mode }}'

- block:
    - name: set alternative (using link parameter)
      alternatives:
        name: dummy
        path: '/usr/bin/dummy{{ item }}'
        link: '/usr/bin/dummy'
      register: alternative

    - name: check expected command was executed
      assert:
        that:
          - 'alternative is successful'
          - 'alternative is changed'
  when: with_link

- block:
    - name: set alternative (without link parameter)
      alternatives:
        name: dummy
        path: '/usr/bin/dummy{{ item }}'
      register: alternative

    - name: check expected command was executed
      assert:
        that:
          - 'alternative is successful'
          - 'alternative is changed'
  when: not with_link

- name: execute dummy command
  shell: dummy
  register: cmd

- name: check expected command was executed
  assert:
    that:
      - 'cmd.stdout == "dummy" ~ item'

- name: 'check mode (manual: alternatives file existed, it has been updated)'
  shell: 'head -n1 {{ alternatives_dir }}/dummy | grep "^manual$"'
  when: ansible_os_family != 'RedHat' or with_alternatives or item != 1

- name: 'check mode (auto: alternatives file didn''t exist, it has been created)'
  shell: 'head -n1 {{ alternatives_dir }}/dummy | grep "^auto$"'
  when: ansible_os_family == 'RedHat' and not with_alternatives and item == 1

- name: check that alternative has been updated
  command: "grep -Pzq '/bin/dummy{{ item }}\\n' '{{ alternatives_dir }}/dummy'"
