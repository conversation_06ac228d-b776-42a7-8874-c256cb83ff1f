####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) 2017 <PERSON><PERSON><PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: 'setup: create a dummy alternative'
  block:
    - import_tasks: setup.yml

    ##############
    # Test parameters:
    # link parameter present / absent ('with_link' variable)
    # with / without alternatives defined in alternatives file ('with_alternatives' variable)
    # auto / manual ('mode' variable)

    - include_tasks: tests.yml
      with_nested:
        - [true, false] # with_link
        - [true, false] # with_alternatives
        - ['auto', 'manual'] # mode
      loop_control:
        loop_var: test_conf

    ##########
    # Priority
    - block:
        - include_tasks: remove_links.yml
        - include_tasks: setup_test.yml
        # at least two iterations again
        - include_tasks: tests_set_priority.yml
          with_sequence: start=3 end=4
      vars:
        with_alternatives: true
        mode: auto

    - block:
        - include_tasks: remove_links.yml
        - include_tasks: setup_test.yml
        # at least two iterations again
        - include_tasks: tests_set_priority.yml
          with_sequence: start=3 end=4
      vars:
        with_alternatives: false
        mode: auto

    # Test that path is checked: alternatives must fail when path is nonexistent
    - import_tasks: path_is_checked.yml

    # Test that subcommands commands work
    - import_tasks: subcommands.yml

    # Test operation of the 'state' parameter
    - block:
        - include_tasks: remove_links.yml
        - include_tasks: tests_state.yml

    # Test for the family parameter
    - block:
        - include_tasks: remove_links.yml
        - include_tasks: tests_family.yml
      when: ansible_os_family == 'RedHat'

  # Cleanup
  always:
    - include_tasks: remove_links.yml

    - file:
        path: '{{ item }}'
        state: absent
      with_items:
        - '{{ alternatives_dir }}/dummy'
        - '{{ alternatives_dir }}/dummymain'
        - '{{ alternatives_dir }}/dummysubcmd'

    - file:
        path: '/usr/bin/dummy{{ item }}'
        state: absent
      with_sequence: start=1 end=4

  # *Disable tests on Fedora 24*
  # Shippable Fedora 24 image provides chkconfig-1.7-2.fc24.x86_64 but not the
  # latest available version (chkconfig-1.8-1.fc24.x86_64). update-alternatives
  # in chkconfig-1.7-2 fails when /etc/alternatives/dummy link is missing,
  # error is: 'failed to read link /usr/bin/dummy: No such file or directory'.
  # Moreover Fedora 24 is no longer maintained.
  #
  # *Disable tests on Arch Linux*
  # TODO: figure out whether there is an alternatives tool for Arch Linux
  #
  # *Disable tests on Alpine*
  # TODO: figure out whether there is an alternatives tool for Alpine
  when:
    - ansible_distribution != 'Fedora' or ansible_distribution_major_version|int > 24
    - ansible_distribution != 'Archlinux'
    - ansible_distribution != 'Alpine'
