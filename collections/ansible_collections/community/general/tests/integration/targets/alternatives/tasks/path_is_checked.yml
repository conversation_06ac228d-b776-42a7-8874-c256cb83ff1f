---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Try with nonexistent path
  alternatives:
    name: dummy
    path: '/non/existent/path/there'
    link: '/usr/bin/dummy'
  ignore_errors: true
  register: alternative

- name: Check previous task failed
  assert:
    that:
      - 'alternative is failed'
