---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: update dummy alternative
  alternatives:
    name: dummy
    path: '/usr/bin/dummy{{ item }}'
    link: /usr/bin/dummy
    priority: '{{ 60 + item|int }}'
  register: alternative

- name: execute dummy command
  shell: dummy
  register: cmd

- name: check if link group is in manual mode
  shell: 'head -n1 {{ alternatives_dir }}/dummy | grep "^manual$"'

- name: check expected command was executed
  assert:
    that:
      - 'alternative is changed'
      - 'cmd.stdout == "dummy" ~ item'

- name: check that alternative has been updated
  command: "grep -Pzq '/bin/dummy{{ item }}\\n{{ 60 + item|int }}' '{{ alternatives_dir }}/dummy'"

- name: update dummy priority
  alternatives:
    name: dummy
    path: '/usr/bin/dummy{{ item }}'
    link: /usr/bin/dummy
    priority: '{{ 70 + item|int }}'
  register: alternative

- name: check that alternative priority has been updated
  command: "grep -Pzq '/bin/dummy{{ item }}\\n{{ 70 + item|int }}' '{{ alternatives_dir }}/dummy'"

- name: no change without priority
  alternatives:
    name: dummy
    path: '/usr/bin/dummy{{ item }}'
    link: /usr/bin/dummy
  register: alternative

- name: check no change was triggered without priority
  assert:
    that:
      - 'alternative is not changed'

- name: check that alternative priority has not been changed
  command: "grep -Pzq '/bin/dummy{{ item }}\\n{{ 70 + item|int }}' '{{ alternatives_dir }}/dummy'"
