---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- template:
    src: dummy_alternative
    dest: '{{ alternatives_dir }}/dummy'
    owner: root
    group: root
    mode: '0644'
  when: with_alternatives or ansible_os_family != 'RedHat'
- file:
    path: '{{ alternatives_dir }}/dummy'
    state: absent
  when: not with_alternatives and ansible_os_family == 'RedHat'
