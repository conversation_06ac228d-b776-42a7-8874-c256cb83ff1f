---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Pre-test setup
- name: create a vpc
  hwc_network_vpc:
    cidr: "192.168.0.0/16"
    name: "ansible_network_vpc_test_local"
    state: present
  register: vpc1
- name: create a vpc
  hwc_network_vpc:
    cidr: "192.168.0.0/16"
    name: "ansible_network_vpc_test_peering"
    state: present
  register: vpc2
- name: delete a peering connect
  hwc_vpc_peering_connect:
    local_vpc_id: "{{ vpc1.id }}"
    name: "ansible_network_peering_test"
    peering_vpc:
      vpc_id: "{{ vpc2.id }}"
    state: absent
# ----------------------------------------------------------
- name: create a peering connect (check mode)
  hwc_vpc_peering_connect:
    local_vpc_id: "{{ vpc1.id }}"
    name: "ansible_network_peering_test"
    peering_vpc:
      vpc_id: "{{ vpc2.id }}"
    state: present
  check_mode: true
  register: result
- name: assert changed is true
  assert:
    that:
      - not result.id
      - result.changed
# ----------------------------------------------------------
- name: create a peering connect
  hwc_vpc_peering_connect:
    local_vpc_id: "{{ vpc1.id }}"
    name: "ansible_network_peering_test"
    peering_vpc:
      vpc_id: "{{ vpc2.id }}"
    state: present
  register: result
- name: assert changed is true
  assert:
    that:
      result is changed
# ----------------------------------------------------------
- name: create a peering connect (idemponent)
  hwc_vpc_peering_connect:
    local_vpc_id: "{{ vpc1.id }}"
    name: "ansible_network_peering_test"
    peering_vpc:
      vpc_id: "{{ vpc2.id }}"
    state: present
  check_mode: true
  register: result
- name: idemponent
  assert:
    that:
      - not result.changed
# ----------------------------------------------------------------------------
- name: create a peering connect that already exists
  hwc_vpc_peering_connect:
    local_vpc_id: "{{ vpc1.id }}"
    name: "ansible_network_peering_test"
    peering_vpc:
      vpc_id: "{{ vpc2.id }}"
    state: present
  register: result
- name: assert changed is false
  assert:
    that:
      - result is not failed
      - result is not changed
# ----------------------------------------------------------
- name: delete a peering connect (check mode)
  hwc_vpc_peering_connect:
    local_vpc_id: "{{ vpc1.id }}"
    name: "ansible_network_peering_test"
    peering_vpc:
      vpc_id: "{{ vpc2.id }}"
    state: absent
  check_mode: true
  register: result
- name: assert changed is true
  assert:
    that:
      result is changed
# ----------------------------------------------------------
- name: delete a peering connect
  hwc_vpc_peering_connect:
    local_vpc_id: "{{ vpc1.id }}"
    name: "ansible_network_peering_test"
    peering_vpc:
      vpc_id: "{{ vpc2.id }}"
    state: absent
  register: result
- name: assert changed is true
  assert:
    that:
      result is changed
# ----------------------------------------------------------
- name: delete a peering connect (idemponent)
  hwc_vpc_peering_connect:
    local_vpc_id: "{{ vpc1.id }}"
    name: "ansible_network_peering_test"
    peering_vpc:
      vpc_id: "{{ vpc2.id }}"
    state: absent
  check_mode: true
  register: result
- name: assert changed is false
  assert:
    that:
      - not result.changed
# ----------------------------------------------------------------------------
- name: delete a peering connect that does not exist
  hwc_vpc_peering_connect:
    local_vpc_id: "{{ vpc1.id }}"
    name: "ansible_network_peering_test"
    peering_vpc:
      vpc_id: "{{ vpc2.id }}"
    state: absent
  register: result
- name: assert changed is false
  assert:
    that:
      - result is not failed
      - result is not changed
# ---------------------------------------------------------
# Post-test teardown
- name: delete a vpc
  hwc_network_vpc:
    cidr: "192.168.0.0/16"
    name: "ansible_network_vpc_test_peering"
    state: absent
  register: vpc2
- name: delete a vpc
  hwc_network_vpc:
    cidr: "192.168.0.0/16"
    name: "ansible_network_vpc_test_local"
    state: absent
  register: vpc1
