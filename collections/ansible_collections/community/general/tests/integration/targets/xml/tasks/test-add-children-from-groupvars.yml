---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Setup test fixture
  copy:
    src: fixtures/ansible-xml-beers.xml
    dest: /tmp/ansible-xml-beers.xml


- name: Add child element
  xml:
    path: /tmp/ansible-xml-beers.xml
    xpath: /business/beers
    add_children: '{{ bad_beers }}'
  register: add_children_from_groupvars

- name: Add trailing newline
  shell: echo "" >> /tmp/ansible-xml-beers.xml

- name: Compare to expected result
  copy:
    src: results/test-add-children-from-groupvars.xml
    dest: /tmp/ansible-xml-beers.xml
  check_mode: true
  diff: true
  register: comparison

- name: Test expected result
  assert:
    that:
      - add_children_from_groupvars is changed
      - comparison is not changed  # identical
  # command: diff -u {{ role_path }}/results/test-add-children-from-groupvars.xml /tmp/ansible-xml-beers.xml
