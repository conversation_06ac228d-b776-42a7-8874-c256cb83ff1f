---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Setup test fixture
  copy:
    src: fixtures/ansible-xml-beers.xml
    dest: /tmp/ansible-xml-beers.xml


- name: Add child element
  xml:
    path: /tmp/ansible-xml-beers.xml
    xpath: /business/beers
    add_children:
      - beer:
          name: Ansible Brew
          type: light
  register: add_children_with_attributes

- name: Add trailing newline
  shell: echo "" >> /tmp/ansible-xml-beers.xml

- name: Compare to expected result
  copy:
    src: results/test-add-children-with-attributes.xml
    dest: /tmp/ansible-xml-beers.xml
  check_mode: true
  diff: true
  register: comparison

# NOTE: This test may fail if lxml does not support predictable element attribute order
#       So we filter the failure out for these platforms (e.g. CentOS 6)
#       The module still works fine, we simply are not comparing as smart as we should.
- name: Test expected result
  assert:
    that:
      - add_children_with_attributes is changed
      - comparison is not changed  # identical
  when: lxml_predictable_attribute_order
  # command: diff -u {{ role_path }}/results/test-add-children-with-attributes.xml /tmp/ansible-xml-beers.xml
