---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Setup test fixture
  copy:
    src: fixtures/ansible-xml-beers.xml
    dest: /tmp/ansible-xml-beers.xml


- name: Add child element
  xml:
    path: /tmp/ansible-xml-beers.xml
    xpath: '/business/beers/beer[text()="St. Bernardus Abbot 12"]'
    insertbefore: true
    add_children:
      - beer: Old Rasputin
      - beer: Old Motor Oil
      - beer: Old Curmudgeon
    pretty_print: true
  register: add_children_insertbefore

- name: Compare to expected result
  copy:
    src: results/test-add-children-insertbefore.xml
    dest: /tmp/ansible-xml-beers.xml
  check_mode: true
  diff: true
  register: comparison

- name: Test expected result
  assert:
    that:
      - add_children_insertbefore is changed
      - comparison is not changed  # identical
