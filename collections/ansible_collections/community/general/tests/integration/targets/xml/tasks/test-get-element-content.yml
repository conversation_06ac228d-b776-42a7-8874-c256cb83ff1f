---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Setup test fixture
  copy:
    src: fixtures/ansible-xml-beers.xml
    dest: /tmp/ansible-xml-beers.xml


- name: Get element attributes
  xml:
    path: /tmp/ansible-xml-beers.xml
    xpath: /business/rating
    content: attribute
  register: get_element_attribute

- name: Test expected result
  assert:
    that:
      - get_element_attribute is not changed
      - get_element_attribute.matches[0]['rating'] is defined
      - get_element_attribute.matches[0]['rating']['subjective'] == 'true'

- name: Get element attributes (should fail)
  xml:
    path: /tmp/ansible-xml-beers.xml
    xpath: /business/rating
    content: attribute
    attribute: subjective
  register: get_element_attribute_wrong
  ignore_errors: true

- name: Test expected result
  assert:
    that:
      - get_element_attribute_wrong is failed

- name: Get element text
  xml:
    path: /tmp/ansible-xml-beers.xml
    xpath: /business/rating
    content: text
  register: get_element_text

- name: Test expected result
  assert:
    that:
      - get_element_text is not changed
      - get_element_text.matches[0]['rating'] == '10'
