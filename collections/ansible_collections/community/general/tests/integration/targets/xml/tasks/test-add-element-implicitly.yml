---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Setup test fixture
  copy:
    src: fixtures/ansible-xml-beers.xml
    dest: /tmp/ansible-xml-beers-implicit.xml


- name: Add a phonenumber element to the business element. Implicit mkdir -p behavior where applicable
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/phonenumber
    value: ************

- name: Add a owner element to the business element, testing implicit mkdir -p behavior 1/2
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/owner/name/last
    value: Smith

- name: Add a owner element to the business element, testing implicit mkdir -p behavior 2/2
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/owner/name/first
    value: John

- name: Add a validxhtml element to the website element. Note that ensure is present by default and while value defaults to null for elements, if one doesn't specify it we don't know what to do.
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/website/validxhtml

- name: Add an empty validateon attribute to the validxhtml element. This actually makes the previous example redundant because of the implicit parent-node creation behavior.
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/website/validxhtml/@validateon

- name: Add an empty validateon attribute to the validxhtml element. Actually verifies the implicit parent-node creation behavior.
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/website_bis/validxhtml/@validateon

- name: Add an attribute with a value
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/owner/@dob='1976-04-12'

- name: Add an element with a value, alternate syntax
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/beers/beer/text()="George Killian's Irish Red"  # note the quote within an XPath string thing

- name: Add an element without special characters
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/testnormalelement
    value: xml tag with no special characters
    pretty_print: true

- name: Add an element with dash
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/test-with-dash
    value: xml tag with dashes
    pretty_print: true

- name: Add an element with dot
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/test-with-dash.and.dot
    value: xml tag with dashes and dots
    pretty_print: true

- name: Add an element with underscore
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/test-with.dash_and.dot_and-underscores
    value: xml tag with dashes, dots and underscores
    pretty_print: true

- name: Add an attribute on a conditional element
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/beers/beer[text()="George Killian's Irish Red"]/@color='red'

- name: Add two attributes on a conditional element
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/beers/beer[text()="Pilsner Urquell" and @origin='CZ']/@color='blonde'

- name: Add a owner element to the business element, testing implicit mkdir -p behavior 3/2 -- complex lookup
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/owner/name[first/text()='John']/middle
    value: Q

- name: Pretty Print this!
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    pretty_print: true

- name: Compare to expected result
  copy:
    src: results/test-add-element-implicitly.xml
    dest: /tmp/ansible-xml-beers-implicit.xml
  check_mode: true
  diff: true
  register: comparison

- name: Test expected result
  assert:
    that:
      - comparison is not changed  # identical
  # command: diff -u {{ role_path }}/results/test-add-element-implicitly.xml /tmp/ansible-xml-beers-implicit.xml


# Now we repeat the same, just to ensure proper use of namespaces
- name: Add a phonenumber element to the business element. Implicit mkdir -p behavior where applicable
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/a:phonenumber
    value: ************
    namespaces:
      a: http://example.com/some/namespace

- name: Add a owner element to the business element, testing implicit mkdir -p behavior 1/2
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/a:owner/a:name/a:last
    value: Smith
    namespaces:
      a: http://example.com/some/namespace

- name: Add a owner element to the business element, testing implicit mkdir -p behavior 2/2
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/a:owner/a:name/a:first
    value: John
    namespaces:
      a: http://example.com/some/namespace

- name: Add a validxhtml element to the website element. Note that ensure is present by default and while value defaults to null for elements, if one doesn't specify it we don't know what to do.
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/a:website/a:validxhtml
    namespaces:
      a: http://example.com/some/namespace

- name: Add an empty validateon attribute to the validxhtml element. This actually makes the previous example redundant because of the implicit parent-node creation behavior.
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/a:website/a:validxhtml/@a:validateon
    namespaces:
      a: http://example.com/some/namespace

- name: Add an empty validateon attribute to the validxhtml element. Actually verifies the implicit parent-node creation behavior.
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/a:website_bis/a:validxhtml/@a:validateon
    namespaces:
      a: http://example.com/some/namespace

- name: Add an attribute with a value
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/a:owner/@a:dob='1976-04-12'
    namespaces:
      a: http://example.com/some/namespace

- name: Add an element with a value, alternate syntax
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/a:beers/a:beer/text()="George Killian's Irish Red"  # note the quote within an XPath string thing
    namespaces:
      a: http://example.com/some/namespace

- name: Add an attribute on a conditional element
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/a:beers/a:beer[text()="George Killian's Irish Red"]/@a:color='red'
    namespaces:
      a: http://example.com/some/namespace

- name: Add two attributes on a conditional element
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/a:beers/a:beer[text()="Pilsner Urquell" and @a:origin='CZ']/@a:color='blonde'
    namespaces:
      a: http://example.com/some/namespace

- name: Add a owner element to the business element, testing implicit mkdir -p behavior 3/2 -- complex lookup
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/a:owner/a:name[a:first/text()='John']/a:middle
    value: Q
    namespaces:
      a: http://example.com/some/namespace

- name: Add an element without special characters
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/testnormalelement
    value: xml tag with no special characters
    pretty_print: true
    namespaces:
      a: http://example.com/some/namespace


- name: Add an element with dash
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/test-with-dash
    value: xml tag with dashes
    pretty_print: true
    namespaces:
      a: http://example.com/some/namespace

- name: Add an element with dot
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/test-with-dash.and.dot
    value: xml tag with dashes and dots
    pretty_print: true
    namespaces:
      a: http://example.com/some/namespace

- name: Add an element with underscore
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    xpath: /business/test-with.dash_and.dot_and-underscores
    value: xml tag with dashes, dots and underscores
    pretty_print: true
    namespaces:
      a: http://example.com/some/namespace

- name: Pretty Print this!
  xml:
    file: /tmp/ansible-xml-beers-implicit.xml
    pretty_print: true
