---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Setup test fixture
  copy:
    src: fixtures/ansible-xml-beers-unicode.xml
    dest: /tmp/ansible-xml-beers-unicode.xml


- name: Get element attributes
  xml:
    path: /tmp/ansible-xml-beers-unicode.xml
    xpath: /business/rating
    content: attribute
  register: get_element_attribute

- name: Test expected result
  assert:
    that:
      - get_element_attribute is not changed
      - get_element_attribute.matches[0]['rating'] is defined and get_element_attribute.matches[0]['rating']['subjective'] == 'да'

- name: Get element text
  xml:
    path: /tmp/ansible-xml-beers-unicode.xml
    xpath: /business/rating
    content: text
  register: get_element_text

- name: Test expected result
  assert:
    that:
      - get_element_text is not changed
      - get_element_text.matches[0]['rating'] == 'десять'
