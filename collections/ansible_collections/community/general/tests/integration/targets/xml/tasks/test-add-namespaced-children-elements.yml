---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Setup test fixture
  copy:
    src: fixtures/ansible-xml-namespaced-beers.xml
    dest: /tmp/ansible-xml-namespaced-beers.xml


- name: Add namespaced child element
  xml:
    path: /tmp/ansible-xml-namespaced-beers.xml
    xpath: /bus:business/ber:beers
    namespaces:
      bus: http://test.business
      ber: http://test.beers
    add_children:
      - beer: Old Rasputin
  register: add_namespaced_children_elements

- name: Add trailing newline
  shell: echo "" >> /tmp/ansible-xml-namespaced-beers.xml

- name: Compare to expected result
  copy:
    src: results/test-add-namespaced-children-elements.xml
    dest: /tmp/ansible-xml-namespaced-beers.xml
  check_mode: true
  diff: true
  register: comparison

- name: Test expected result
  assert:
    that:
      - add_namespaced_children_elements is changed
      - comparison is not changed  # identical
  # command: diff -u {{ role_path }}/results/test-add-namespaced-children-elements.xml /tmp/ansible-xml-namespaced-beers.xml
