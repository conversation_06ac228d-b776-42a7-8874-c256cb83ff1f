---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Setup test fixture
  copy:
    src: fixtures/ansible-xml-namespaced-beers.xml
    dest: /tmp/ansible-xml-namespaced-beers.xml


- name: Set namespaced '/bus:business/rat:rating' to '11'
  xml:
    path: /tmp/ansible-xml-namespaced-beers.xml
    namespaces:
      bus: http://test.business
      ber: http://test.beers
      rat: http://test.rating
      attr: http://test.attribute
    xpath: /bus:business/rat:rating
    value: '11'
  register: set_element_first_run

- name: Set namespaced '/bus:business/rat:rating' to '11' again
  xml:
    path: /tmp/ansible-xml-namespaced-beers.xml
    namespaces:
      bus: http://test.business
      ber: http://test.beers
      rat: http://test.rating
      attr: http://test.attribute
    xpath: /bus:business/rat:rating
    value: '11'
  register: set_element_second_run

- name: Add trailing newline
  shell: echo "" >> /tmp/ansible-xml-namespaced-beers.xml

- name: Compare to expected result
  copy:
    src: results/test-set-namespaced-element-value.xml
    dest: /tmp/ansible-xml-namespaced-beers.xml
  check_mode: true
  diff: true
  register: comparison
  # command: diff -u {{ role_path }}/results/test-set-namespaced-element-value.xml /tmp/ansible-xml-namespaced-beers.xml

- name: Test expected result
  assert:
    that:
      - set_element_first_run is changed
      - set_element_second_run is not changed
      - comparison is not changed  # identical
