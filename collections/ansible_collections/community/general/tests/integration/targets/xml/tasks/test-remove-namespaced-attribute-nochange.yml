---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Setup test fixture
  copy:
    src: results/test-remove-namespaced-attribute.xml
    dest: /tmp/ansible-xml-namespaced-beers.xml


- name: Remove non-existing namespaced '/bus:business/rat:rating/@attr:subjective'
  xml:
    path: /tmp/ansible-xml-namespaced-beers.xml
    xpath: /bus:business/rat:rating/@attr:subjective
    namespaces:
      bus: http://test.business
      ber: http://test.beers
      rat: http://test.rating
      attr: http://test.attribute
    state: absent
  register: remove_namespaced_attribute

- name: Compare to expected result
  copy:
    src: results/test-remove-namespaced-attribute.xml
    dest: /tmp/ansible-xml-namespaced-beers.xml
  check_mode: true
  diff: true
  register: comparison

- name: Test expected result
  assert:
    that:
      - remove_namespaced_attribute is not changed
      - comparison is not changed  # identical
  # command: diff -u {{ role_path }}/results/test-remove-namespaced-attribute.xml /tmp/ansible-xml-namespaced-beers.xml
