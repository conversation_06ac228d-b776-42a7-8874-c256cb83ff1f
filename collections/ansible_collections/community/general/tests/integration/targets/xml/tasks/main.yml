---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Install lxml (FreeBSD)
  package:
    name: 'py{{ ansible_python.version.major }}{{ ansible_python.version.minor }}-lxml'
    state: present
  when: ansible_os_family == "FreeBSD"

- name: Install requirements (RHEL 8)
  package:
    name:
      - libxml2-devel
      - libxslt-devel
      - python3-lxml
    state: present
  when: ansible_distribution == "RedHat" and ansible_distribution_major_version == "8"

# Needed for MacOSX !
- name: Install lxml
  pip:
    name: lxml
    state: present
#  when: ansible_os_family == "Darwin"

- name: Get lxml version
  command: "{{ ansible_python_interpreter }} -c 'from lxml import etree; print(\".\".join(str(v) for v in etree.LXML_VERSION))'"
  register: lxml_version

- name: Set lxml capabilities as variables
  set_fact:
    # NOTE: Some tests require predictable element attribute order,
    #       which is only guaranteed starting from lxml v3.0alpha1
    lxml_predictable_attribute_order: '{{ lxml_version.stdout is version("3", ">=") }}'

    # NOTE: The xml module requires at least lxml v2.3.0
    lxml_xpath_attribute_result_attrname: '{{ lxml_version.stdout is version("2.3.0", ">=") }}'

- name: Only run the tests when lxml v2.3.0+
  when: lxml_xpath_attribute_result_attrname
  block:

    - include_tasks: test-add-children-elements.yml
    - include_tasks: test-add-children-from-groupvars.yml
    - include_tasks: test-add-children-insertafter.yml
    - include_tasks: test-add-children-insertbefore.yml
    - include_tasks: test-add-children-with-attributes.yml
    - include_tasks: test-add-element-implicitly.yml
    - include_tasks: test-count.yml
    - include_tasks: test-mutually-exclusive-attributes.yml
    - include_tasks: test-remove-attribute.yml
    - include_tasks: test-remove-attribute-nochange.yml
    - include_tasks: test-remove-element.yml
    - include_tasks: test-remove-element-nochange.yml
    - include_tasks: test-set-attribute-value.yml
    - include_tasks: test-set-children-elements.yml
    - include_tasks: test-set-children-elements-level.yml
    - include_tasks: test-set-children-elements-value.yml
    - include_tasks: test-set-element-value.yml
    - include_tasks: test-set-element-value-empty.yml
    - include_tasks: test-pretty-print.yml
    - include_tasks: test-pretty-print-only.yml
    - include_tasks: test-add-namespaced-children-elements.yml
    - include_tasks: test-remove-namespaced-attribute.yml
    - include_tasks: test-remove-namespaced-attribute-nochange.yml
    - include_tasks: test-set-namespaced-attribute-value.yml
    - include_tasks: test-set-namespaced-element-value.yml
    - include_tasks: test-set-namespaced-children-elements.yml
    - include_tasks: test-get-element-content.yml
    - include_tasks: test-xmlstring.yml
    - include_tasks: test-children-elements-xml.yml

    # Unicode tests
    - include_tasks: test-add-children-elements-unicode.yml
    - include_tasks: test-add-children-with-attributes-unicode.yml
    - include_tasks: test-set-attribute-value-unicode.yml
    - include_tasks: test-count-unicode.yml
    - include_tasks: test-get-element-content.yml
    - include_tasks: test-set-children-elements-unicode.yml
    - include_tasks: test-set-element-value-unicode.yml
