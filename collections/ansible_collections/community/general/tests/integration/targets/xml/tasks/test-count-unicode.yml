---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Setup test fixture
  copy:
    src: fixtures/ansible-xml-beers-unicode.xml
    dest: /tmp/ansible-xml-beers-unicode.xml


- name: Count child element
  xml:
    path: /tmp/ansible-xml-beers-unicode.xml
    xpath: /business/beers/beer
    count: true
  register: beers

- name: Test expected result
  assert:
    that:
      - beers is not changed
      - beers.count == 2
