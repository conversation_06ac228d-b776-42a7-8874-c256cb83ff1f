---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Setup test fixture
  copy:
    src: fixtures/ansible-xml-beers.xml
    dest: /tmp/ansible-xml-beers.xml


- name: Add 2nd '/business/rating' with value '5'
  xml:
    path: /tmp/ansible-xml-beers.xml
    xpath: /business
    add_children:
      - rating: '5'

- name: Set '/business/rating' to '5'
  xml:
    path: /tmp/ansible-xml-beers.xml
    xpath: /business/rating
    value: '5'
  register: set_element_first_run

- name: Set '/business/rating' to '5'... again
  xml:
    path: /tmp/ansible-xml-beers.xml
    xpath: /business/rating
    value: '5'
  register: set_element_second_run

- name: Add trailing newline
  shell: echo "" >> /tmp/ansible-xml-beers.xml

- name: Compare to expected result
  copy:
    src: results/test-set-element-value.xml
    dest: /tmp/ansible-xml-beers.xml
  check_mode: true
  diff: true
  register: comparison

- name: Test expected result
  assert:
    that:
      - set_element_first_run is changed
      - set_element_second_run is not changed
      - comparison is not changed  # identical
  # command: diff -u {{ role_path }}/results/test-set-element-value.xml /tmp/ansible-xml-beers.xml
