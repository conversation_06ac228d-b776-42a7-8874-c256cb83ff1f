---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Setup test fixture
  copy:
    src: fixtures/ansible-xml-namespaced-beers.xml
    dest: /tmp/ansible-xml-namespaced-beers-xml.xml

- name: Set child elements
  xml:
    path: /tmp/ansible-xml-namespaced-beers-xml.xml
    xpath: /bus:business/ber:beers
    namespaces:
      bus: http://test.business
      ber: http://test.beers
    set_children:
      - beer: 90 Minute IPA
      - beer: Harvest Pumpkin Ale

- name: Copy state after first set_children
  copy:
    src: /tmp/ansible-xml-namespaced-beers.xml
    dest: /tmp/ansible-xml-namespaced-beers-1.xml
    remote_src: true

- name: Set child elements again
  xml:
    path: /tmp/ansible-xml-namespaced-beers-xml.xml
    xpath: /bus:business/ber:beers
    namespaces:
      bus: http://test.business
      ber: http://test.beers
    set_children:
      - beer: 90 Minute IPA
      - beer: Harvest Pumpkin Ale
  register: set_children_again

- name: Copy state after second set_children
  copy:
    src: /tmp/ansible-xml-namespaced-beers.xml
    dest: /tmp/ansible-xml-namespaced-beers-2.xml
    remote_src: true

- name: Compare to expected result
  copy:
    src: /tmp/ansible-xml-namespaced-beers-1.xml
    dest: /tmp/ansible-xml-namespaced-beers-2.xml
    remote_src: true
  check_mode: true
  diff: true
  register: comparison
  # command: diff /tmp/ansible-xml-namespaced-beers-1.xml /tmp/ansible-xml-namespaced-beers-2.xml

- name: Test expected result
  assert:
    that:
      - set_children_again is not changed  # idempotency
      - set_namespaced_attribute_value is changed
      - comparison is not changed  # identical
