---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Setup test fixture
  copy:
    src: fixtures/ansible-xml-namespaced-beers.xml
    dest: /tmp/ansible-xml-namespaced-beers.xml


- name: Set namespaced '/bus:business/rat:rating/@attr:subjective' to 'false'
  xml:
    path: /tmp/ansible-xml-namespaced-beers.xml
    xpath: /bus:business/rat:rating
    namespaces:
      bus: http://test.business
      ber: http://test.beers
      rat: http://test.rating
      attr: http://test.attribute
    attribute: attr:subjective
    value: 'false'
  register: set_namespaced_attribute_value

- name: Add trailing newline
  shell: echo "" >> /tmp/ansible-xml-namespaced-beers.xml

- name: Compare to expected result
  copy:
    src: results/test-set-namespaced-attribute-value.xml
    dest: /tmp/ansible-xml-namespaced-beers.xml
  check_mode: true
  diff: true
  register: comparison

- name: Test expected result
  assert:
    that:
      - set_namespaced_attribute_value is changed
      - comparison is not changed  # identical
  # command: diff -u {{ role_path }}/results/test-set-namespaced-attribute-value.xml /tmp/ansible-xml-namespaced-beers.xml
