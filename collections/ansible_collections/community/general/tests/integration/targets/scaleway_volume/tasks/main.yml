---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Make sure volume is not there before tests
  scaleway_volume:
    name: ansible-test-volume
    state: absent
    region: '{{ scaleway_region }}'
    organization: '{{ scaleway_organization }}'
  register: server_creation_check_task

- assert:
    that:
      - server_creation_check_task is success

- name: Create volume
  scaleway_volume:
    name: ansible-test-volume
    state: present
    region: '{{ scaleway_region }}'
    organization: '{{ scaleway_organization }}'
    "size": 10000000000
    volume_type: l_ssd
  register: server_creation_check_task

- debug: var=server_creation_check_task

- assert:
    that:
      - server_creation_check_task is success
      - server_creation_check_task is changed

- name: Make sure volume is deleted
  scaleway_volume:
    name: ansible-test-volume
    state: absent
    region: '{{ scaleway_region }}'
    organization: '{{ scaleway_organization }}'
  register: server_creation_check_task

- assert:
    that:
      - server_creation_check_task is success
      - server_creation_check_task is changed
