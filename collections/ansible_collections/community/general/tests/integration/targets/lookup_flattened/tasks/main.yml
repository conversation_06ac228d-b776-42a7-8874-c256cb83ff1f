---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: test with_flattened
  set_fact: '{{ item }}=flattened'
  with_community.general.flattened:
    - - a__
    - - b__
      - - c__
        - d__
- name: verify with_flattened results
  assert:
    that:
      - a__ == 'flattened'
      - b__ == 'flattened'
      - c__ == 'flattened'
      - d__ == 'flattened'
