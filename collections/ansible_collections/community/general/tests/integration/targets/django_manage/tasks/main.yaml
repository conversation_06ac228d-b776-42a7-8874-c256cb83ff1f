# Test code for django_manage module
#
# Copyright (c) 2020, <PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later
- name: Create temporary test directory
  tempfile:
    state: directory
    suffix: .django_manage
  register: tmp_django_root

- name: Install virtualenv
  package:
    name: "{{ os_package_name.virtualenv }}"
    state: present

- name: Install required library
  pip:
    name: django
    state: present
    virtualenv: "{{ tmp_django_root.path }}/venv"

- name: Copy files
  copy:
    src: base_test/
    dest: "{{ tmp_django_root.path }}"
    mode: preserve

- name: Create project
  command:
    chdir: "{{ tmp_django_root.path }}/startproj"
    cmd: "{{ tmp_django_root.path }}/venv/bin/django-admin startproject test_django_manage_1"

- name: Create app
  command:
    chdir: "{{ tmp_django_root.path }}/startproj"
    cmd: "{{ tmp_django_root.path }}/venv/bin/django-admin startapp app1"

- name: Make manage.py executable
  file:
    path: "{{ tmp_django_root.path }}/startproj/test_django_manage_1/manage.py"
    mode: "0755"

- name: Check
  community.general.django_manage:
    project_path: "{{ tmp_django_root.path }}/startproj/test_django_manage_1"
    command: check
    virtualenv: "{{ tmp_django_root.path }}/venv"

- name: Check simple_project
  community.general.django_manage:
    project_path: "{{ tmp_django_root.path }}/simple_project/p1"
    command: check
    virtualenv: "{{ tmp_django_root.path }}/venv"

- name: Check custom project
  community.general.django_manage:
    project_path: "{{ tmp_django_root.path }}/1045-single-app-project/single_app_project"
    pythonpath: "{{ tmp_django_root.path }}/1045-single-app-project/"
    command: check
    virtualenv: "{{ tmp_django_root.path }}/venv"

- name: Run collectstatic --noinput on simple project
  community.general.django_manage:
    project_path: "{{ tmp_django_root.path }}/simple_project/p1"
    command: collectstatic --noinput
    virtualenv: "{{ tmp_django_root.path }}/venv"

- name: Trigger exception with environment variable
  community.general.django_manage:
    project_path: "{{ tmp_django_root.path }}/simple_project/p1"
    command: collectstatic --noinput
    virtualenv: "{{ tmp_django_root.path }}/venv"
  environment:
    DJANGO_ANSIBLE_RAISE: blah
  ignore_errors: true
  register: env_raise

- name: Check env variable reached manage.py
  ansible.builtin.assert:
    that:
      - "'ValueError: DJANGO_ANSIBLE_RAISE=blah' in env_raise.msg"
