---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# test code for the zypper module
# Copyright 2015, <PERSON> <<EMAIL>>
# heavily based on the yum tests which are
# Copyright 2014, <PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- include_tasks: 'zypper.yml'
  when: ansible_os_family == 'Suse'
