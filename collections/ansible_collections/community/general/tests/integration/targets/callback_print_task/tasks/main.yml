---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Run tests
  include_role:
    name: callback
  vars:
    tests:
      - name: community.general.print_task is not enabled
        environment:
          ANSIBLE_NOCOLOR: 'true'
          ANSIBLE_FORCE_COLOR: 'false'
        playbook: |
          - hosts: testhost
            gather_facts: false
            tasks:
              - name: Sample task
                debug:
                  msg: This is a test
        expected_output:
          - ""
          - "PLAY [testhost] ****************************************************************"
          - ""
          - "TASK [Sample task] *************************************************************"
          - "ok: [testhost] => {"
          - "    \"msg\": \"This is a test\""
          - "}"
          - ""
          - "PLAY RECAP *********************************************************************"
          - "testhost                   : ok=1    changed=0    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   "

      - name: community.general.print_task is enabled
        environment:
          ANSIBLE_NOCOLOR: 'true'
          ANSIBLE_FORCE_COLOR: 'false'
          ANSIBLE_CALLBACKS_ENABLED: 'community.general.print_task'
        playbook: |
          - hosts: testhost
            gather_facts: false
            tasks:
              - name: Sample task
                debug:
                  msg: This is a test
        expected_output:
          - ""
          - "PLAY [testhost] ****************************************************************"
          - ""
          - "TASK [Sample task] *************************************************************"
          - ""
          - "- name: Sample task"
          - "  debug:"
          - "    msg: This is a test"
          - ""
          - "ok: [testhost] => {"
          - "    \"msg\": \"This is a test\""
          - "}"
          - ""
          - "PLAY RECAP *********************************************************************"
          - "testhost                   : ok=1    changed=0    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   "

      - name: Print with msg parameter on the same line
        environment:
          ANSIBLE_NOCOLOR: 'true'
          ANSIBLE_FORCE_COLOR: 'false'
          ANSIBLE_CALLBACKS_ENABLED: 'community.general.print_task'
        playbook: |
          - hosts: testhost
            gather_facts: false
            tasks:
              - name: Sample task
                debug: msg="This is a test"
        expected_output:
          - ""
          - "PLAY [testhost] ****************************************************************"
          - ""
          - "TASK [Sample task] *************************************************************"
          - ""
          - "- name: Sample task"
          - "  debug: msg=\"This is a test\""
          - ""
          - "ok: [testhost] => {"
          - "    \"msg\": \"This is a test\""
          - "}"
          - ""
          - "PLAY RECAP *********************************************************************"
          - "testhost                   : ok=1    changed=0    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   "

      - name: Task with additional parameters
        environment:
          ANSIBLE_NOCOLOR: 'true'
          ANSIBLE_FORCE_COLOR: 'false'
          ANSIBLE_CALLBACKS_ENABLED: 'community.general.print_task'
        playbook: |
          - hosts: testhost
            gather_facts: false
            tasks:
              - name: Sample task
                when: True
                vars:
                  test_var: "Hello World"
                debug:
                  var: test_var
        expected_output:
          - ""
          - "PLAY [testhost] ****************************************************************"
          - ""
          - "TASK [Sample task] *************************************************************"
          - ""
          - "- name: Sample task"
          - "  when: true"
          - "  vars:"
          - "    test_var: Hello World"
          - "  debug:"
          - "    var: test_var"
          - ""
          - "ok: [testhost] => {"
          - "    \"test_var\": \"Hello World\""
          - "}"
          - ""
          - "PLAY RECAP *********************************************************************"
          - "testhost                   : ok=1    changed=0    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   "
