# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Plain MQTT protocol
listener 1883

# MQTT over TLS 1.1
listener 8883
tls_version tlsv1.1
cafile /tls/ca_certificate.pem
certfile /tls/server_certificate.pem
keyfile /tls/server_key.pem

# MQTT over TLS 1.2
listener 8884
tls_version tlsv1.2
cafile /tls/ca_certificate.pem
certfile /tls/server_certificate.pem
keyfile /tls/server_key.pem

# TODO(This does not appear to be supported on Ubuntu 18.04. Re-try on 20.04 or next LTS release)
# MQTT over TLS 1.3
#
# listener 8885
# tls_version tlsv1.3
# cafile /tls/ca_certificate.pem
# certfile /tls/server_certificate.pem
# keyfile /tls/server_key.pem

log_dest syslog

log_type error
log_type warning
log_type notice
log_type information
log_type debug

connection_messages true
