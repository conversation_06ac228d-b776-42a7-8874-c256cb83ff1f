---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Test valid hashable inputs
  assert:
    that:
      - "single_int | community.general.hashids_encode | community.general.hashids_decode == [single_int]"
      - "int_list | community.general.hashids_encode | community.general.hashids_decode | list == int_list"
      - "(1,2,3) | community.general.hashids_encode | community.general.hashids_decode == [1,2,3]"

- name: Test valid parameters
  assert:
    that:
      - "single_int | community.general.hashids_encode(salt='test') | community.general.hashids_decode(salt='test') == [single_int]"
      - "single_int | community.general.hashids_encode(alphabet='1234567890abcdef') | community.general.hashids_decode(alphabet='1234567890abcdef') == [single_int]"
      - "single_int | community.general.hashids_encode(min_length=20) | community.general.hashids_decode(min_length=20) == [single_int]"
      - "single_int | community.general.hashids_encode(min_length=20) | length == 20"

- name: Test valid unhashable inputs
  assert:
    that:
      - "single_float | community.general.hashids_encode | community.general.hashids_decode == []"
      - "arbitrary_string | community.general.hashids_encode | community.general.hashids_decode == []"

- name: Register result of invalid salt
  debug:
    var: "single_int | community.general.hashids_encode(salt=10)"
  register: invalid_salt_message
  ignore_errors: true

- name: Test invalid salt fails
  assert:
    that:
      - invalid_salt_message is failed

- name: Register result of invalid alphabet
  debug:
    var: "single_int | community.general.hashids_encode(alphabet='abc')"
  register: invalid_alphabet_message
  ignore_errors: true

- name: Test invalid alphabet fails
  assert:
    that:
      - invalid_alphabet_message is failed

- name: Register result of invalid min_length
  debug:
    var: "single_int | community.general.hashids_encode(min_length='foo')"
  register: invalid_min_length_message
  ignore_errors: true

- name: Test invalid min_length fails
  assert:
    that:
      - invalid_min_length_message is failed
