---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Get snapshot information and register it in a variable
  scaleway_snapshot_info:
    region: par1
  register: snapshots

- name: Display snapshots variable
  debug:
    var: snapshots

- name: Ensure retrieval of snapshots info is success
  assert:
    that:
      - snapshots is success

- name: Get snapshot information and register it in a variable (AMS1)
  scaleway_snapshot_info:
    region: ams1
  register: ams1_snapshots

- name: Display snapshots variable (AMS1)
  debug:
    var: ams1_snapshots

- name: Ensure retrieval of snapshots info is success (AMS1)
  assert:
    that:
      - ams1_snapshots is success
