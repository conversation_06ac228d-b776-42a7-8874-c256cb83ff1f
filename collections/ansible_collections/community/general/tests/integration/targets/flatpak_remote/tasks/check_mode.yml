---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# - Tests with absent flatpak remote -------------------------------------------

# state=present

- name: Test addition of absent flatpak remote (check mode)
  flatpak_remote:
    name: flatpak-test
    flatpakrepo_url: /tmp/flatpak/repo/dummy-repo.flatpakrepo
    state: present
  register: addition_result
  check_mode: true

- name: Verify addition of absent flatpak remote test result (check mode)
  assert:
    that:
      - addition_result is changed
    msg: "Adding an absent flatpak remote shall mark module execution as changed"

- name: Test non-existent idempotency of addition of absent flatpak remote (check mode)
  flatpak_remote:
    name: flatpak-test
    flatpakrepo_url: /tmp/flatpak/repo/dummy-repo.flatpakrepo
    state: present
  register: double_addition_result
  check_mode: true

- name: >
      Verify non-existent idempotency of addition of absent flatpak remote
      test result (check mode)
  assert:
    that:
      - double_addition_result is changed
    msg: |
        Adding an absent flatpak remote a second time shall still mark module execution
        as changed in check mode

# state=absent

- name: Test removal of absent flatpak remote not doing anything in check mode
  flatpak_remote:
    name: flatpak-test
    state: absent
  register: removal_result
  check_mode: true

- name: Verify removal of absent flatpak remote test result (check mode)
  assert:
    that:
      - removal_result is not changed
    msg: "Removing an absent flatpak remote shall mark module execution as not changed"


# - Tests with present flatpak remote -------------------------------------------

# state=present

- name: Test addition of present flatpak remote (check mode)
  flatpak_remote:
    name: check-mode-test-remote
    flatpakrepo_url: /tmp/flatpak/repo/dummy-repo.flatpakrepo
    state: present
  register: addition_result
  check_mode: true

- name: Verify addition of present flatpak remote test result (check mode)
  assert:
    that:
      - addition_result is not changed
    msg: "Adding a present flatpak remote shall mark module execution as not changed"

# state=absent

- name: Test removal of present flatpak remote not doing anything in check mode
  flatpak_remote:
    name: check-mode-test-remote
    state: absent
  register: removal_result
  check_mode: true

- name: Verify removal of present flatpak remote test result (check mode)
  assert:
    that:
      - removal_result is changed
    msg: "Removing a present flatpak remote shall mark module execution as changed"

- name: Test non-existent idempotency of removal of present flatpak remote (check mode)
  flatpak_remote:
    name: check-mode-test-remote
    state: absent
  register: double_removal_result
  check_mode: true

- name: >
      Verify non-existent idempotency of removal of present flatpak remote
      test result (check mode)
  assert:
    that:
      - double_removal_result is changed
    msg: |
        Removing a present flatpak remote a second time shall still mark module execution
        as changed in check mode


# - Tests with disabled flatpak remote ------------------------------------------

# enabled=true

- name: Test activation of disabled flatpak remote (check mode)
  flatpak_remote:
    name: check-mode-disabled-test-remote
    enabled: true
  register: activation_result
  check_mode: true

- name: Verify activation of disabled flatpak remote test result (check mode)
  assert:
    that:
      - activation_result is changed
    msg: "Enabling an disabled flatpak remote shall mark module execution as changed"

- name: Test non-existent idempotency of activation of disabled flatpak remote (check mode)
  flatpak_remote:
    name: check-mode-disabled-test-remote
    enabled: true
  register: double_activation_result
  check_mode: true

- name: >
      Verify non-existent idempotency of activation of disabled flatpak remote
      test result (check mode)
  assert:
    that:
      - double_activation_result is changed
    msg: |
        Enabling an disabled flatpak remote a second time shall still mark module execution
        as changed in check mode

# enabled=false

- name: Test deactivation of disabled flatpak remote not doing anything in check mode
  flatpak_remote:
    name: check-mode-disabled-test-remote
    enabled: false
  register: deactivation_result
  check_mode: true

- name: Verify deactivation of disabled flatpak remote test result (check mode)
  assert:
    that:
      - deactivation_result is not changed
    msg: "Disabling an disabled flatpak remote shall mark module execution as not changed"


# - Tests with enabled flatpak remote ------------------------------------------

# enabled=true

- name: Test activation of enabled flatpak remote (check mode)
  flatpak_remote:
    name: check-mode-enabled-test-remote
    enabled: true
  register: activation_result
  check_mode: true

- name: Verify activation of enabled flatpak remote test result (check mode)
  assert:
    that:
      - activation_result is not changed
    msg: "Enabling a enabled flatpak remote shall mark module execution as not changed"

# enabled=false

- name: Test deactivation of enabled flatpak remote not doing anything in check mode
  flatpak_remote:
    name: check-mode-enabled-test-remote
    enabled: false
  register: deactivation_result
  check_mode: true

- name: Verify deactivation of enabled flatpak remote test result (check mode)
  assert:
    that:
      - deactivation_result is changed
    msg: "Disabling a enabled flatpak remote shall mark module execution as changed"

- name: Test non-existent idempotency of deactivation of enabled flatpak remote (check mode)
  flatpak_remote:
    name: check-mode-enabled-test-remote
    enabled: false
  register: double_deactivation_result
  check_mode: true

- name: >
      Verify non-existent idempotency of deactivation of enabled flatpak remote
      test result (check mode)
  assert:
    that:
      - double_deactivation_result is changed
    msg: |
        "Disabling a enabled flatpak remote a second time shall still mark module execution
        as changed in check mode
