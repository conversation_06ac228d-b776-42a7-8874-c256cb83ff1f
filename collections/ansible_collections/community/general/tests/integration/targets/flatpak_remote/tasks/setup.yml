---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Install flatpak on Fedora
  dnf:
    name: flatpak
    state: present
  when: ansible_distribution == 'Fedora'
- block:
    - name: Activate flatpak ppa on Ubuntu versions older than 18.04/bionic
      apt_repository:
        repo: ppa:alex<PERSON>son/flatpak
        state: present
        mode: '0644'
      when: ansible_lsb.major_release | int < 18
    - name: Install flatpak package on Ubuntu
      apt:
        name: flatpak
        state: present
  when: ansible_distribution == 'Ubuntu'
- name: Install flatpak remote for testing check mode
  flatpak_remote:
    name: check-mode-test-remote
    flatpakrepo_url: /tmp/flatpak/repo/dummy-repo.flatpakrepo
    state: present
    enabled: true
- name: Install disabled flatpak remote for testing check mode
  flatpak_remote:
    name: check-mode-disabled-test-remote
    flatpakrepo_url: /tmp/flatpak/repo/dummy-repo.flatpakrepo
    state: present
    enabled: false
- name: Install enabled flatpak remote for testing check mode
  flatpak_remote:
    name: check-mode-enabled-test-remote
    flatpakrepo_url: /tmp/flatpak/repo/dummy-repo.flatpakrepo
    state: present
    enabled: true
