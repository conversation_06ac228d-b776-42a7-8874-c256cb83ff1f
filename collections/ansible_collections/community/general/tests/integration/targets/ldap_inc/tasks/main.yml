---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Run LDAP search module tests
  block:
    - include_tasks: "{{ item }}"
      with_fileglob:
        - 'tests/*.yml'
  when: ansible_os_family in ['Ubuntu', 'Debian']
