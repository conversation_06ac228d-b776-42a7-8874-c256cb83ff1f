// Copyright (c) Ansible Project
// GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
// SPDX-License-Identifier: GPL-3.0-or-later

To be able to run these integration tests a keycloak server must be
reachable under a specific url with a specific admin user and password.
The exact values expected for these parameters can be found in
'vars/main.yml' file. A vanilla Keycloak server will not be sufficient:
you will need to deploy a custom JAR file with two policies:

* _MyPolicy1:_ policy-1.js
* _MyPolicy2:_ policy-2.js

To create a customized Keycloak test instance running on Podman first
install the "zip" command, go to the policy subdirectory and then do

[source,shell]
----
./build-policy.sh
podman build --tag keycloak_authz_custom_policy_test:1.0.0 .
podman rm mykeycloak && podman run --name mykeycloak -p 8080:8080 -e KEYCLOAK_ADMIN=admin -e KEYCLOAK_ADMIN_PASSWORD=password -e KC_HTTP_RELATIVE_PATH=/auth localhost/keycloak_authz_custom_policy_test:1.0.0  start-dev
----

This process probably also work with Docker just by replacing _podman_ with
_docker_. Modify the FROM argument in Containerfile to change Keycloak version
to test against. Quarkus versions of Keycloak should work - older versions
will not.
