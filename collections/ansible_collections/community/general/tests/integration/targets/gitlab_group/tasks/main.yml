---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Install required libs
  pip:
    name: python-gitlab
    state: present

- name: Cleanup GitLab Group
  gitlab_group:
    api_url: "{{ gitlab_host }}"
    validate_certs: false
    api_token: "{{ gitlab_login_token }}"
    name: ansible_test_group
    path: ansible_test_group
    state: absent

- name: Create GitLab Group
  gitlab_group:
    api_url: "{{ gitlab_host }}"
    validate_certs: false
    api_token: "{{ gitlab_login_token }}"
    name: ansible_test_group
    path: ansible_test_group
    state: present
  register: gitlab_group_state

- name: Test group created
  assert:
    that:
      - gitlab_group_state is changed


- name: Create GitLab Group ( Idempotency test )
  gitlab_group:
    api_url: "{{ gitlab_host }}"
    validate_certs: false
    api_token: "{{ gitlab_login_token }}"
    name: ansible_test_group
    path: ansible_test_group
    state: present
  register: gitlab_group_state_again

- name: Test module is idempotent
  assert:
    that:
      - gitlab_group_state_again is not changed

- name: Cleanup GitLab Group for Description Test
  gitlab_group:
    api_url: "{{ gitlab_host }}"
    validate_certs: false
    api_token: "{{ gitlab_login_token }}"
    name: ansible_test_group
    path: ansible_test_group
    state: absent

- name: Create GitLab Group for Description Test
  gitlab_group:
    api_url: "{{ gitlab_host }}"
    validate_certs: false
    api_token: "{{ gitlab_login_token }}"
    name: ansible_test_group
    path: ansible_test_group
    description: My Test Group
    state: present
  register: gitlab_group_state_desc

- name: Test group created with Description
  assert:
    that:
      - gitlab_group_state_desc.group.description == "My Test Group"

- name: Cleanup GitLab Group for project_creation_level Test
  gitlab_group:
    api_url: "{{ gitlab_host }}"
    validate_certs: false
    api_token: "{{ gitlab_login_token }}"
    name: ansible_test_group
    path: ansible_test_group
    state: absent

- name: Create GitLab Group for project_creation_level Test
  gitlab_group:
    api_url: "{{ gitlab_host }}"
    validate_certs: false
    api_token: "{{ gitlab_login_token }}"
    name: ansible_test_group
    path: ansible_test_group
    project_creation_level: noone
    state: present
  register: gitlab_group_state_pcl

- name: Test group created with project_creation_level
  assert:
    that:
      - gitlab_group_state_pcl.group.project_creation_level == "noone"

- name: Cleanup GitLab Group for require_two_factor_authentication Test
  gitlab_group:
    api_url: "{{ gitlab_host }}"
    validate_certs: false
    api_token: "{{ gitlab_login_token }}"
    name: ansible_test_group
    path: ansible_test_group
    state: absent

- name: Create GitLab Group for project_creation_level Test
  gitlab_group:
    api_url: "{{ gitlab_host }}"
    validate_certs: false
    api_token: "{{ gitlab_login_token }}"
    name: ansible_test_group
    path: ansible_test_group
    require_two_factor_authentication: true
    state: present
  register: gitlab_group_state_rtfa

- name: Test group created with project_creation_level
  assert:
    that:
      - gitlab_group_state_rtfa.group.require_two_factor_authentication == true
