---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: ensure application conan2 is uninstalled
  community.general.pipx:
    name: conan2
    state: absent

- name: Install Python Package conan with suffix 2 (conan2)
  community.general.pipx:
    name: conan
    state: install
    suffix: "2"
  register: install_conan2

- name: Install Python Package conan with suffix 2 (conan2) again
  community.general.pipx:
    name: conan
    state: install
    suffix: "2"
  register: install_conan2_again

- name: cleanup conan2
  community.general.pipx:
    name: conan2
    state: absent

- name: check assertions
  assert:
    that:
      - install_conan2 is changed
      - "'    - conan2' in install_conan2.stdout"
      - install_conan2_again is not changed
