---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: ensure application pyinstaller is uninstalled
  community.general.pipx:
    name: pyinstaller
    state: absent

- name: Install Python Package pyinstaller
  community.general.pipx:
    name: pyinstaller
    state: present
    system_site_packages: true
    pip_args: "--no-cache-dir"
  register: install_pyinstaller

- name: cleanup pyinstaller
  community.general.pipx:
    name: pyinstaller
    state: absent

- name: check assertions
  assert:
    that:
      - install_pyinstaller is changed
