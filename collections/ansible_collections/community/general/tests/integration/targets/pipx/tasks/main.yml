---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Bail out if Python < 3.8
  when: ansible_python_version is version('3.8', '<')
  ansible.builtin.meta: end_play
- name: Install pipx>=1.7.0
  ansible.builtin.pip:
    name: pipx>=1.7.0
    extra_args: --user

- name: Determine packaging level
  block:
    - name: Install packaging>=22.0
      ansible.builtin.pip:
        name: packaging>=22.0
    - name: Set has_packaging22 fact true
      ansible.builtin.set_fact:
        has_packaging22: true
  rescue:
    - name: Set has_packaging22 fact false
      ansible.builtin.set_fact:
        has_packaging22: false
    - name: Install has_packaging (no version spec)
      ansible.builtin.pip:
        name: packaging

##############################################################################
- name: ensure application tox is uninstalled
  community.general.pipx:
    state: absent
    name: tox
  register: uninstall_tox

- name: install application tox
  community.general.pipx:
    name: tox
  register: install_tox

- name: set fact latest_tox_version
  set_fact:
    latest_tox_version: "{{ install_tox.application.tox.version }}"

- name: install application tox again
  community.general.pipx:
    name: tox
  register: install_tox_again

- name: install application tox again force
  community.general.pipx:
    name: tox
    force: true
  register: install_tox_again_force

- name: uninstall application tox
  community.general.pipx:
    state: absent
    name: tox
  register: uninstall_tox

- name: check assertions tox
  assert:
    that:
      - install_tox is changed
      - "'version' in install_tox"
      - "'tox' in install_tox.application"
      - install_tox_again is not changed
      - install_tox_again_force is changed
      - uninstall_tox is changed
      - "'tox' not in uninstall_tox.application"

##############################################################################
- name: install application tox with system-site-packages
  community.general.pipx:
    name: tox
    system_site_packages: true
  register: install_tox

- name: get raw pipx_info
  community.general.pipx_info:
    include_raw: true
  register: pipx_info_raw

- name: uninstall application tox
  community.general.pipx:
    state: absent
    name: tox
  register: uninstall_tox

- name: check assertions tox
  assert:
    that:
      - install_tox is changed
      - "'tox' in install_tox.application"
      - pipx_info_raw is not changed
      - "'--system-site-packages' in pipx_info_raw.raw_output.venvs.tox.metadata.venv_args"
      - uninstall_tox is changed
      - "'tox' not in uninstall_tox.application"

##############################################################################
- name: install application tox 3.24.0
  community.general.pipx:
    name: tox
    source: tox==3.24.0
  register: install_tox_324

- name: reinstall tox 3.24.0
  community.general.pipx:
    name: tox
    state: reinstall
  register: reinstall_tox_324

- name: reinstall without name
  community.general.pipx:
    state: reinstall
  register: reinstall_noname
  ignore_errors: true

- name: upgrade tox from 3.24.0
  community.general.pipx:
    name: tox
    state: upgrade
  register: upgrade_tox_324

- name: upgrade without name
  community.general.pipx:
    state: upgrade
  register: upgrade_noname
  ignore_errors: true

- name: downgrade tox 3.24.0
  community.general.pipx:
    name: tox
    source: tox==3.24.0
    force: true
  register: downgrade_tox_324

- name: cleanup tox 3.24.0
  community.general.pipx:
    state: absent
    name: tox
  register: uninstall_tox_324

- name: check assertions tox 3.24.0
  assert:
    that:
      - install_tox_324 is changed
      - "'tox' in install_tox_324.application"
      - install_tox_324.application.tox.version == '3.24.0'
      - reinstall_tox_324 is changed
      - reinstall_tox_324.application.tox.version == '3.24.0'
      - upgrade_tox_324 is changed
      - upgrade_tox_324.application.tox.version != '3.24.0'
      - downgrade_tox_324 is changed
      - downgrade_tox_324.application.tox.version == '3.24.0'
      - uninstall_tox_324 is changed
      - "'tox' not in uninstall_tox_324.application"
      - upgrade_noname is failed
      - reinstall_noname is failed

##############################################################################
- name: install application latest tox
  community.general.pipx:
    name: tox
    state: latest
  register: install_tox_latest

- name: cleanup tox latest
  community.general.pipx:
    state: absent
    name: tox
  register: uninstall_tox_1

- name: install application tox 3.24.0 for latest
  community.general.pipx:
    name: tox
    source: tox==3.24.0
  register: install_tox_324_for_latest

- name: install application latest tox
  community.general.pipx:
    name: tox
    state: latest
  register: install_tox_latest_with_preinstall

- name: install application latest tox again
  community.general.pipx:
    name: tox
    state: latest
  register: install_tox_latest_with_preinstall_again

- name: install application latest tox (force)
  community.general.pipx:
    name: tox
    state: latest
    force: true
  register: install_tox_latest_with_preinstall_again_force

- name: cleanup tox latest again
  community.general.pipx:
    state: absent
    name: tox
  register: uninstall_tox_latest_again

- name: install application tox with deps
  community.general.pipx:
    state: latest
    name: tox
    install_deps: true
  register: install_tox_with_deps

- name: cleanup tox latest yet again
  community.general.pipx:
    state: absent
    name: tox
  register: uninstall_tox_2

- name: install tox with dependency group 'docs'
  community.general.pipx:
    name: tox
    source: tox[docs]
    state: latest
  register: install_tox_latest_docs

- name: install tox with dependency group 'docs' again
  community.general.pipx:
    name: tox
    source: tox[docs]
    state: latest
  register: install_tox_latest_docs_again

- name: cleanup tox latest yet again
  community.general.pipx:
    state: absent
    name: tox
  register: uninstall_tox_3

- name: check assertions tox latest
  assert:
    that:
      - install_tox_latest is changed
      - "'tox' in install_tox_latest.application"
      - install_tox_latest.application.tox.version != '3.24.0'
      - uninstall_tox_1 is changed
      - "'tox' not in uninstall_tox_1.application"
      - install_tox_324_for_latest is changed
      - "'tox' in install_tox_324_for_latest.application"
      - install_tox_324_for_latest.application.tox.version == '3.24.0'
      - install_tox_latest_with_preinstall is changed
      - "'tox' in install_tox_latest_with_preinstall.application"
      - install_tox_latest_with_preinstall.application.tox.version != '3.24.0'
      - install_tox_latest_with_preinstall_again is not changed
      - install_tox_latest_with_preinstall_again_force is changed
      - uninstall_tox_2 is changed
      - "'tox' not in uninstall_tox_2.application"
      - install_tox_latest_docs is changed
      - install_tox_latest_docs_again is not changed
      - uninstall_tox_3 is changed
      - "'tox' not in uninstall_tox_3.application"

##############################################################################
# Test version specifiers in name parameter
- name: Run version specifier tests
  ansible.builtin.include_tasks: testcase-10031-version-specs.yml

##############################################################################

- name: Include testcase for inject packages
  ansible.builtin.include_tasks: testcase-injectpkg.yml

- name: Include testcase for jupyter
  ansible.builtin.include_tasks: testcase-jupyter.yml

- name: Include testcase for old site-wide
  ansible.builtin.include_tasks: testcase-oldsitewide.yml

- name: Include testcase for issue 7497
  ansible.builtin.include_tasks: testcase-7497.yml

- name: Include testcase for issue 8656
  ansible.builtin.include_tasks: testcase-8656.yml

- name: Include testcase for PR 8793 --global
  ansible.builtin.include_tasks: testcase-8793-global.yml

- name: Include testcase for PR 8809 install-all
  ansible.builtin.include_tasks: testcase-8809-installall.yml

- name: Include testcase for PR 8809 pin
  ansible.builtin.include_tasks: testcase-8809-pin.yml

- name: Include testcase for PR 8809 injectpkg
  ansible.builtin.include_tasks: testcase-8809-uninjectpkg.yml

- name: Include testcase for PR 9009 injectpkg --global
  ansible.builtin.include_tasks: testcase-9009-fixglobal.yml

- name: Include testcase for PR 9103 upgrade --global
  ansible.builtin.include_tasks: testcase-9103-upgrade-global.yml

- name: Include testcase for issue 9619 latest --global
  ansible.builtin.include_tasks: testcase-9619-latest-global.yml
