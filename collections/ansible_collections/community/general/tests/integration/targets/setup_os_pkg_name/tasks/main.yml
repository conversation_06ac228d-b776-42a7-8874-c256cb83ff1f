---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Make sure we have the ansible_os_family and ansible_distribution_version facts
  ansible.builtin.setup:
    gather_subset: distribution
  when: ansible_facts == {}

- name: Create OS Package name fact
  ansible.builtin.set_fact:
    os_package_name: {}

- name: Include the files setting the package names
  ansible.builtin.include_tasks: "{{ file }}"
  loop_control:
    loop_var: file
  loop:
    - "default.yml"
    - "{{ ansible_os_family | lower }}.yml"
