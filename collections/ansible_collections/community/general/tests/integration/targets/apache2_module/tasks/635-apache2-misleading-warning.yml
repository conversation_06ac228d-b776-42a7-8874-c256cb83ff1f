---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later
# This test represent the misleading behavior of the following issue: https://github.com/ansible-collections/community.general/issues/635
- name: Disable MPM event module
  apache2_module:
    name: "{{ item.module}}"
    state: "{{ item.state}}"
    ignore_configcheck: true
  register: disable_mpm_modules
  with_items:
    - { module: mpm_event, state: absent }
    - { module: mpm_prefork, state: present }

- assert:
    that:
      - "'warnings' in disable_mpm_modules"
      - disable_mpm_modules["warnings"] == [
        "No MPM module loaded! apache2 reload AND other module actions will fail if no MPM module is loaded immediately.",
        "No MPM module loaded! apache2 reload AND other module actions will fail if no MPM module is loaded immediately."
        ]

- name: Enable MPM event module - Revert previous change
  apache2_module:
    name: "{{ item.module}}"
    state: "{{ item.state}}"
    ignore_configcheck: true
  register: disable_mpm_modules
  with_items:
    - { module: mpm_prefork, state: absent }
    - { module: mpm_event, state: present }

- name: Disable MPM event module
  apache2_module:
    name: "{{ item.module}}"
    state: "{{ item.state}}"
    ignore_configcheck: true
    warn_mpm_absent: false
  register: disable_mpm_modules
  with_items:
    - { module: mpm_event, state: absent }
    - { module: mpm_prefork, state: present }

- assert:
    that:
      - "'warnings' not in disable_mpm_modules"
