---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Create volume group on disk device
  lvg:
    vg: testvg
    pvs: "{{ loop_device1 }}"

- name: Save testvg uuid
  shell: vgs -ouuid --noheadings testvg | xargs -n1
  register: orig_vg_uuid_cmd_result

- name: Save pv uuid
  shell: "pvs -ouuid --noheadings {{ loop_device1 }} | xargs -n1"
  register: orig_pv_uuid_cmd_result

- name: Deactivate and reset vg/pv uuid
  lvg:
    state: inactive
    vg: testvg
    pvs: "{{ loop_device1 }}"
    reset_vg_uuid: true
    reset_pv_uuid: true
  register: vg_uuid_reset

- name: Save testvg uuid
  shell: vgs -ouuid --noheadings testvg | xargs -n1
  register: new_vg_uuid_cmd_result

- name: Save pv uuid
  shell: "pvs -ouuid --noheadings {{ loop_device1 }} | xargs -n1"
  register: new_pv_uuid_cmd_result

- name: Do all assertions to verify expected results
  assert:
    that:
      - vg_uuid_reset is changed
      - orig_vg_uuid_cmd_result.stdout != new_vg_uuid_cmd_result.stdout
      - orig_pv_uuid_cmd_result.stdout != new_pv_uuid_cmd_result.stdout

- name: Reset vg uuid again to verify non-idempotence
  lvg:
    vg: testvg
    reset_vg_uuid: true
  register: repeat_vg_uuid_reset

- name: Reset pv uuid again to verify non-idempotence
  lvg:
    vg: testvg
    reset_pv_uuid: true
    pvs: "{{ loop_device1 }}"
  register: repeat_pv_uuid_reset

- name: Save testvg uuid
  shell: vgs -ouuid --noheadings testvg | xargs -n1
  register: repeat_vg_uuid_cmd_result

- name: Save pv uuid
  shell: "pvs -ouuid --noheadings {{ loop_device1 }} | xargs -n1"
  register: repeat_pv_uuid_cmd_result

- name: Do all assertions to verify expected results
  assert:
    that:
      - repeat_vg_uuid_reset is changed
      - repeat_pv_uuid_reset is changed
      - new_vg_uuid_cmd_result.stdout != repeat_vg_uuid_cmd_result.stdout
      - new_pv_uuid_cmd_result.stdout != repeat_pv_uuid_cmd_result.stdout

- name: Reset vg uuid in check mode
  lvg:
    vg: testvg
    reset_vg_uuid: true
  register: check_mode_vg_uuid_reset
  check_mode: true

- name: Reset pv uuid in check mode
  lvg:
    vg: testvg
    reset_pv_uuid: true
    pvs: "{{ loop_device1 }}"
  register: check_mode_pv_uuid_reset
  check_mode: true

- name: Save testvg uuid
  shell: vgs -ouuid --noheadings testvg | xargs -n1
  register: check_mode_vg_uuid_cmd_result

- name: Save pv uuid
  shell: "pvs -ouuid --noheadings {{ loop_device1 }} | xargs -n1"
  register: check_mode_pv_uuid_cmd_result

- name: Do all assertions to verify expected results
  assert:
    that:
      - check_mode_vg_uuid_reset is changed
      - check_mode_pv_uuid_reset is changed
      - check_mode_vg_uuid_cmd_result.stdout == repeat_vg_uuid_cmd_result.stdout
      - check_mode_pv_uuid_cmd_result.stdout == repeat_pv_uuid_cmd_result.stdout

- name: Activate volume group
  lvg:
    state: active
    vg: testvg
  register: vg_activate
