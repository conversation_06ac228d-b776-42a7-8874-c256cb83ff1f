---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Create volume group on disk device
  lvg:
    vg: testvg
    pvs: "{{ loop_device1 }}"

- name: Create the volume group again to verify idempotence
  lvg:
    vg: testvg
    pvs: "{{ loop_device1 }}"
  register: repeat_vg_create

- name: Do all assertions to verify expected results
  assert:
    that:
      - repeat_vg_create is not changed
