---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Install required packages (Linux)
  package:
    name: lvm2
    state: present
  when: ansible_system == 'Linux'

- name: Test lvg module
  block:
    - import_tasks: setup.yml

    - import_tasks: setup_missing_pv.yml

    - import_tasks: test_indempotency.yml

    - import_tasks: test_grow_reduce.yml

    - import_tasks: test_remove_extra_pvs.yml

    - import_tasks: test_pvresize.yml

    - import_tasks: test_active_change.yml

    - import_tasks: test_active_create.yml

    - import_tasks: test_uuid_reset.yml
  always:
    - import_tasks: teardown.yml

    - import_tasks: teardown_missing_pv.yml
