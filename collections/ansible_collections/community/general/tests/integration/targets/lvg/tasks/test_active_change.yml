---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Create volume group on disk device
  lvg:
    vg: testvg
    pvs: "{{ loop_device1 }}"

- name: Create logical volumes on volume group
  loop:
    - lv1
    - lv2
  lvol:
    vg: testvg
    lv: "{{ item }}"
    size: 2m

- name: Create snapshot volumes of origin logical volumes
  loop:
    - lv1
    - lv2
  lvol:
    vg: testvg
    lv: "{{ item }}"
    snapshot: "{{ item }}_snap"
    size: 50%ORIGIN

- name: Collect all lv active status in testvg
  shell: vgs -olv_active --noheadings testvg | xargs -n1
  register: initial_lv_status_result

- name: Assert all lv in testvg are active
  loop: "{{ initial_lv_status_result.stdout_lines }}"
  assert:
    that:
      - "'active' == item"

- name: Deactivate volume group
  lvg:
    state: inactive
    vg: testvg
  register: vg_deactivate_result

- name: Collect all lv active status in testvg
  shell: vgs -olv_active --noheadings testvg | xargs -n1
  register: deactivated_lv_status_result

- name: Do all assertions to verify expected results
  assert:
    that:
      - vg_deactivate_result is changed
      - "'active' not in deactivated_lv_status_result.stdout"

- name: Deactivate volume group again to verify idempotence
  lvg:
    state: inactive
    vg: testvg
  register: repeated_vg_deactivate_result

- name: Verify vg deactivation idempontency
  assert:
    that:
      - repeated_vg_deactivate_result is not changed

- name: Activate volume group in check mode
  lvg:
    state: active
    vg: testvg
  register: check_mode_vg_activate_result
  check_mode: true

- name: Collect all lv active status in testvg
  shell: vgs -olv_active --noheadings testvg | xargs -n1
  register: check_mode_activate_lv_status_result

- name: Verify VG activation in check mode changed without activating LVs
  assert:
    that:
      - check_mode_vg_activate_result is changed
      - "'active' not in check_mode_activate_lv_status_result.stdout"

- name: Activate volume group
  lvg:
    state: active
    vg: testvg
  register: vg_activate_result

- name: Collect all lv active status in testvg
  shell: vgs -olv_active --noheadings testvg | xargs -n1
  register: activate_lv_status_result

- name: Verify vg activation
  assert:
    that:
      - vg_activate_result is changed

- name: Assert all lv in testvg are active
  loop: "{{ activate_lv_status_result.stdout_lines }}"
  assert:
    that:
      - "'active' == item"

- name: Activate volume group again to verify idempontency
  lvg:
    state: active
    vg: testvg
  register: repeated_vg_activate_result

- name: Verify vg activation idempontency
  assert:
    that:
      - repeated_vg_activate_result is not changed

- name: Deactivate lv2 in testvg
  lvol:
    vg: testvg
    lv: lv2
    active: false

- name: Activate volume group again to verify partially activated vg activation
  lvg:
    state: active
    vg: testvg
  register: partial_vg_activate_result

- name: Verify partially activated vg activation
  assert:
    that:
      - partial_vg_activate_result is changed

- name: Collect all lv active status in testvg
  shell: vgs -olv_active --noheadings testvg | xargs -n1
  register: activate_partial_lv_status_result

- name: Assert all lv in testvg are active
  loop: "{{ activate_partial_lv_status_result.stdout_lines }}"
  assert:
    that:
      - "'active' == item"

- name: Deactivate volume group in check mode
  lvg:
    state: inactive
    vg: testvg
  register: check_mode_vg_deactivate_result
  check_mode: true

- name: Collect all lv active status in testvg
  shell: vgs -olv_active --noheadings testvg | xargs -n1
  register: check_mode_deactivate_lv_status_result

- name: Verify check mode vg deactivation changed
  assert:
    that:
      - check_mode_vg_deactivate_result is changed

- name: Assert all lv in testvg are still active
  loop: "{{ check_mode_deactivate_lv_status_result.stdout_lines }}"
  assert:
    that:
      - "'active' == item"
