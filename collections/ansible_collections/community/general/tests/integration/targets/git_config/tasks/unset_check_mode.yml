---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- import_tasks: setup_value.yml

- name: unsetting value with check mode
  git_config:
    name: "{{ option_name }}"
    scope: "{{ option_scope }}"
    state: absent
  check_mode: true
  register: unset_result

- name: getting value
  git_config_info:
    name: "{{ option_name }}"
    scope: "{{ option_scope }}"
  register: get_result

- name: assert unset changed but dit not delete value
  assert:
    that:
      - unset_result is changed
      - unset_result.diff.before == option_value + "\n"
      - unset_result.diff.after == "\n"
      - get_result.config_value == option_value
...
