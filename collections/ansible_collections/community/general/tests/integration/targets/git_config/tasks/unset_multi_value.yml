---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- import_tasks: setup_value.yml

- name: unsetting "push.pushoption"
  git_config:
    name: push.pushoption
    scope: global
    state: absent
  register: unset_result

- name: getting all pushoptions values
  git_config_info:
    name: push.pushoption
    scope: global
  register: get_all_result

- name: assert unsetting muti-values
  assert:
    that:
      - unset_result is changed
      - 'get_all_result.config_values == {"push.pushoption": []}'
      - unset_result.diff.before == ["merge_request.create", "merge_request.draft", "merge_request.target=foobar"]
      - unset_result.diff.after == "\n"
...
