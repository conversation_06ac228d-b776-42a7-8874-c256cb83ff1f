---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# - Tests with absent flatpak --------------------------------------------------

# state=present on absent flatpak

- name: Test addition of absent flatpak (check mode)
  flatpak:
    name: com.dummy.App1
    remote: dummy-remote
    state: present
  register: addition_result
  check_mode: true

- name: Verify addition of absent flatpak test result (check mode)
  assert:
    that:
      - addition_result is changed
    msg: "Adding an absent flatpak shall mark module execution as changed"

- name: Test non-existent idempotency of addition of absent flatpak (check mode)
  flatpak:
    name: com.dummy.App1
    remote: dummy-remote
    state: present
  register: double_addition_result
  check_mode: true

- name: Verify non-existent idempotency of addition of absent flatpak test result (check mode)
  assert:
    that:
      - double_addition_result is changed
    msg: |
        Adding an absent flatpak a second time shall still mark module execution
        as changed in check mode

# state=absent on absent flatpak

- name: Test removal of absent flatpak check mode
  flatpak:
    name: com.dummy.App1
    state: absent
  register: removal_result
  check_mode: true

- name: Verify removal of absent flatpak test result (check mode)
  assert:
    that:
      - removal_result is not changed
    msg: "Removing an absent flatpak shall mark module execution as not changed"

# state=latest on absent flatpak

- name: Test state=latest of absent flatpak (check mode)
  flatpak:
    name: com.dummy.App1
    remote: dummy-remote
    state: latest
  register: latest_result
  check_mode: true

- name: Verify state=latest of absent flatpak test result (check mode)
  assert:
    that:
      - latest_result is changed
    msg: "state=latest an absent flatpak shall mark module execution as changed"

- name: Test non-existent idempotency of state=latest of absent flatpak (check mode)
  flatpak:
    name: com.dummy.App1
    remote: dummy-remote
    state: latest
  register: double_latest_result
  check_mode: true

- name: Verify non-existent idempotency of state=latest of absent flatpak test result (check mode)
  assert:
    that:
      - double_latest_result is changed
    msg: |
        state=latest an absent flatpak a second time shall still mark module execution
        as changed in check mode

# state=present with url on absent flatpak

- name: Test addition of absent flatpak with url (check mode)
  flatpak:
    name: http://127.0.0.1:8000/repo/com.dummy.App1.flatpakref
    remote: dummy-remote
    state: present
  register: url_addition_result
  check_mode: true

- name: Verify addition of absent flatpak with url test result (check mode)
  assert:
    that:
      - url_addition_result is changed
    msg: "Adding an absent flatpak from URL shall mark module execution as changed"

- name: Test non-existent idempotency of addition of absent flatpak with url (check mode)
  flatpak:
    name: http://127.0.0.1:8000/repo/com.dummy.App1.flatpakref
    remote: dummy-remote
    state: present
  register: double_url_addition_result
  check_mode: true

- name: >
      Verify non-existent idempotency of additionof absent flatpak with url test
      result (check mode)
  assert:
    that:
      - double_url_addition_result is changed
    msg: |
        Adding an absent flatpak from URL a second time shall still mark module execution
        as changed in check mode

# state=absent with url on absent flatpak

- name: Test removal of absent flatpak with url not doing anything (check mode)
  flatpak:
    name: http://127.0.0.1:8000/repo/com.dummy.App1.flatpakref
    state: absent
  register: url_removal_result
  check_mode: true

- name: Verify removal of absent flatpak with url test result (check mode)
  assert:
    that:
      - url_removal_result is not changed
    msg: "Removing an absent flatpak shall mark module execution as not changed"

# state=latest with url on absent flatpak

- name: Test state=latest of absent flatpak with url (check mode)
  flatpak:
    name: http://127.0.0.1:8000/repo/com.dummy.App1.flatpakref
    remote: dummy-remote
    state: latest
  register: url_latest_result
  check_mode: true

- name: Verify state=latest of absent flatpak with url test result (check mode)
  assert:
    that:
      - url_latest_result is changed
    msg: "state=latest an absent flatpak from URL shall mark module execution as changed"

- name: Test non-existent idempotency of state=latest of absent flatpak with url (check mode)
  flatpak:
    name: http://127.0.0.1:8000/repo/com.dummy.App1.flatpakref
    remote: dummy-remote
    state: latest
  register: double_url_latest_result
  check_mode: true

- name: >
      Verify non-existent idempotency of additionof state=latest flatpak with url test
      result (check mode)
  assert:
    that:
      - double_url_latest_result is changed
    msg: |
        state=latest an absent flatpak from URL a second time shall still mark module execution
        as changed in check mode

# - Tests with present flatpak -------------------------------------------------

# state=present on present flatpak

- name: Test addition of present flatpak (check mode)
  flatpak:
    name: com.dummy.App2
    remote: dummy-remote
    state: present
  register: addition_present_result
  check_mode: true

- name: Verify addition test result of present flatpak (check mode)
  assert:
    that:
      - addition_present_result is not changed
    msg: "Adding an present flatpak shall mark module execution as not changed"

# state=absent on present flatpak

- name: Test removal of present flatpak (check mode)
  flatpak:
    name: com.dummy.App2
    state: absent
  register: removal_present_result
  check_mode: true

- name: Verify removal of present flatpak test result (check mode)
  assert:
    that:
      - removal_present_result is changed
    msg: "Removing a present flatpak shall mark module execution as changed"

- name: Test non-existent idempotency of removal (check mode)
  flatpak:
    name: com.dummy.App2
    state: absent
  register: double_removal_present_result
  check_mode: true

- name: Verify non-existent idempotency of removal (check mode)
  assert:
    that:
      - double_removal_present_result is changed
    msg: |
        Removing a present flatpak a second time shall still mark module execution
        as changed in check mode

# state=latest on present flatpak

- name: Test state=latest of present flatpak (check mode)
  flatpak:
    name: com.dummy.App2
    remote: dummy-remote
    state: latest
  register: latest_present_result
  check_mode: true

- name: Verify latest test result of present flatpak (check mode)
  assert:
    that:
      - latest_present_result is changed
    msg: "state=latest an present flatpak shall mark module execution as changed"

# state=present with url on present flatpak

- name: Test addition with url of present flatpak (check mode)
  flatpak:
    name: http://127.0.0.1:8000/repo/com.dummy.App2.flatpakref
    remote: dummy-remote
    state: present
  register: url_addition_present_result
  check_mode: true

- name: Verify addition with url of present flatpak test result (check mode)
  assert:
    that:
      - url_addition_present_result is not changed
    msg: "Adding a present flatpak from URL shall mark module execution as not changed"

# state=absent with url on present flatpak

- name: Test removal with url of present flatpak (check mode)
  flatpak:
    name: http://127.0.0.1:8000/repo/com.dummy.App2.flatpakref
    state: absent
  register: url_removal_present_result
  check_mode: true

- name: Verify removal with url of present flatpak test result (check mode)
  assert:
    that:
      - url_removal_present_result is changed
    msg: "Removing an absent flatpak shall mark module execution as not changed"

- name: Test non-existent idempotency of removal with url of present flatpak (check mode)
  flatpak:
    name: http://127.0.0.1:8000/repo/com.dummy.App2.flatpakref
    remote: dummy-remote
    state: absent
  register: double_url_removal_present_result
  check_mode: true

- name: >
      Verify non-existent idempotency of installation with url of present
      flatpak test result (check mode)
  assert:
    that:
      - double_url_removal_present_result is changed
    msg: Removing an absent flatpak a second time shall still mark module execution as changed

# state=latest with url on present flatpak

- name: Test state=latest with url of present flatpak (check mode)
  flatpak:
    name: http://127.0.0.1:8000/repo/com.dummy.App2.flatpakref
    remote: dummy-remote
    state: latest
  register: url_latest_present_result
  check_mode: true

- name: Verify state=latest with url of present flatpak test result (check mode)
  assert:
    that:
      - url_latest_present_result is changed
    msg: "state=latest a present flatpak from URL shall mark module execution as changed"
