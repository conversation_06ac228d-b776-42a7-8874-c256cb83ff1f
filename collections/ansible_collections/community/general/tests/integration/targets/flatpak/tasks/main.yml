---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) 2018, <PERSON> <<EMAIL>>
# Copyright (c) 2018, Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- block:

    - import_tasks: setup.yml
      become: true

    # executable override

    - name: Test executable override
      flatpak:
        name: com.dummy.App1
        remote: dummy-remote
        state: present
        executable: nothing-that-exists
      ignore_errors: true
      register: executable_override_result

    - name: Verify executable override test result
      assert:
        that:
          - executable_override_result is failed
          - executable_override_result is not changed
        msg: "Specifying non-existing executable shall fail module execution"

    - import_tasks: check_mode.yml
      become: false

    - import_tasks: test.yml
      become: false
      vars:
        method: user

    - import_tasks: test.yml
      become: true
      vars:
        method: system

  always:

    - name: Check HTTP server status
      async_status:
        jid: "{{ webserver_status.ansible_job_id }}"
      ignore_errors: true

    - name: List processes
      command: ps aux

    - name: Stop HTTP server
      command: >-
        pkill -f -- '{{ remote_tmp_dir }}/serve.py'

  when: |
      ansible_distribution == 'Fedora' or
      ansible_distribution == 'Ubuntu' and not ansible_distribution_major_version | int < 16
