---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Install https transport for apt and ca-certificates
  apt: name={{ item }} state=latest force=yes
  with_items:
    - apt-transport-https
    - ca-certificates

- name: Install apt_key dependencies
  pip: name={{ item }}
  with_items:
    - pyOpenSSL
    - ndg-httpsclient
    - pyasn1

- name: Add InfluxDB public GPG key
  apt_key: url=https://repos.influxdata.com/influxdb.key state=present

- name: Add InfluxDB repository
  apt_repository: repo='deb https://repos.influxdata.com/ubuntu trusty stable' filename='influxdb' state=present update_cache=yes

- name: Install InfluxDB
  apt: name=influxdb state=latest

- name: Start InfluxDB service
  service: name=influxdb state=started
