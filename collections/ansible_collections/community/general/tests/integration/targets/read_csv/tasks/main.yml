---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Create basic CSV file
- name: Create unique CSV file
  copy:
    content: &users_content |
      name,uid,gid,gecos
      dag,500,500,<PERSON><PERSON>
      j<PERSON>,501,500,<PERSON><PERSON><PERSON>
    dest: "{{ remote_tmp_dir }}/users_unique.csv"

# Read a CSV file and access user 'dag'
- name: Read users from CSV file and return a dictionary
  read_csv:
    path: "{{ remote_tmp_dir }}/users_unique.csv"
    key: name
  register: users_unique

- assert:
    that:
      - users_unique.dict.dag.name == 'dag'
      - users_unique.dict.dag.gecos == 'Dag Wieërs'
      - users_unique.dict.dag.uid == '500'
      - users_unique.dict.dag.gid == '500'
      - users_unique.dict.jeroen.name == 'jeroen'
      - users_unique.dict.jeroen.gecos == 'Jeroen Hoekx'
      - users_unique.dict.jeroen.uid == '501'
      - users_unique.dict.jeroen.gid == '500'

# Read a CSV file and access the first item
- name: Read users from CSV file and return a list
  read_csv:
    path: "{{ remote_tmp_dir }}/users_unique.csv"
  register: users_unique

- assert:
    that:
      - users_unique.list.0.name == 'dag'
      - users_unique.list.0.gecos == 'Dag Wieërs'
      - users_unique.list.0.uid == '500'
      - users_unique.list.0.gid == '500'
      - users_unique.list.1.name == 'jeroen'
      - users_unique.list.1.gecos == 'Jeroen Hoekx'
      - users_unique.list.1.uid == '501'
      - users_unique.list.1.gid == '500'


# Create basic CSV file using semi-colon
- name: Create non-unique CSV file using semi-colon
  copy:
    content: |
      name;uid;gid;gecos
      dag;500;500;Dag Wieërs
      jeroen;501;500;Jeroen Hoekx
      dag;502;500;Dag Wieers
    dest: "{{ remote_tmp_dir }}/users_nonunique.csv"

# Read a CSV file and access user 'dag'
- name: Read users from CSV file and return a dictionary
  read_csv:
    path: "{{ remote_tmp_dir }}/users_nonunique.csv"
    key: name
    unique: false
    delimiter: ';'
  register: users_nonunique

- assert:
    that:
      - users_nonunique.dict.dag.name == 'dag'
      - users_nonunique.dict.dag.gecos == 'Dag Wieers'
      - users_nonunique.dict.dag.uid == '502'
      - users_nonunique.dict.dag.gid == '500'
      - users_nonunique.dict.jeroen.name == 'jeroen'
      - users_nonunique.dict.jeroen.gecos == 'Jeroen Hoekx'
      - users_nonunique.dict.jeroen.uid == '501'
      - users_nonunique.dict.jeroen.gid == '500'


# Read a CSV file using an non-existing dialect
- name: Read users from CSV file and return a dictionary
  read_csv:
    path: "{{ remote_tmp_dir }}/users_nonunique.csv"
    dialect: placebo
  register: users_placebo
  ignore_errors: true

- assert:
    that:
      - users_placebo is failed
      - users_placebo.msg == "Dialect 'placebo' is not supported by your version of python."


# Create basic CSV file without header
- name: Create unique CSV file without header
  copy:
    content: |
      dag,500,500,Dag Wieërs
      jeroen,501,500,Jeroen Hoekx
    dest: "{{ remote_tmp_dir }}/users_noheader.csv"

# Read a CSV file and access user 'dag'
- name: Read users from CSV file and return a dictionary
  read_csv:
    path: "{{ remote_tmp_dir }}/users_noheader.csv"
    key: name
    fieldnames: name,uid,gid,gecos
  register: users_noheader

- assert:
    that:
      - users_noheader.dict.dag.name == 'dag'
      - users_noheader.dict.dag.gecos == 'Dag Wieërs'
      - users_noheader.dict.dag.uid == '500'
      - users_noheader.dict.dag.gid == '500'
      - users_noheader.dict.jeroen.name == 'jeroen'
      - users_noheader.dict.jeroen.gecos == 'Jeroen Hoekx'
      - users_noheader.dict.jeroen.uid == '501'
      - users_noheader.dict.jeroen.gid == '500'


# Create broken file
- name: Create unique CSV file
  copy:
    content: |
      name,uid,gid,gecos
      dag,500,500,Dag Wieërs
      jeroen,501,500,"Jeroen"Hoekx"
    dest: "{{ remote_tmp_dir }}/users_broken.csv"

# Read a broken CSV file using strict
- name: Read users from a broken CSV file
  read_csv:
    path: "{{ remote_tmp_dir }}/users_broken.csv"
    key: name
    strict: true
  register: users_broken
  ignore_errors: true

- assert:
    that:
      - users_broken is failed
      - "'Unable to process file' in users_broken.msg"

# Create basic CSV file with BOM
- name: Create unique CSV file with BOM
  copy:
    content: "{{ bom + content }}"
    dest: "{{ remote_tmp_dir }}/users_bom.csv"
  vars:
    content: *users_content
    bom: "{{ '\ufeff' }}"

 # Read a CSV file and access the first item
- name: Read users from CSV file and return a list
  read_csv:
    path: "{{ remote_tmp_dir }}/users_bom.csv"
  register: users_bom

- assert:
    that:
      - users_bom.list.0.name == 'dag'
      - users_bom.list.0.gecos == 'Dag Wieërs'
      - users_bom.list.0.uid == '500'
      - users_bom.list.0.gid == '500'
      - users_bom.list.1.name == 'jeroen'
      - users_bom.list.1.gecos == 'Jeroen Hoekx'
      - users_bom.list.1.uid == '501'
      - users_bom.list.1.gid == '500'
