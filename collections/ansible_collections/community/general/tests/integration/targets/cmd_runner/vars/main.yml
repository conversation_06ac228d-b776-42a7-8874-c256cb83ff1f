# -*- coding: utf-8 -*-
# Copyright (c) 2022, <PERSON>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

cmd_echo_tests:
  - name: set aa and bb value
    arg_formats:
      aa:
        func: as_opt_eq_val
        args: [--answer]
      bb:
        func: as_bool
        args: [--bb-here]
    arg_order: 'aa bb'
    arg_values:
      bb: true
    aa: 11
    assertions:
      - test_result.rc == 0
      - test_result.out == "-- --answer=11 --bb-here\n"
      - test_result.err == ""

  - name: default aa value
    arg_formats:
      aa:
        func: as_opt_eq_val
        args: [--answer]
      bb:
        func: as_bool
        args: [--bb-here]
    arg_order: ['aa', 'bb']
    arg_values:
      aa: 43
      bb: true
    assertions:
      - test_result.rc == 0
      - test_result.out == "-- --answer=43 --bb-here\n"
      - test_result.err == ""

  - name: missing bb format
    arg_order: ['aa', 'bb']
    arg_formats:
      aa:
        func: as_opt_eq_val
        args: [--answer]
    arg_values:
      bb: true
    aa: 1984
    expect_error: true
    assertions:
      - test_result is failed
      - test_result.rc == 1
      - '"out" not in test_result'
      - '"err" not in test_result'
      - >-
        "MissingArgumentFormat: Cannot find format for parameter bb"
        in test_result.module_stderr

  - name: missing bb value
    arg_formats:
      aa:
        func: as_opt_eq_val
        args: [--answer]
      bb:
        func: as_bool
        args: [--bb-here]
    arg_order: 'aa bb'
    aa: 1984
    expect_error: true
    assertions:
      - test_result is failed
      - test_result.rc == 1
      - '"out" not in test_result'
      - '"err" not in test_result'
      - >-
        "MissingArgumentValue: Cannot find value for parameter bb"
        in test_result.module_stderr

  - name: set aa and bb value with check_mode on
    arg_formats:
      aa:
        func: as_opt_eq_val
        args: [--answer]
      bb:
        func: as_bool
        args: [--bb-here]
    arg_order: 'aa bb'
    arg_values:
      bb: true
    aa: 11
    check_mode: true
    assertions:
      - test_result.rc == 0
      - test_result.out == "-- --answer=11 --bb-here\n"
      - test_result.err == ""

  - name: set aa and bb value with check_mode and check_mode_skip on
    arg_formats:
      aa:
        func: as_opt_eq_val
        args: [--answer]
      bb:
        func: as_bool
        args: [--bb-here]
    arg_order: 'aa bb'
    arg_values:
      bb: true
    check_mode_skip: true
    aa: 11
    check_mode: true
    expect_error: true  # because if result contains rc != 0, ansible assumes error
    assertions:
      - test_result.rc == None
      - test_result.out == None
      - test_result.err == None

  - name: set aa and tt value
    arg_formats:
      aa:
        func: as_opt_eq_val
        args: [--answer]
      tt:
        func: as_opt_val
        args: [--tt-arg]
    arg_order: 'aa tt'
    arg_values:
      tt: potatoes
    aa: 11
    assertions:
      - test_result.rc == 0
      - test_result.out == "-- --answer=11 --tt-arg potatoes\n"
      - test_result.err == ""

  - name: use cmd echo
    cmd: echo
    arg_formats:
      aa:
        func: as_opt_eq_val
        args: [--answer]
      tt:
        func: as_opt_val
        args: [--tt-arg]
    arg_order: 'aa tt'
    arg_values:
      tt: potatoes
    aa: 11
    assertions:
      - test_result.rc == 0
      - test_result.out == "-- --answer=11 --tt-arg potatoes\n"
      - test_result.err == ""

  - name: use cmd /bin/echo
    cmd: /bin/echo
    arg_formats:
      aa:
        func: as_opt_eq_val
        args: [--answer]
      tt:
        func: as_opt_val
        args: [--tt-arg]
    arg_order: 'aa tt'
    arg_values:
      tt: potatoes
    aa: 11
    assertions:
      - test_result.rc == 0
      - test_result.out == "-- --answer=11 --tt-arg potatoes\n"
      - test_result.err == ""

  # this will not be in the regular set of paths get_bin_path() searches
  - name: use cmd {{ remote_tmp_dir }}/echo
    condition: >
      {{
        ansible_distribution != "MacOSX" and
        not (ansible_distribution == "CentOS" and ansible_distribution_major_version is version('7.0', '<'))
      }}
    copy_to: "{{ remote_tmp_dir }}"
    cmd: "{{ remote_tmp_dir }}/echo"
    arg_formats:
      aa:
        func: as_opt_eq_val
        args: [--answer]
      tt:
        func: as_opt_val
        args: [--tt-arg]
    arg_order: 'aa tt'
    arg_values:
      tt: potatoes
    aa: 11
    assertions:
      - test_result.rc == 0
      - test_result.out == "-- --answer=11 --tt-arg potatoes\n"
      - test_result.err == ""

  - name: use cmd echo with path_prefix {{ remote_tmp_dir }}
    cmd: echo
    condition: >
      {{
        ansible_distribution != "MacOSX" and
        not (ansible_distribution == "CentOS" and ansible_distribution_major_version is version('7.0', '<'))
      }}
    copy_to: "{{ remote_tmp_dir }}"
    path_prefix: "{{ remote_tmp_dir }}"
    arg_formats:
      aa:
        func: as_opt_eq_val
        args: [--answer]
      tt:
        func: as_opt_val
        args: [--tt-arg]
    arg_order: 'aa tt'
    arg_values:
      tt: potatoes
    aa: 11
    assertions:
      - test_result.rc == 0
      - test_result.out == "-- --answer=11 --tt-arg potatoes\n"
      - test_result.err == ""

  - name: use cmd never-existed
    cmd: never-existed
    arg_formats:
      aa:
        func: as_opt_eq_val
        args: [--answer]
      tt:
        func: as_opt_val
        args: [--tt-arg]
    arg_order: 'aa tt'
    arg_values:
      tt: potatoes
    aa: 11
    expect_error: true
    assertions:
      - >
        "Failed to find required executable" in test_result.msg

  - name: use cmd /usr/bin/never-existed
    cmd: /usr/bin/never-existed
    arg_formats:
      aa:
        func: as_opt_eq_val
        args: [--answer]
      tt:
        func: as_opt_val
        args: [--tt-arg]
    arg_order: 'aa tt'
    arg_values:
      tt: potatoes
    aa: 11
    expect_error: true
    assertions:
      - >
        "No such file or directory" in test_result.msg
        or
        "Error executing command." == test_result.msg
