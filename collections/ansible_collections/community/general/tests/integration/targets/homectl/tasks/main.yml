---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Get systemd version and if it doesn't exist don't run these tests.
- name: check systemd version
  command: "systemctl --version"
  register: systemd_version
  ignore_errors: true

- name: check homectl version
  command: homectl --version
  register: homectl_version
  ignore_errors: true

- block:
    - name: Install legacycrypt on Python 3.13+
      pip:
        name: legacycrypt
      when: ansible_python_version is version("3.13", ">=")

    - name: Check and start systemd-homed service
      service:
        name: systemd-homed.service
        state: started
        enabled: true

    - name: Add a user 'james'
      community.general.homectl:
        name: james
        password: myreallysecurepassword1!
        state: present

    - name: verify user added
      command: homectl inspect james
      register: james_info

    - name: Add the user 'tom' with a zsh shell, uid of 1000, and gid of 1000
      community.general.homectl:
        name: tom
        password: myreallysecurepassword1!
        state: present
        shell: /bin/zsh
        uid: 1000
        gid: 1000
        disksize: 10G
      register: tom_userinfo

    - name: Try to add user 'james' that already exists
      community.general.homectl:
        name: james
        password: myreallysecurepassword1!
        state: present
        shell: /bin/ksh
      register: user_exists

    - name: Try to use 'resize=yes' option without 'disksize' option (not allowed)
      community.general.homectl:
        name: foo
        password: uq4895738!@#$%dfd
        state: present
        resize: true
      register: resize_out
      ignore_errors: true

    - name: Use option 'disksize=1G' without option resize (allowed)
      community.general.homectl:
        name: foobar
        password: "uq4895738!@#$%dfd"
        state: present
        disksize: 1G
      register: disk_out
      ignore_errors: true

    - name: Try to Create user without giving password
      community.general.homectl:
        name: danielle
      register: danielle_out
      ignore_errors: true

    - name: remove user 'foobar' without requiring password
      community.general.homectl:
        name: foobar
        state: absent
      register: delete_foobar_out

    - name: modify user 'james' to have zsh shell and timezone 'America/New_York'
      community.general.homectl:
        name: james
        password: myreallysecurepassword1!
        state: present
        shell: /bin/zsh
        timezone: America/New_York
      register: lukuser_modify_out

    - name: create user 'jake' with all mount options
      community.general.homectl:
        name: jake
        password: myreallysecurepassword12!
        mountopts: noexec,nosuid,nodev
        sshkeys: "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDUSW/q2qFZPr2vS0qrmAs+1iQI1jLIBdJ4CVIhE3KnIwxkkiInS8mJ+t0FBTeK3ks3GZLPVYC1v9o2P+oqyUk1CiBnCsMXGJud+L/t8b5r8MiJMyP7Jzd6yhmcvenjvz+vY06jQ9chWAtThEknuaOMongIpQQzSLGbdMy0yMsz4GEjicwdcj1PDwItPvUt4TL4K7V9NE672idADlRt6qng4UwpziqlYgsyIG46ettDras8hGAPricrhFWUS2rLDsCD0thkPFdR8HL1ZWTZ6LtolhO4MYtgntzXn708TTmFC2oIDluzyxVoUYmsfVotVdXFZcOWffnwbCgU+tn75JXTLozgTbV3VWmkxpJFErCWPerxcZv3+7b0f36/Y0gRNjM9HERLDSE1c8yz29NOLY0qH5306aByjOaerxNq9+ZOU/Fmf5/VfGIUp/FdLxDw+V0AzejFG580VAcstEMsOHSdwTbi3gf6LoGSiRyWKKDod0TZCMC6RzfdsfdsfI9CClGl0s= <EMAIL>"
      register: jake_out

    - name: Try to remove user 'janet' that doesn't exist
      community.general.homectl:
        name: janet
        state: absent
      register: user_not_exist
      ignore_errors: true

    - name: Use check_mode to try and create user 'diana'
      community.general.homectl:
        name: diana
        password: helloworld123!@
        state: present
      check_mode: true
      register: diana_create_checkmode_out

    - name: Verify user 'diana' was not created with check_mode
      command: homectl inspect diana
      register: user_diana_exists
      ignore_errors: true

    - name: Try to modify user 'jake' with only noexec mount option in check_mode
      community.general.homectl:
        name: jake
        password: myreallysecurepassword12!
        state: present
        mountopts: noexec
      check_mode: true
      register: jake_checkmode_out

    - name: Verify user 'jake' was not modified and still has all mount options
      command: homectl inspect jake
      register: user_jake_details_out

    - name: Modify user 'jake' with only noexec mount option
      community.general.homectl:
        name: jake
        password: myreallysecurepassword12!
        state: present
        mountopts: noexec
      register: jake_modify_out

    - name: modify user 'jake' again with only noexec mount option to make sure changed is false as nothing has changed.
      community.general.homectl:
        name: jake
        password: myreallysecurepassword12!
        state: present
        mountopts: noexec
      register: jake_modify_again_out

    - name: Try to modify user 'jake' with an incorrect password
      community.general.homectl:
        name: jake
        password: incorrectPassword!
        state: present
        mountopts: noexec
        locked: true
      ignore_errors: true
      register: jake_incorrect_pass_out

    - assert:
        that:
          - james_info.rc == 0
          - tom_userinfo.data['gid'] == 1000 and tom_userinfo.data['uid'] == 1000
          - user_exists is changed and user_exists.data['shell'] == '/bin/ksh'
          - resize_out is not changed
          - disk_out is changed
          - delete_foobar_out is changed
          - danielle_out is not changed
          - lukuser_modify_out.data['timeZone'] == "America/New_York" and lukuser_modify_out.data['shell'] == "/bin/zsh"
          - user_not_exist is not changed and user_not_exist.msg == "User does not exist!"
          - jake_out is changed and jake_out.data['mountNoDevices'] == True and jake_out.data['mountNoSuid'] == True and jake_out.data['mountNoExecute'] == True
          - diana_create_checkmode_out is changed and 'No home for user diana known' in user_diana_exists.stderr
          - "jake_checkmode_out is changed and 'Mount Flags: nosuid nodev noexec' in user_jake_details_out.stdout"
          - jake_modify_out is changed and jake_modify_out.data['privileged']['sshAuthorizedKeys'] is not none
          - jake_modify_out.data['mountNoDevices'] == False and jake_modify_out.data['mountNoExecute'] == True and jake_modify_out.data['mountNoSuid'] == False
          - jake_modify_again_out is not changed
          - jake_incorrect_pass_out is not changed and jake_incorrect_pass_out is failed and jake_incorrect_pass_out.msg == 'User exists but password is incorrect!'

  # homectl was first introduced in systemd 245 so check version  >= 245 and make sure system has systemd and homectl command
  when:
    - systemd_version.rc == 0 and (systemd_version.stdout | regex_search('[0-9][0-9][0-9]') | int >= 245) and homectl_version.rc == 0
    - ansible_distribution != 'Archlinux'  # TODO!
    - ansible_distribution != 'Fedora' or ansible_distribution_major_version|int < 36 # TODO!
