---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

tests:
  - template: default.j2
    group:
      - d: By default, match keys that equal any of the attributes before.
        tt:
          - {before: k0_x0, after: a0}
          - {before: k1_x1, after: a1}
    input:
      - {k0_x0: A0, k1_x1: B0, k2_x2: [C0], k3_x3: foo}
      - {k0_x0: A1, k1_x1: B1, k2_x2: [C1], k3_x3: bar}
    result:
      - {a0: A0, a1: B0, k2_x2: [C0], k3_x3: foo}
      - {a0: A1, a1: B1, k2_x2: [C1], k3_x3: bar}
  - template: mp.j2
    group:
      - d: Replace keys that starts with any of the attributes before.
        mp: starts_with
        tt:
          - {before: k0, after: a0}
          - {before: k1, after: a1}
      - d: Replace keys that ends with any of the attributes before.
        mp: ends_with
        tt:
          - {before: x0, after: a0}
          - {before: x1, after: a1}
      - d: Replace keys that match any regex of the attributes before.
        mp: regex
        tt:
          - {before: "^.*0_x.*$", after: a0}
          - {before: "^.*1_x.*$", after: a1}
    input:
      - {k0_x0: A0, k1_x1: B0, k2_x2: [C0], k3_x3: foo}
      - {k0_x0: A1, k1_x1: B1, k2_x2: [C1], k3_x3: bar}
    result:
      - {a0: A0, a1: B0, k2_x2: [C0], k3_x3: foo}
      - {a0: A1, a1: B1, k2_x2: [C1], k3_x3: bar}
  - template: mp.j2
    group:
      - d: If more keys match the same attribute before the last one will be used.
        mp: regex
        tt:
          - {before: "^.*_x.*$", after: X}
      - d: If there are items with equal attribute before the first one will be used.
        mp: regex
        tt:
          - {before: "^.*_x.*$", after: X}
          - {before: "^.*_x.*$", after: Y}
    input:
      - {k0_x0: A0, k1_x1: B0, k2_x2: [C0], k3_x3: foo}
      - {k0_x0: A1, k1_x1: B1, k2_x2: [C1], k3_x3: bar}
    result:
      - X: foo
      - X: bar
  - template: mp.j2
    group:
      - d: If there are more matches for a key the first one will be used.
        mp: starts_with
        tt:
          - {before: a, after: X}
          - {before: aa, after: Y}
    input:
      - {aaa1: A, bbb1: B, ccc1: C}
      - {aaa2: D, bbb2: E, ccc2: F}
    result:
      - {X: A, bbb1: B, ccc1: C}
      - {X: D, bbb2: E, ccc2: F}
