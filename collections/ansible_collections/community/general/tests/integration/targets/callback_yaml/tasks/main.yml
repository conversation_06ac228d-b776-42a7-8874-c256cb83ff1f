---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Write vault password to disk
  ansible.builtin.copy:
    dest: "{{ remote_tmp_dir }}/vault-password"
    content: asdf

- name: Run tests
  include_role:
    name: callback
  vars:
    tests:
      - name: Basic run
        environment:
          ANSIBLE_NOCOLOR: 'true'
          ANSIBLE_FORCE_COLOR: 'false'
          ANSIBLE_STDOUT_CALLBACK: community.general.yaml
        playbook: |
          - hosts: testhost
            gather_facts: false
            tasks:
              - name: Sample task name
                debug:
                  msg: sample debug msg
        expected_output:
          - ""
          - "PLAY [testhost] ****************************************************************"
          - ""
          - "TASK [Sample task name] ********************************************************"
          - "ok: [testhost] => "
          - "  msg: sample debug msg"
          - ""
          - "PLAY RECAP *********************************************************************"
          - "testhost                   : ok=1    changed=0    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   "

      - name: Test umlauts in multiline
        environment:
          ANSIBLE_NOCOLOR: 'true'
          ANSIBLE_FORCE_COLOR: 'false'
          ANSIBLE_STDOUT_CALLBACK: community.general.yaml
        playbook: |
          - hosts: testhost
            gather_facts: false
            tasks:
              - name: Umlaut output
                debug:
                  msg: "äöü\néêè\nßï☺"
        expected_output:
          - ""
          - "PLAY [testhost] ****************************************************************"
          - ""
          - "TASK [Umlaut output] ***********************************************************"
          - "ok: [testhost] => "
          - "  msg: |-"
          - "    äöü"
          - "    éêè"
          - "    ßï☺"
          - ""
          - "PLAY RECAP *********************************************************************"
          - "testhost                   : ok=1    changed=0    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   "

      - name: Test to_yaml
        environment:
          ANSIBLE_NOCOLOR: 'true'
          ANSIBLE_FORCE_COLOR: 'false'
          ANSIBLE_STDOUT_CALLBACK: community.general.yaml
        playbook: !unsafe |
          - hosts: testhost
            gather_facts: false
            vars:
              data: |
                line 1
                line 2
                line 3
            tasks:
              - name: Test to_yaml
                debug:
                  msg: "{{ data | to_yaml }}"
        expected_output:
          - ""
          - "PLAY [testhost] ****************************************************************"
          - ""
          - "TASK [Test to_yaml] ************************************************************"
          - "ok: [testhost] => "
          - "  msg: |-"
          - "    'line 1"
          - "  "
          - "      line 2"
          - "  "
          - "      line 3"
          - "  "
          - "      '"
          - ""
          - "PLAY RECAP *********************************************************************"
          - "testhost                   : ok=1    changed=0    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   "
      - name: Some more fun with data tagging
        environment:
          ANSIBLE_NOCOLOR: 'true'
          ANSIBLE_FORCE_COLOR: 'false'
          ANSIBLE_STDOUT_CALLBACK: community.general.yaml
        extra_cli_arguments: "--vault-password-file {{ remote_tmp_dir }}/vault-password"
        playbook: !unsafe |
          - hosts: testhost
            gather_facts: false
            vars:
              foo: bar
              baz: !vault |
                $ANSIBLE_VAULT;1.1;AES256
                30393064316433636636373336363538663034643135363938646665393661353833633865313765
                3835366434646339313337663335393865336163663434310a316161313662666466333332353731
                64663064366461643162666137303737643164376134303034306366383830336232363837636638
                3830653338626130360a313639623231353931356563313065373661303262646337383534663932
                64353461663065333362346264326335373032313333343539646661656634653138646332313639
                3566313765626464613734623664663266336237646139373935
            tasks:
              - name: Test regular string
                debug:
                  var: foo
              - name: Test vaulted string
                debug:
                  var: baz
        expected_output:
          - ""
          - "PLAY [testhost] ****************************************************************"
          - ""
          - "TASK [Test regular string] *****************************************************"
          - "ok: [testhost] => "
          - "  foo: bar"
          - ""
          - "TASK [Test vaulted string] *****************************************************"
          - "ok: [testhost] => "
          - "  baz: aBcDeFgHiJkLmNoPqRsTuVwXyZ012345"
          - ""
          - "PLAY RECAP *********************************************************************"
          - "testhost                   : ok=2    changed=0    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   "
