---
# test code for the etcd3 module
# Copyright (c) 2017,  <PERSON><PERSON><PERSON> <jean-philip<PERSON>@evrard.me>
# Copyright 2020, SCC France, <PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# ============================================================

# Integration tests
- name: Check mode, show need change
  etcd3:
    key: "foo"
    value: "bar"
    state: "present"
  register: _etcd3_prst_chktst
  check_mode: true

- name: Change to new value
  etcd3:
    key: "foo"
    value: "bar"
    state: "present"
  register: _etcd3_prst_chgtst

- name: Idempotency test, show unchanged.
  etcd3:
    key: "foo"
    value: "bar"
    state: "present"
  register: _etcd3_prst_idmptnttst

- name: Idempotency test in check mode, show unchanged
  etcd3:
    key: "foo"
    value: "bar"
    state: "present"
  register: _etcd3_prst_idmptntchktst
  check_mode: true

- name: Check mode, show need removal of key
  etcd3:
    key: "foo"
    value: "baz"
    state: "absent"
  register: _etcd3_absnt_chktst
  check_mode: true

- name: Remove foo key
  etcd3:
    key: "foo"
    value: "baz"
    state: "absent"
  register: _etcd3_absnt_chgtst

- name: Idempotency test in check mode, show unchanged
  etcd3:
    key: "foo"
    value: "baz"
    state: "absent"
  register: _etcd3_absnt_idmptnttst
  check_mode: true

- name: Idempotency test, show unchanged
  etcd3:
    key: "foo"
    value: "baz"
    state: "absent"
  register: _etcd3_absnt_idmptntchktst

- name: Checking the status are expected
  assert:
    that:
      - _etcd3_prst_chktst is changed
      - _etcd3_prst_chgtst is changed
      - _etcd3_prst_idmptnttst is not changed
      - _etcd3_prst_idmptntchktst is not changed
      - _etcd3_absnt_chktst is changed
      - _etcd3_absnt_chgtst is changed
      - _etcd3_absnt_idmptnttst is not changed
      - _etcd3_absnt_idmptntchktst is not changed
