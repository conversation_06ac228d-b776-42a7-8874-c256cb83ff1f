---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# test code for the etcd3 module
# Copyright (c) 2017,  <PERSON><PERSON><PERSON> Evrard <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# ============================================================


- name: run_tests for supported distros
  include_tasks: run_tests.yml
  when:
    - ansible_distribution | lower ~ "-" ~ ansible_distribution_major_version | lower != 'centos-6'
