---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Create a password ({{ backend }})
  set_fact:
    newpass: "{{ lookup('community.general.passwordstore', 'test-pass', length=8, create=true, backend=backend) }}"

- name: Fetch password from an existing file ({{ backend }})
  set_fact:
    readpass: "{{ lookup('community.general.passwordstore', 'test-pass', backend=backend) }}"

- name: Verify password ({{ backend }})
  assert:
    that:
      - readpass == newpass

- name: Create a password with equal sign ({{ backend }})
  set_fact:
    newpass: "{{ lookup('community.general.passwordstore', 'test-pass-equal userpass=SimpleSample= create=true', backend=backend) }}"

- name: Fetch a password with equal sign ({{ backend }})
  set_fact:
    readpass: "{{ lookup('community.general.passwordstore', 'test-pass-equal', backend=backend) }}"

- name: Verify password ({{ backend }})
  assert:
    that:
      - readpass == newpass

- name: Create a password using missing=create ({{ backend }})
  set_fact:
    newpass: "{{ lookup('community.general.passwordstore', 'test-missing-create', missing='create', length=8, backend=backend) }}"

- name: Fetch password from an existing file ({{ backend }})
  set_fact:
    readpass: "{{ lookup('community.general.passwordstore', 'test-missing-create', backend=backend) }}"

- name: Verify password ({{ backend }})
  assert:
    that:
      - readpass == newpass

- name: Fetch password from existing file using missing=empty ({{ backend }})
  set_fact:
    readpass: "{{ lookup('community.general.passwordstore', 'test-missing-create', missing='empty', backend=backend) }}"

- name: Verify password ({{ backend }})
  assert:
    that:
      - readpass == newpass

- name: Fetch password from non-existing file using missing=empty ({{ backend }})
  set_fact:
    readpass: "{{ query('community.general.passwordstore', 'test-missing-pass', missing='empty', backend=backend) }}"

- name: Verify password ({{ backend }})
  assert:
    that:
      - readpass == [ none ]

- name: Create the YAML password ({{ backend }})
  command: "{{ backend }} insert -m -f test-yaml-pass"
  args:
    stdin: |
      testpassword
      key: |
        multi
        line

- name: Fetch a password with YAML subkey ({{ backend }})
  set_fact:
    readyamlpass: "{{ lookup('community.general.passwordstore', 'test-yaml-pass', subkey='key', backend=backend) }}"

- name: Read a yaml subkey ({{ backend }})
  assert:
    that:
      - readyamlpass == 'multi\nline\n'

- name: Create a non-YAML multiline file ({{ backend }})
  command: "{{ backend }} insert -m -f test-multiline-pass"
  args:
    stdin: |
      testpassword
      random additional line

- name: Fetch password from multiline file ({{ backend }})
  set_fact:
    readyamlpass: "{{ lookup('community.general.passwordstore', 'test-multiline-pass', backend=backend) }}"

- name: Multiline pass only returns first line ({{ backend }})
  assert:
    that:
      - readyamlpass == 'testpassword'

- name: Fetch all from multiline file ({{ backend }})
  set_fact:
    readyamlpass: "{{ lookup('community.general.passwordstore', 'test-multiline-pass', returnall='yes', backend=backend) }}"

- name: Multiline pass returnall returns everything in the file ({{ backend }})
  assert:
    that:
      - readyamlpass == 'testpassword\nrandom additional line\n'

- name: Create a password in a folder ({{ backend }})
  set_fact:
    newpass: "{{ lookup('community.general.passwordstore', 'folder/test-pass', length=8, create=true, backend=backend) }}"

- name: Fetch password from folder ({{ backend }})
  set_fact:
    readpass: "{{ lookup('community.general.passwordstore', 'folder/test-pass', backend=backend) }}"

- name: Verify password from folder ({{ backend }})
  assert:
    that:
      - readpass == newpass

- name: Try to read folder as passname ({{ backend }})
  set_fact:
    newpass: "{{ lookup('community.general.passwordstore', 'folder', backend=backend) }}"
  ignore_errors: true
  register: eval_error

- name: Make sure reading folder as passname failed ({{ backend }})
  assert:
    that:
      - eval_error is failed
      - '"passname folder not found" in eval_error.msg'
  when: backend != "gopass"  # Remove this line once gopass backend can handle this
