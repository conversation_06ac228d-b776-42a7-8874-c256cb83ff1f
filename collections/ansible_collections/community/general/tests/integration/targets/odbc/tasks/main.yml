---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- when:
    - ansible_os_family != 'Archlinux'  # TODO install driver from AUR: https://aur.archlinux.org/packages/psqlodbc
    - ansible_os_family != 'RedHat' or ansible_distribution_major_version != '7'  # CentOS 7 stopped working
  block:

    #
    # Test for proper failures without pyodbc
    #
    # Some of the docker images already have pyodbc installed on it
    - include_tasks: no_pyodbc.yml
      when: ansible_os_family != 'FreeBSD' and ansible_os_family != 'Suse' and ansible_os_family != 'Debian'

    #
    # Get pyodbc installed
    #
    - include_tasks: install_pyodbc.yml

    #
    # Test missing parameters & invalid DSN
    #
    - include_tasks: negative_tests.yml

    #
    # Setup DSN per env
    #
    - name: Changing DSN for Suse
      set_fact:
        dsn: "DRIVER={PSQL};Server=localhost;Port=5432;Database=postgres;Uid={{ my_user }};Pwd={{ my_pass_decrypted }};UseUnicode=True"
      when: ansible_os_family == 'Suse' or ansible_os_family == 'Alpine'

    - name: Changing DSN for Alpine
      set_fact:
        dsn: "DRIVER={/usr/lib/psqlodbcw.so};Server=localhost;Port=5432;Database=postgres;Uid={{ my_user }};Pwd={{ my_pass_decrypted }};UseUnicode=True"
      when: ansible_os_family == 'Alpine'

    - name: Changing DSN for Debian
      set_fact:
        dsn: "DRIVER={PostgreSQL Unicode};Server=localhost;Port=5432;Database=postgres;Uid={{ my_user }};Pwd={{ my_pass_decrypted }};UseUnicode=True"
      when: ansible_os_family == 'Debian'

    #
    # Name setup database
    #
    - name: Create a user to run the tests with
      shell: echo "CREATE USER {{ my_user }} SUPERUSER PASSWORD '{{ my_pass }}'" | psql postgres
      become_user: "{{ pg_user }}"
      become: true

    - name: Create a table
      odbc:
        dsn: "{{ dsn }}"
        query: |
            CREATE TABLE films (
                code        char(5) CONSTRAINT firstkey PRIMARY KEY,
                title       varchar(40) NOT NULL,
                did         integer NOT NULL,
                date_prod   date,
                kind        varchar(10),
                len         interval hour to minute
            );
      become_user: "{{ pg_user }}"
      become: true
      register: results

    - assert:
        that:
          - results is changed

    #
    # Insert records
    #
    - name: Insert a record without params
      odbc:
        dsn: "{{ dsn }}"
        query: "INSERT INTO films (code, title, did, date_prod, kind, len) VALUES ('asdfg', 'My First Movie', 1, '2019-01-12', 'SyFi', '02:00')"
      become_user: "{{ pg_user }}"
      become: true
      register: results

    - assert:
        that:
          - results is changed

    - name: Insert a record with params
      odbc:
        dsn: "{{ dsn }}"
        query: "INSERT INTO films (code, title, did, date_prod, kind, len) VALUES (?, ?, ?, ?, ?, ?)"
        params:
          - 'qwert'
          - 'My Second Movie'
          - 2
          - '2019-01-12'
          - 'Comedy'
          - '01:30'
      become_user: "{{ pg_user }}"
      become: true
      register: results

    - assert:
        that:
          - results is changed
          - results['row_count'] == -1
          - results['results'] == []
          - results['description'] == []

    #
    # Select data
    #
    - name: Perform select single row without params (do not coherse changed)
      odbc:
        dsn: "{{ dsn }}"
        query: "SELECT * FROM films WHERE code='asdfg'"
      register: results

    - assert:
        that:
          - results is changed
          - results is successful
          - results.row_count == 1

    - name: Perform select multiple rows with params (coherse changed)
      odbc:
        dsn: "{{ dsn }}"
        query: 'SELECT * FROM films WHERE code=? or code=?'
        params:
          - 'asdfg'
          - 'qwert'
      register: results
      changed_when: false

    - assert:
        that:
          - results is not changed
          - results is successful
          - results.row_count == 2

    - name: Drop the table
      odbc:
        dsn: "{{ dsn }}"
        query: "DROP TABLE films"
      register: results

    - assert:
        that:
          - results is successful
          - results is changed
          - results['row_count'] == -1
          - results['results'] == []
          - results['description'] == []
