---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# defaults file for test_postgresql_db
my_user: 'ansible_user'
my_pass: 'md5d5e044ccd9b4b8adc89e8fed2eb0db8a'
my_pass_decrypted: '6EjMk<hcX3<5(Yp?Xi5aQ8eS`a#Ni'
dsn: "DRIVER={PostgreSQL};Server=localhost;Port=5432;Database=postgres;Uid={{ my_user }};Pwd={{ my_pass_decrypted }};UseUnicode=True"
packages:
  Alpine:
    - psqlodbc
    - unixodbc
    - unixodbc-dev
    - g++
  Archlinux:
    - unixodbc
  RedHat:
    - postgresql-odbc
    - unixODBC
    - unixODBC-devel
    - gcc
    - gcc-c++
  Debian:
    - odbc-postgresql
    - unixodbc
    - unixodbc-dev
    - gcc
    - g++
  Suse:
    - psqlODBC
    - unixODBC
    - unixODBC-devel
    - gcc
    - gcc-c++
  FreeBSD:
    - unixODBC
