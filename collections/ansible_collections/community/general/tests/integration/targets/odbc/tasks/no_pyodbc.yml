---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Testing the module without pyodbc
  odbc:
    dsn: "Test"
    query: "SELECT * FROM nothing"
  ignore_errors: true
  register: results

- assert:
    that:
      - results is failed
      - "'Failed to import the required Python library (pyodbc) on' in results.msg"
