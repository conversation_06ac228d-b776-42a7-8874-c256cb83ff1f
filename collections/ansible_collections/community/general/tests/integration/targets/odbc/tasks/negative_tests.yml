---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

#
# Missing params for the module
# There is nothing you need to do here because the params are required
#

#
# Invalid DSN in the module
#
- name: "Test with an invalid DSN"
  odbc:
    dsn: "t1"
    query: "SELECT * FROM nothing"
  register: results
  ignore_errors: true

- assert:
    that:
      - results is failed
      - "'Failed to connect to DSN' in results.msg"
