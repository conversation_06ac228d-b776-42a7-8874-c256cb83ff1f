---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name:
  set_fact:
    interfaces_testfile: '{{ remote_tmp_dir }}/interfaces'
    interfaces_testfile_3841: '{{ remote_tmp_dir }}/interfaces_3841'
    interfaces_testfile_7610: '{{ remote_tmp_dir }}/interfaces_7610'

- name: Copy interfaces file
  copy:
    src: 'files/interfaces_ff'
    dest: '{{ interfaces_testfile }}'

- name: Change IP address to *******
  community.general.interfaces_file:
    dest: "{{ interfaces_testfile }}"
    iface: eno1
    option: address
    value: *******
  register: ifile_1

- assert:
    that:
      - ifile_1 is changed

- name: Change IP address to ******* again
  community.general.interfaces_file:
    dest: "{{ interfaces_testfile }}"
    iface: eno1
    option: address
    value: *******
  register: ifile_2

- assert:
    that:
      - ifile_2 is not changed

- name: 3841 - copy interfaces file
  copy:
    src: 'files/interfaces_ff_3841'
    dest: '{{ interfaces_testfile_3841 }}'

- name: 3841 - floating_ip_interface_up_ip 2a01:a:b:c::1/64 dev eth0
  interfaces_file:
    option: up
    iface: eth0
    dest: "{{ interfaces_testfile_3841 }}"
    value: 'ip addr add 2a01:a:b:c::1/64 dev eth0'
    state: present
  register: ifile_3841_a

- name: 3841 - floating_ip_interface_up_ip 2a01:a:b:c::1/64 dev eth0 (again)
  interfaces_file:
    option: up
    iface: eth0
    dest: "{{ interfaces_testfile_3841 }}"
    value: 'ip addr add 2a01:a:b:c::1/64 dev eth0'
    state: present
  register: ifile_3841_b

- name: 3841 - check assertions
  assert:
    that:
      - ifile_3841_a is changed
      - ifile_3841_b is not changed

- name: 7610 - create file
  copy:
    dest: '{{ interfaces_testfile_7610 }}'
    content: |
      iface ens3 inet dhcp
      iface ens3 inet6 auto

- name: 7610 - modify file
  interfaces_file:
    dest: '{{ interfaces_testfile_7610 }}'
    iface: ens3
    address_family: "inet6"
    option: "{{ item.option }}"
    value: "{{ item.value }}"
  loop:
    - option: "method"
      value: "static"
    - option: "address"
      value: "1:2::3/48"

- name: 7610 - read file
  slurp:
    src: '{{ interfaces_testfile_7610 }}'
  register: content_7610

- name: 7610 - check assertions
  assert:
    that:
      - content_7610.content | b64decode == expected_content
  vars:
    expected_content: |
      iface ens3 inet dhcp
      iface ens3 inet6 static
          address 1:2::3/48

- name: 7610 - modify file again
  interfaces_file:
    dest: '{{ interfaces_testfile_7610 }}'
    iface: ens3
    option: method
    value: foobar

- name: 7610 - read file
  slurp:
    src: '{{ interfaces_testfile_7610 }}'
  register: content_7610

- name: 7610 - check assertions
  assert:
    that:
      - content_7610.content | b64decode == expected_content
  vars:
    expected_content: |
      iface ens3 inet foobar
      iface ens3 inet6 foobar
          address 1:2::3/48
