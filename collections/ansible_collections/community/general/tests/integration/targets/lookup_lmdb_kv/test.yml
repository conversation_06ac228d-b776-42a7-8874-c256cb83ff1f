---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- hosts: localhost
  tasks:
    - debug:
        msg: '{{ query("community.general.lmdb_kv", "nl", "be", "lu", db="jp.mdb") }}'
    - debug:
        var: item.1
      loop: '{{ query("community.general.lmdb_kv", db="jp.mdb") }}'
    - assert:
        that:
          - query('community.general.lmdb_kv', 'nl', 'be', 'lu', db='jp.mdb') == ['Netherlands', 'Belgium', 'Luxembourg']
          - query('community.general.lmdb_kv', db='jp.mdb')|length == 5
    - assert:
        that:
          - item.0 == 'nl'
          - item.1 == 'Netherlands'
      vars:
        lmdb_kv_db: jp.mdb
      with_community.general.lmdb_kv:
        - n*
    - assert:
        that:
          - item == 'Belgium'
      vars:
        lmdb_kv_db: jp.mdb
      with_community.general.lmdb_kv:
        - be
