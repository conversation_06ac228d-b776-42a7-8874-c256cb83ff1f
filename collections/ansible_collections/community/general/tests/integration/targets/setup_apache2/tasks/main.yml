---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Install apache via apt
  apt:
    name: "{{item}}"
    state: present
  when: "ansible_os_family == 'Debian'"
  with_items:
    - apache2
    - libapache2-mod-evasive

- name: Install apache via zypper
  community.general.zypper:
    name: apache2
    state: present
  when: "ansible_os_family == 'Suse'"

- name: Enable mod_slotmem_shm on SuSE
  apache2_module:
    name: slotmem_shm
    state: present
  when: "ansible_os_family == 'Suse'"
