---
# Copyright (c) 2025, <PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Test adding a single disk vdev
  block:
    - name: Ensure a single disk pool exists
      community.general.zpool:
        name: "{{ zpool_generic_pool_name }}"
        vdevs:
          - disks: "{{ zpool_vdevs_disk_config.vdev1 }}"
        state: present

    - name: Add a single disk vdev
      community.general.zpool:
        name: "{{ zpool_generic_pool_name }}"
        vdevs:
          - disks: "{{ zpool_vdevs_disk_config.vdev1 }}"
          - disks: "{{ zpool_vdevs_disk_config.vdev2 }}"
        state: present

    - name: Check if vdev was added
      ansible.builtin.command:
        cmd: "zpool status -P -L {{ zpool_generic_pool_name }}"
      register: single_disk_pool_check
      changed_when: false

    - name: Asser<PERSON> that added disk is present
      ansible.builtin.assert:
        that:
          - "zpool_vdevs_disk_config.vdev2[0] in single_disk_pool_check.stdout"

    - name: Ensure the single disk pool is absent
      community.general.zpool:
        name: "{{ zpool_generic_pool_name }}"
        state: absent

- name: Test adding a mirror vdev
  block:
    - name: Ensure a single disk pool exists
      community.general.zpool:
        name: "{{ zpool_generic_pool_name }}"
        vdevs:
          - disks: "{{ zpool_vdevs_disk_config.vdev1 }}"
        state: present

    - name: Add a mirror vdev
      community.general.zpool:
        name: "{{ zpool_generic_pool_name }}"
        force: true # This is necessary because of the mismatched replication level
        vdevs:
          - disks: "{{ zpool_vdevs_disk_config.vdev1 }}"
          - type: mirror
            disks: "{{ zpool_vdevs_disk_config.vdev3 }}"
        state: present

    - name: Check if vdev was added
      ansible.builtin.command:
        cmd: "zpool status -P -L {{ zpool_generic_pool_name }}"
      register: mirror_pool_check
      changed_when: false

    - name: Assert that added vdev is present
      ansible.builtin.assert:
        that:
          - "zpool_vdevs_disk_config.vdev3[0] in mirror_pool_check.stdout"
          - "zpool_vdevs_disk_config.vdev3[1] in mirror_pool_check.stdout"

    - name: Ensure the single disk pool is absent
      community.general.zpool:
        name: "{{ zpool_generic_pool_name }}"
        state: absent

- name: Test adding a raidz vdev
  block:
    - name: Ensure a single disk pool exists
      community.general.zpool:
        name: "{{ zpool_generic_pool_name }}"
        vdevs:
          - disks: "{{ zpool_vdevs_disk_config.vdev1 }}"
        state: present

    - name: Add a raidz vdev
      community.general.zpool:
        name: "{{ zpool_generic_pool_name }}"
        force: true # This is necessary because of the mismatched replication level
        vdevs:
          - disks: "{{ zpool_vdevs_disk_config.vdev1 }}"
          - type: raidz
            disks: "{{ zpool_vdevs_disk_config.vdev3 }}"
        state: present

    - name: Check if vdev was added
      ansible.builtin.command:
        cmd: "zpool status -P -L {{ zpool_generic_pool_name }}"
      register: raidz_pool_check
      changed_when: false

    - name: Assert that added vdev is present
      ansible.builtin.assert:
        that:
          - "zpool_vdevs_disk_config.vdev3[0] in raidz_pool_check.stdout"
          - "zpool_vdevs_disk_config.vdev3[1] in raidz_pool_check.stdout"

    - name: Ensure the single disk pool is absent
      community.general.zpool:
        name: "{{ zpool_generic_pool_name }}"
        state: absent

- name: Test removing an existing vdev
  block:
    - name: Ensure a pool with two mirrored vdevs exists
      community.general.zpool:
        name: "{{ zpool_generic_pool_name }}"
        vdevs:
          - type: mirror
            disks: "{{ zpool_vdevs_disk_config.vdev3 }}"
          - type: mirror
            disks: "{{ zpool_vdevs_disk_config.vdev4 }}"
        state: present

    - name: Remove a vdev
      community.general.zpool:
        name: "{{ zpool_generic_pool_name }}"
        vdevs:
          - type: mirror
            disks: "{{ zpool_vdevs_disk_config.vdev4 }}"
        state: present

    - name: Check if vdev was removed
      ansible.builtin.command:
        cmd: "zpool status -P -L {{ zpool_generic_pool_name }}"
      register: remove_vdev_check
      changed_when: false

    - name: Assert that removed vdev is absent
      ansible.builtin.assert:
        that:
          - "zpool_vdevs_disk_config.vdev3[0] not in remove_vdev_check.stdout"
          - "zpool_vdevs_disk_config.vdev3[1] not in remove_vdev_check.stdout"
          - "'Removal of vdev' in remove_vdev_check.stdout"

    - name: Ensure the pool is absent
      community.general.zpool:
        name: "{{ zpool_generic_pool_name }}"
        state: absent
