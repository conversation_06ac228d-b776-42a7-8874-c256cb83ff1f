---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: make sure we have the ansible_os_family and ansible_distribution_version facts
  setup:
    gather_subset: distribution
  when: ansible_facts == {}

- include_tasks: "{{ lookup('first_found', files)}}"
  vars:
    files:
      - "{{ ansible_os_family | lower }}.yml"
      - "default.yml"
