---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: create ~/tmp
  file:
    path: '~/tmp'
    state: directory

- name: create temporary directory
  tempfile:
    state: directory
    suffix: .test
    path: ~/tmp
  register: remote_tmp_dir
  notify:
    - delete temporary directory

- name: record temporary directory
  set_fact:
    remote_tmp_dir: "{{ remote_tmp_dir.path }}"
