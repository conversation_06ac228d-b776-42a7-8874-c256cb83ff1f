---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Create realm
  community.general.keycloak_realm: "{{ auth_args | combine(call_args) }}"
  vars:
    call_args:
      id: "{{ realm }}"
      realm: "{{ realm }}"
      state: present

- name: Keycloak Client
  community.general.keycloak_client: "{{ auth_args | combine(call_args) }}"
  vars:
    call_args:
      realm: "{{ realm }}"
      client_id: "{{ client_id }}"
      state: present
  register: client

- name: Keycloak Client fetch clientsecret by client_id
  community.general.keycloak_clientsecret_info: "{{ auth_args | combine(call_args) }}"
  vars:
    call_args:
      realm: "{{ realm }}"
      client_id: "{{ client_id }}"
  register: fetch_by_client_id_result

- name: Assert that the client secret was retrieved
  assert:
    that:
      - fetch_by_client_id_result.clientsecret_info.type == "secret"
      - fetch_by_client_id_result.clientsecret_info.value | length >= 32

- name: Keycloak Client fetch clientsecret by id
  community.general.keycloak_clientsecret_info: "{{ auth_args | combine(call_args) }}"
  vars:
    call_args:
      realm: "{{ realm }}"
      id: "{{ client.end_state.id }}"
  register: fetch_by_id_result

- name: Assert that the same client secret was retrieved both times
  assert:
    that:
      - fetch_by_id_result.clientsecret_info.value == fetch_by_client_id_result.clientsecret_info.value
