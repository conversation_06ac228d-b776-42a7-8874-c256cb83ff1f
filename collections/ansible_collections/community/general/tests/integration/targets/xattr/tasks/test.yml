---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Set attributes
  xattr:
    path: "{{ test_file }}"
    key: user.foo
    value: bar
  register: xattr_set_result

- name: Get attributes
  xattr:
    path: "{{ test_file }}"
  register: xattr_get_all_result

- name: Get specific attribute
  xattr:
    path: "{{ test_file }}"
    key: foo
  register: xattr_get_specific_result

- assert:
    that:
      - "xattr_set_result.changed"
      - "xattr_get_all_result['xattr']['user.foo'] == 'bar'"
      - "not xattr_get_all_result.changed"
      - "xattr_get_specific_result['xattr']['user.foo'] == 'bar'"
      - "not xattr_get_specific_result.changed"

- name: Set attribute again
  xattr:
    path: "{{ test_file }}"
    namespace: user
    key: foo
    value: bar
  register: xattr_set_again_result

- assert:
    that:
      - "not xattr_set_again_result.changed"

- name: Unset attribute
  xattr:
    path: "{{ test_file }}"
    key: foo
    state: absent
  register: xattr_unset_result

- name: Get attributes
  xattr:
    path: "{{ test_file }}"
  register: xattr_get_after_unset_result

- assert:
    that:
      - "xattr_unset_result.changed"
      - "xattr_get_after_unset_result['xattr'] == {}"
      - "not xattr_get_after_unset_result.changed"

- name: Unset attribute again
  xattr:
    path: "{{ test_file }}"
    namespace: user
    key: foo
    state: absent
  register: xattr_unset_result

- assert:
    that:
      - "not xattr_set_again_result.changed"
