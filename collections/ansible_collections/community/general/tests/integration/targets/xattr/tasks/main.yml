---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Setup
  include_tasks: setup.yml

- name: Check availability of xattr support
  command: setfattr -n user.foo {{ test_file }}
  ignore_errors: true
  register: xattr

- name: Test
  include_tasks: test.yml
  when: xattr is not failed
