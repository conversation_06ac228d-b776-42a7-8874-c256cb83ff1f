---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: delete a smn topic
  hwc_smn_topic:
    identity_endpoint: "{{ identity_endpoint }}"
    user: "{{ user }}"
    password: "{{ password }}"
    domain: "{{ domain }}"
    project: "{{ project }}"
    region: "{{ region }}"
    name: "ansible_smn_topic_test"
    state: absent
# ----------------------------------------------------------
- name: create a smn topic
  hwc_smn_topic:
    identity_endpoint: "{{ identity_endpoint }}"
    user: "{{ user }}"
    password: "{{ password }}"
    domain: "{{ domain }}"
    project: "{{ project }}"
    region: "{{ region }}"
    name: "ansible_smn_topic_test"
    state: present
  register: result
- name: assert changed is true
  assert:
    that:
      - result is changed
# ----------------------------------------------------------------------------
- name: create a smn topic that already exists
  hwc_smn_topic:
    identity_endpoint: "{{ identity_endpoint }}"
    user: "{{ user }}"
    password: "{{ password }}"
    domain: "{{ domain }}"
    project: "{{ project }}"
    region: "{{ region }}"
    name: "ansible_smn_topic_test"
    state: present
  register: result
- name: assert changed is false
  assert:
    that:
      - result is not failed
      - result is not changed
# ----------------------------------------------------------
- name: delete a smn topic
  hwc_smn_topic:
    identity_endpoint: "{{ identity_endpoint }}"
    user: "{{ user }}"
    password: "{{ password }}"
    domain: "{{ domain }}"
    project: "{{ project }}"
    region: "{{ region }}"
    name: "ansible_smn_topic_test"
    state: absent
  register: result
- name: assert changed is true
  assert:
    that:
      - result is changed
# ----------------------------------------------------------------------------
- name: delete a smn topic that does not exist
  hwc_smn_topic:
    identity_endpoint: "{{ identity_endpoint }}"
    user: "{{ user }}"
    password: "{{ password }}"
    domain: "{{ domain }}"
    project: "{{ project }}"
    region: "{{ region }}"
    name: "ansible_smn_topic_test"
    state: absent
  register: result
- name: assert changed is false
  assert:
    that:
      - result is not failed
      - result is not changed
