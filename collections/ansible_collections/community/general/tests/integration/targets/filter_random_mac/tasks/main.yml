####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# test code for filters
# Copyright (c) 2014, <PERSON> <micha<PERSON>.<EMAIL>>
# Copyright (c) 2019, Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Test random_mac filter bad argument type
  debug:
    var: "0 | community.general.random_mac"
  register: _bad_random_mac_filter
  ignore_errors: true

- name: Verify random_mac filter showed a bad argument type error message
  assert:
    that:
      - _bad_random_mac_filter is failed
      - "_bad_random_mac_filter.msg is search('Invalid value type (.*int.*) for random_mac .*')"

- name: Test random_mac filter bad argument value
  debug:
    var: "'dummy' | community.general.random_mac"
  register: _bad_random_mac_filter
  ignore_errors: true

- name: Verify random_mac filter showed a bad argument value error message
  assert:
    that:
      - _bad_random_mac_filter is failed
      - "_bad_random_mac_filter.msg is search('Invalid value (.*) for random_mac: .* not hexa byte')"

- name: Test random_mac filter prefix too big
  debug:
    var: "'00:00:00:00:00:00' | community.general.random_mac"
  register: _bad_random_mac_filter
  ignore_errors: true

- name: Verify random_mac filter showed a prefix too big error message
  assert:
    that:
      - _bad_random_mac_filter is failed
      - "_bad_random_mac_filter.msg is search('Invalid value (.*) for random_mac: 5 colon.* separated items max')"

- name: Verify random_mac filter
  assert:
    that:
      - "'00' | community.general.random_mac is match('^00:[a-f0-9][a-f0-9]:[a-f0-9][a-f0-9]:[a-f0-9][a-f0-9]:[a-f0-9][a-f0-9]:[a-f0-9][a-f0-9]$')"
      - "'00:00' | community.general.random_mac is match('^00:00:[a-f0-9][a-f0-9]:[a-f0-9][a-f0-9]:[a-f0-9][a-f0-9]:[a-f0-9][a-f0-9]$')"
      - "'00:00:00' | community.general.random_mac is match('^00:00:00:[a-f0-9][a-f0-9]:[a-f0-9][a-f0-9]:[a-f0-9][a-f0-9]$')"
      - "'00:00:00:00' | community.general.random_mac is match('^00:00:00:[a-f0-9][a-f0-9]:[a-f0-9][a-f0-9]:[a-f0-9][a-f0-9]$')"
      - "'00:00:00:00:00' | community.general.random_mac is match('^00:00:00:00:00:[a-f0-9][a-f0-9]$')"
      - "'00:00:00' | community.general.random_mac != '00:00:00' | community.general.random_mac"

- name: Verify random_mac filter with seed
  assert:
    that:
      - "'00:00:00' | community.general.random_mac(seed='test') == '00:00:00' | community.general.random_mac(seed='test')"
      - "'00:00:00' | community.general.random_mac(seed='test') != '00:00:00' | community.general.random_mac(seed='another_test')"
