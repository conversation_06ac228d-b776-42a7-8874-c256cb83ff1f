---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# TODO: Our current implementation does not handle SMTP authentication

- when:
    # TODO: https://github.com/ansible-collections/community.general/issues/4656
    - ansible_python.version.major != 3 or ansible_python.version.minor < 12
  block:

    # NOTE: If the system does not support smtpd-tls (python 2.6 and older) we do basic tests
    - name: Attempt to install smtpd-tls
      pip:
        name: smtpd-tls
        state: present
      ignore_errors: true
      register: smtpd_tls

    - name: Install test smtpserver
      copy:
        src: '{{ item }}'
        dest: '{{ remote_tmp_dir }}/{{ item }}'
      loop:
        - smtpserver.py
        - smtpserver.crt
        - smtpserver.key

    # FIXME: Verify the mail after it was send would be nice
    #        This would require either dumping the content, or registering async task output
    - name: Start test smtpserver
      shell: '{{ ansible_python.executable }} {{ remote_tmp_dir }}/smtpserver.py 10025:10465'
      async: 45
      poll: 0
      register: smtpserver

    - name: Send a basic test-mail
      mail:
        port: 10025
        subject: Test mail 1 (smtp)
        secure: never

    - name: Send a test-mail with body and specific recipient
      mail:
        port: 10025
        from: ansible@localhost
        to: root@localhost
        subject: Test mail 2 (smtp + body)
        body: Test body 2
        secure: never

    - name: Send a test-mail with attachment
      mail:
        port: 10025
        from: ansible@localhost
        to: root@localhost
        subject: Test mail 3 (smtp + body + attachment)
        body: Test body 3
        attach: /etc/group
        secure: never

    # NOTE: This might fail if smtpd-tls is missing or python 2.7.8 or older is used
    - name: Send a test-mail using starttls
      mail:
        port: 10025
        from: ansible@localhost
        to: root@localhost
        subject: Test mail 4 (smtp + starttls + body + attachment)
        body: Test body 4
        attach: /etc/group
        secure: starttls
      ignore_errors: true
      register: starttls_support

    # NOTE: This might fail if smtpd-tls is missing or python 2.7.8 or older is used
    - name: Send a test-mail using TLS
      mail:
        port: 10465
        from: ansible@localhost
        to: root@localhost
        subject: Test mail 5 (smtp + tls + body + attachment)
        body: Test body 5
        attach: /etc/group
        secure: always
      ignore_errors: true
      register: tls_support

    - fail:
        msg: Sending mail using starttls failed.
      when: smtpd_tls is succeeded and starttls_support is failed and tls_support is succeeded

    - fail:
        msg: Send mail using TLS failed.
      when: smtpd_tls is succeeded and tls_support is failed and starttls_support is succeeded

    - name: Send a test-mail with body, specific recipient and specific ehlohost
      mail:
        port: 10025
        ehlohost: some.domain.tld
        from: ansible@localhost
        to: root@localhost
        subject: Test mail 6 (smtp + body + ehlohost)
        body: Test body 6
        secure: never
