---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- set_fact:
    pkg_mgr: community.general.pkgng
    ansible_pkg_mgr: community.general.pkgng
    cacheable: true
  when: ansible_os_family == "FreeBSD"

- set_fact:
    pkg_mgr: community.general.zypper
    ansible_pkg_mgr: community.general.zypper
    cacheable: true
  when: ansible_os_family == "Suse"

- set_fact:
    pkg_mgr: community.general.pacman
    ansible_pkg_mgr: community.general.pacman
    cacheable: true
  when: ansible_os_family == "Archlinux"

- shell:
    cmd: |
      sed -i 's/mirrorlist/#mirrorlist/g' /etc/yum.repos.d/*.repo
      sed -i 's%#baseurl=http://mirror.centos.org/%baseurl=https://vault.centos.org/%g' /etc/yum.repos.d/*.repo
  when: ansible_distribution in 'CentOS' and ansible_distribution_major_version == '7'

- shell:
    cmd: |
      sed -i 's/mirrorlist/#mirrorlist/g' /etc/yum.repos.d/CentOS-Linux-*.repo
      sed -i 's%#baseurl=http://mirror.centos.org/$contentdir/$releasever/%baseurl=https://vault.centos.org/8.4.2105/%g' /etc/yum.repos.d/CentOS-Linux-*.repo
  ignore_errors: true  # This fails for CentOS Stream 8
  when: ansible_distribution in 'CentOS' and ansible_distribution_major_version == '8'

- when: ansible_os_family == "Archlinux"
  block:
    - name: ArchLinux specific setup
      include_tasks: archlinux.yml
