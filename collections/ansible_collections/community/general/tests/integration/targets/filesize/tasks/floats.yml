---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Test module with floating point numbers (ensure they're not rounded too
# wrongly), since in python floats are tricky:
# 256.256 * 1000 == 256255.**********
# 512.512 * 1000 == 512511.**********
# 512.513 * 1000 == 512513.********** != .512513 * 1000000

- name: Create a file with a size of 512.512kB (check mode)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 512.512kB
  register: filesize_test_float_01
  check_mode: true

- name: Stat the file (should not exist)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_float_01


- name: Create a file with a size of 512.512kB
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 512.512kB
  register: filesize_test_float_02

- name: Stat the file (should exist now)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_float_02


- name: Create a file with a size of 0.512512MB (check mode, idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 0.512512MB
  register: filesize_test_float_03
  check_mode: true

- name: Create a file with a size of 0.512512MB (idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 0.512512MB
  register: filesize_test_float_04

- name: Stat the file (should still exist, unchanged)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_float_04


- name: Assert that results are as expected
  ansible.builtin.assert:
    that:
      - filesize_test_float_01 is changed
      - filesize_test_float_02 is changed
      - filesize_test_float_03 is not changed
      - filesize_test_float_04 is not changed

      - filesize_test_float_02.cmd == filesize_test_float_01.cmd
      - filesize_test_float_03.cmd is undefined
      - filesize_test_float_04.cmd is undefined

      - filesize_test_float_01.filesize.bytes == 512512
      - filesize_test_float_02.filesize.bytes == 512512
      - filesize_test_float_03.filesize.bytes == 512512
      - filesize_test_float_04.filesize.bytes == 512512

      - filesize_test_float_01.size_diff == 512512
      - filesize_test_float_02.size_diff == 512512
      - filesize_test_float_03.size_diff == 0
      - filesize_test_float_04.size_diff == 0

      - filesize_test_float_01.state is undefined
      - filesize_test_float_02.state in ["file"]
      - filesize_test_float_01.size is undefined
      - filesize_test_float_02.size == 512512
      - filesize_test_float_03.size == 512512
      - filesize_test_float_04.size == 512512

      - not filesize_stat_float_01.stat.exists
      - filesize_stat_float_02.stat.exists
      - filesize_stat_float_02.stat.isreg
      - filesize_stat_float_02.stat.size == 512512
      - filesize_stat_float_04.stat.size == 512512


- name: Create a file with a size of 512.513kB (check mode)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 512.513kB
  register: filesize_test_float_11
  check_mode: true

- name: Stat the file again (should remain the same)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_float_11


- name: Create a file with a size of 512.513kB
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 512.513kB
  register: filesize_test_float_12

- name: Stat the file (should have grown of 1 byte)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_float_12


- name: Create a file with a size of 0.512513MB (check mode, idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 0.512513MB
  register: filesize_test_float_13
  check_mode: true

- name: Create a file with a size of 0.512513MB (idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 0.512513MB
  register: filesize_test_float_14

- name: Stat the file again (should remain the same)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_float_14


- name: Assert that results are as expected
  ansible.builtin.assert:
    that:
      - filesize_test_float_11 is changed
      - filesize_test_float_12 is changed
      - filesize_test_float_13 is not changed
      - filesize_test_float_14 is not changed

      - filesize_test_float_12.cmd == filesize_test_float_11.cmd
      - filesize_test_float_13.cmd is undefined
      - filesize_test_float_14.cmd is undefined

      - filesize_test_float_11.filesize.bytes == 512513
      - filesize_test_float_12.filesize.bytes == 512513
      - filesize_test_float_13.filesize.bytes == 512513
      - filesize_test_float_14.filesize.bytes == 512513

      - filesize_test_float_11.size_diff == 1
      - filesize_test_float_12.size_diff == 1
      - filesize_test_float_13.size_diff == 0
      - filesize_test_float_14.size_diff == 0

      - filesize_test_float_11.size == 512512
      - filesize_test_float_12.size == 512513
      - filesize_test_float_13.size == 512513
      - filesize_test_float_14.size == 512513

      - filesize_stat_float_11.stat.size == 512512
      - filesize_stat_float_12.stat.size == 512513
      - filesize_stat_float_14.stat.size == 512513


- name: Create a file with a size of 4.004MB (check mode)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 4.004MB
  register: filesize_test_float_21
  check_mode: true

- name: Stat the file again (should remain the same)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_float_21


- name: Create a file with a size of 4.004MB
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 4.004MB
  register: filesize_test_float_22

- name: Stat the file (should have grown to 4.004MB)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_float_22


- name: Create a file with a size of 4.004MB (check mode, idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 4.004MB
  register: filesize_test_float_23
  check_mode: true

- name: Create a file with a size of 4.004MB (idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 4.004MB
  register: filesize_test_float_24

- name: Stat the file again (should remain the same)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_float_24


- name: Assert that results are as expected
  ansible.builtin.assert:
    that:
      - filesize_test_float_21 is changed
      - filesize_test_float_22 is changed
      - filesize_test_float_23 is not changed
      - filesize_test_float_24 is not changed

      - filesize_test_float_22.cmd == filesize_test_float_21.cmd
      - filesize_test_float_23.cmd is undefined
      - filesize_test_float_24.cmd is undefined

      - filesize_test_float_21.filesize.bytes == 4004000
      - filesize_test_float_22.filesize.bytes == 4004000
      - filesize_test_float_23.filesize.bytes == 4004000
      - filesize_test_float_24.filesize.bytes == 4004000

      - filesize_test_float_21.size_diff == 4004000 - 512513
      - filesize_test_float_22.size_diff == 4004000 - 512513
      - filesize_test_float_23.size_diff == 0
      - filesize_test_float_24.size_diff == 0

      - filesize_test_float_21.size == 512513
      - filesize_test_float_22.size == 4004000
      - filesize_test_float_23.size == 4004000
      - filesize_test_float_24.size == 4004000

      - filesize_stat_float_21.stat.size == 512513
      - filesize_stat_float_22.stat.size == 4004000
      - filesize_stat_float_24.stat.size == 4004000


- name: Remove test file
  ansible.builtin.file:
    path: "{{ filesize_testfile }}"
    state: absent
