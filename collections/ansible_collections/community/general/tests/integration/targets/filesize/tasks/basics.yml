---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Test module with basic parameters.
# Create a file, grow it, reduce it to its initial size and check the match
# between initial and final checksums. Also check size formats consistency
# (as 57001B == 57001 B == 57.001 kB, for example, or 0 block or 0 unit is
# zero, etc).

- name: Create an empty file (check mode)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 0
  register: filesize_test_basic_01
  check_mode: true

- name: Stat the file (should not exist)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_01


- name: Create an empty file
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 0
  register: filesize_test_basic_02

- name: Stat the file (should exist now)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_02


- name: Create an empty file (check mode, idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 0G
  register: filesize_test_basic_03
  check_mode: true

- name: Create an empty file (idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 0G
  register: filesize_test_basic_04

- name: Stat the file (should still exist, unchanged)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_04


- name: Assert that results are as expected
  ansible.builtin.assert:
    that:
      # check_mode & idempotency are in good shape.
      - filesize_test_basic_01 is changed
      - filesize_test_basic_02 is changed
      - filesize_test_basic_03 is not changed
      - filesize_test_basic_04 is not changed

      # check_mode returns the same command than actual mode.
      - filesize_test_basic_02.cmd == filesize_test_basic_01.cmd
      - filesize_test_basic_03.cmd is undefined
      - filesize_test_basic_04.cmd is undefined

      # Module's specific return results are consistent with user input, that
      # means: with *expected* results.
      - filesize_test_basic_01.filesize.bytes == 0
      - filesize_test_basic_02.filesize.bytes == 0
      - filesize_test_basic_03.filesize.bytes == 0
      - filesize_test_basic_04.filesize.bytes == 0

      - filesize_test_basic_01.size_diff == 0
      - filesize_test_basic_02.size_diff == 0
      - filesize_test_basic_03.size_diff == 0
      - filesize_test_basic_04.size_diff == 0

      # Results populated by module.set_fs_attributes_if_different() are still
      # consistent with current state of the file.
      - filesize_test_basic_01.state is undefined
      - filesize_test_basic_02.state in ["file"]
      - filesize_test_basic_01.size is undefined
      - filesize_test_basic_02.size == 0
      - filesize_test_basic_03.size == 0
      - filesize_test_basic_04.size == 0

      # Cross results with those retrieved by another module.
      - not filesize_stat_basic_01.stat.exists
      - filesize_stat_basic_02.stat.exists
      - filesize_stat_basic_02.stat.isreg
      - filesize_stat_basic_02.stat.size == 0
      - filesize_stat_basic_04.stat.size == 0


- name: Fill the file up to 57kB (57000B) with random data (check mode)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 57kB
    source: /dev/urandom
  register: filesize_test_basic_11
  check_mode: true

- name: Stat the file (should still be unchanged)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_11


- name: Fill the file up to 57kB (57000B) with random data
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 57kB
    source: /dev/urandom
  register: filesize_test_basic_12

- name: Stat the resulting file (and get its checksum)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_12

- name: Store checksum as fact
  ansible.builtin.set_fact:
    filesize_test_checksum: "{{ filesize_stat_basic_12.stat.checksum }}"


- name: Fill the file up to 57000B (57kB) with random data (check mode, idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 57000B
    source: /dev/urandom
  register: filesize_test_basic_13
  check_mode: true

- name: Fill the file up to 57000B (57kB) with random data (idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 57000B
    source: /dev/urandom
  register: filesize_test_basic_14

- name: Stat the file again (should remain the same)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_14


- name: Assert that results are as expected
  ansible.builtin.assert:
    that:
      - filesize_test_basic_11 is changed
      - filesize_test_basic_12 is changed
      - filesize_test_basic_13 is not changed
      - filesize_test_basic_14 is not changed

      - filesize_test_basic_12.cmd == filesize_test_basic_11.cmd
      - filesize_test_basic_13.cmd is undefined
      - filesize_test_basic_14.cmd is undefined

      - filesize_test_basic_11.filesize.bytes == 57000
      - filesize_test_basic_12.filesize.bytes == 57000
      - filesize_test_basic_13.filesize.bytes == 57000
      - filesize_test_basic_14.filesize.bytes == 57000

      - filesize_test_basic_11.size_diff == 57000
      - filesize_test_basic_12.size_diff == 57000
      - filesize_test_basic_13.size_diff == 0
      - filesize_test_basic_14.size_diff == 0

      - filesize_stat_basic_11.stat.size == 0
      - filesize_stat_basic_12.stat.size == 57000
      - filesize_stat_basic_14.stat.size == 57000

      - filesize_stat_basic_14.stat.checksum == filesize_test_checksum


- name: Expand the file with 1 byte (57001B) (check mode)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 57001B
  register: filesize_test_basic_21
  check_mode: true

- name: Stat the file again (should remain the same)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_21


- name: Expand the file with 1 byte (57001B)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 57001B
  register: filesize_test_basic_22

- name: Stat the file (should have grown of 1 byte)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_22


- name: Expand the file with 1 byte (57.001 kB) (check mode, idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 57.001 kB
  register: filesize_test_basic_23
  check_mode: true

- name: Expand the file with 1 byte (57.001 kB) (idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 57.001 kB
  register: filesize_test_basic_24

- name: Stat the file again (should remain the same)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_24


- name: Assert that results are as expected
  ansible.builtin.assert:
    that:
      - filesize_test_basic_21 is changed
      - filesize_test_basic_22 is changed
      - filesize_test_basic_23 is not changed
      - filesize_test_basic_24 is not changed

      - filesize_test_basic_22.cmd == filesize_test_basic_21.cmd
      - filesize_test_basic_23.cmd is undefined
      - filesize_test_basic_24.cmd is undefined

      - filesize_test_basic_21.filesize.bytes == 57001
      - filesize_test_basic_22.filesize.bytes == 57001
      - filesize_test_basic_23.filesize.bytes == 57001
      - filesize_test_basic_24.filesize.bytes == 57001

      - filesize_test_basic_21.size_diff == 1
      - filesize_test_basic_22.size_diff == 1
      - filesize_test_basic_23.size_diff == 0
      - filesize_test_basic_24.size_diff == 0

      - filesize_stat_basic_21.stat.size == 57000
      - filesize_stat_basic_22.stat.size == 57001
      - filesize_stat_basic_24.stat.size == 57001

      - filesize_stat_basic_21.stat.checksum == filesize_test_checksum
      - filesize_stat_basic_22.stat.checksum != filesize_test_checksum
      - filesize_stat_basic_24.stat.checksum != filesize_test_checksum


- name: Expand the file up to 2 MiB (2*1024*1024 bytes) (check mode)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 2 MiB
  register: filesize_test_basic_31
  check_mode: true

- name: Stat the file again (should remain the same)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_31


- name: Expand the file up to 2 MiB (2*1024*1024 bytes)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 2 MiB
  register: filesize_test_basic_32

- name: Stat the file again (should have grown to 2MiB)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_32


- name: Expand the file up to 2×1M (2*1024*1024 bytes) (check mode, idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 2
    blocksize: 1M
  register: filesize_test_basic_33
  check_mode: true

- name: Expand the file up to 2×1M (2*1024*1024 bytes) (idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 2
    blocksize: 1M
  register: filesize_test_basic_34

- name: Stat the file again (should remain the same)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_34


- name: Assert that results are as expected
  ansible.builtin.assert:
    that:
      - filesize_test_basic_31 is changed
      - filesize_test_basic_32 is changed
      - filesize_test_basic_33 is not changed
      - filesize_test_basic_34 is not changed

      - filesize_test_basic_32.cmd == filesize_test_basic_31.cmd
      - filesize_test_basic_33.cmd is undefined
      - filesize_test_basic_34.cmd is undefined

      - filesize_test_basic_31.filesize.bytes == 2*1024**2
      - filesize_test_basic_32.filesize.bytes == 2*1024**2
      - filesize_test_basic_33.filesize.bytes == 2*1024**2
      - filesize_test_basic_34.filesize.bytes == 2*1024**2

      - filesize_test_basic_31.size_diff == 2*1024**2 - 57001
      - filesize_test_basic_32.size_diff == 2*1024**2 - 57001
      - filesize_test_basic_33.size_diff == 0
      - filesize_test_basic_34.size_diff == 0

      - filesize_stat_basic_31.stat.size == 57001
      - filesize_stat_basic_32.stat.size == 2*1024**2
      - filesize_stat_basic_34.stat.size == 2*1024**2


- name: Truncate the file to 57kB (57000B) (check mode)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 57kB
  register: filesize_test_basic_41
  check_mode: true

- name: Stat the resulting file (should be unchanged)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_41


- name: Truncate the file to 57kB (57000B)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 57kB
  register: filesize_test_basic_42

- name: Stat the resulting file (and get its checksum)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_42


- name: Truncate the file to 57000 B (57kB) (check mode, idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 57000 B
  register: filesize_test_basic_43
  check_mode: true

- name: Truncate the file to 57000 B (57kB) (idempotency)
  community.general.filesize:
    path: "{{ filesize_testfile }}"
    size: 57000 B
  register: filesize_test_basic_44

- name: Stat the file again (should remain the same)
  ansible.builtin.stat:
    path: "{{ filesize_testfile }}"
  register: filesize_stat_basic_44


- name: Assert that results are as expected
  ansible.builtin.assert:
    that:
      - filesize_test_basic_41 is changed
      - filesize_test_basic_42 is changed
      - filesize_test_basic_43 is not changed
      - filesize_test_basic_44 is not changed

      - filesize_test_basic_42.cmd == filesize_test_basic_41.cmd
      - filesize_test_basic_43.cmd is undefined
      - filesize_test_basic_44.cmd is undefined

      - filesize_test_basic_41.filesize.bytes == 57000
      - filesize_test_basic_42.filesize.bytes == 57000
      - filesize_test_basic_43.filesize.bytes == 57000
      - filesize_test_basic_44.filesize.bytes == 57000

      - filesize_test_basic_41.size_diff == 57000 - 2*1024**2
      - filesize_test_basic_42.size_diff == 57000 - 2*1024**2
      - filesize_test_basic_43.size_diff == 0
      - filesize_test_basic_44.size_diff == 0

      - filesize_stat_basic_41.stat.size == 2*1024**2
      - filesize_stat_basic_42.stat.size == 57000
      - filesize_stat_basic_44.stat.size == 57000

      # The original random file is back.
      - filesize_stat_basic_41.stat.checksum != filesize_test_checksum
      - filesize_stat_basic_42.stat.checksum == filesize_test_checksum
      - filesize_stat_basic_44.stat.checksum == filesize_test_checksum


- name: Remove test file
  ansible.builtin.file:
    path: "{{ filesize_testfile }}"
    state: absent
