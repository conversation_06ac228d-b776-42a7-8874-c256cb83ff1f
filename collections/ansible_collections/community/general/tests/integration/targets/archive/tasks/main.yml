---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Test code for the archive module.
# Copyright (c) 2017, A<PERSON><PERSON><PERSON><PERSON> Kasurde <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Make sure we start fresh

# Test setup
- name: prep our files
  copy: src={{ item }} dest={{remote_tmp_dir}}/{{ item }}
  with_items:
    - foo.txt
    - bar.txt
    - empty.txt
    - sub
    - sub/subfile.txt

# Run twice without lzma backport installed, to make sure it does not crash
- name: Archive - pre-test - first run
  archive:
    path: "{{ remote_tmp_dir }}/*.txt"
    dest: "{{ remote_tmp_dir }}/archive_pretest_1.tar"
    format: "tar"
  register: pretest_1

- name: Archive - pre-test - second run
  archive:
    path: "{{ remote_tmp_dir }}/*.txt"
    dest: "{{ remote_tmp_dir }}/archive_pretest_1.tar"
    format: "tar"
  register: pretest_2

- name: Archive - validate pre-test
  assert:
    that:
      - pretest_1 is changed
      - pretest_2 is not changed

# Install dependencies
- name: Ensure zip is present to create test archive (yum)
  yum: name=zip state=latest
  when: ansible_facts.pkg_mgr == 'yum'

- name: Ensure zip is present to create test archive (apt)
  apt: name=zip state=latest
  when: ansible_facts.pkg_mgr == 'apt'

- name: Install prerequisites for backports.lzma when using python2 (non OSX)
  block:
    - name: Set liblzma package name depending on the OS
      set_fact:
        liblzma_dev_package:
          Debian: liblzma-dev
          RedHat: xz-devel
          Suse: xz-devel
    - name: Ensure liblzma-dev is present to install backports-lzma
      package: name={{ liblzma_dev_package[ansible_os_family] }} state=latest
      when: ansible_os_family in liblzma_dev_package.keys()
  when:
    - ansible_python_version.split('.')[0] == '2'
    - ansible_os_family != 'Darwin'

- name: Install prerequisites for backports.lzma when using python2 (OSX)
  block:
    - name: Find brew binary
      command: which brew
      register: brew_which
    - name: Get owner of brew binary
      stat: path="{{ brew_which.stdout }}"
      register: brew_stat
    - name: "Install package"
      homebrew:
        name: xz
        state: present
        update_homebrew: false
      become: true
      become_user: "{{ brew_stat.stat.pw_name }}"
      # Newer versions of brew want to compile a package which takes a long time. Do not upgrade homebrew until a
      # proper solution can be found
      environment:
        HOMEBREW_NO_AUTO_UPDATE: "True"
  when:
    - ansible_python_version.split('.')[0] == '2'
    - ansible_os_family == 'Darwin'

- name: Ensure backports.lzma is present to create test archive (pip)
  pip: name=backports.lzma state=latest
  when: ansible_python_version.split('.')[0] == '2'
  register: backports_lzma_pip

- name: Define formats to test
  set_fact:
    formats:
      - tar
      - zip
      - gz
      - bz2
      - xz

# Run tests
- name: Run core tests
  include_tasks:
    file: ../tests/core.yml
  loop: "{{ formats }}"
  loop_control:
    loop_var: format

- name: Run exclusions tests
  include_tasks:
    file: ../tests/exclusions.yml
  loop: "{{ formats }}"
  loop_control:
    loop_var: format

- name: Run remove tests
  include_tasks:
    file: ../tests/remove.yml
  loop: "{{ formats }}"
  loop_control:
    loop_var: format

- name: Run broken link tests
  include_tasks:
    file: ../tests/broken-link.yml
  loop: "{{ formats }}"
  loop_control:
    loop_var: format

- name: Run Idempotency tests
  include_tasks:
    file: ../tests/idempotency.yml
  loop: "{{ formats }}"
  loop_control:
    loop_var: format

# Test cleanup
- name: Remove backports.lzma if previously installed (pip)
  pip: name=backports.lzma state=absent
  when: backports_lzma_pip is changed
