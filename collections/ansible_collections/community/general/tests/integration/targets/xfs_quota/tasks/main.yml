---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: setup Alpine
  when: ansible_distribution == 'Alpine'
  package:
    name:
      - xfsprogs
      - xfsprogs-extra
      - mount
      - umount
    state: latest

- block:
    - name: Create test user
      user:
        name: xfsquotauser
        state: present
      become: true

    - include_tasks: uquota.yml
    - include_tasks: gquota.yml
    - include_tasks: pquota.yml

  always:
    - name: cleanup test user
      user:
        name: xfsquotauser
        state: absent
      become: true
