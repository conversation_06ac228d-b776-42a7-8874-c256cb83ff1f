# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

[lxc]
lxc-pipelining    ansible_ssh_pipelining=true
lxc-no-pipelining ansible_ssh_pipelining=false
[lxc:vars]
# 1. install lxc
# 2. install python2-lxc
# $ pip install git+https://github.com/lxc/python2-lxc.git
# 3. create container:
# $ sudo lxc-create -t download -n centos-7-amd64 -- -d centos -r 7 -a amd64
# 4. start container:
# $ sudo lxc-start -n centos-7-amd64 -d
# 5. run test:
# $ sudo -E make test_connection_lxc
# 6. stop container
# $ sudo lxc-stop -n centos-7-amd64
ansible_host=centos-7-amd64
ansible_connection=community.general.lxc
