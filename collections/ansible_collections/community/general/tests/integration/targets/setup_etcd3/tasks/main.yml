---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# setup etcd3 for integration tests on module/lookup
# Copyright 2017,  <PERSON><PERSON><PERSON> <<EMAIL>>
# Copyright 2020, SCC France, <PERSON> <ebelhom<PERSON>@fr.scc.com>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# ============================================================

# setup etcd3 for supported distros
- block:

    - name: python 2
      set_fact:
        python_suffix: ""
      when: ansible_python_version is version('3', '<')

    - name: python 3
      set_fact:
        python_suffix: "-py3"
      when: ansible_python_version is version('3', '>=')

    - include_vars: '{{ item }}'
      with_first_found:
        - files:
            - '{{ ansible_distribution }}-{{ ansible_distribution_major_version }}{{ python_suffix }}.yml'
            - '{{ ansible_distribution }}-{{ ansible_distribution_version }}{{ python_suffix }}.yml'
            - '{{ ansible_os_family }}-{{ ansible_distribution_major_version }}{{ python_suffix }}.yml'
            - '{{ ansible_os_family }}{{ python_suffix }}.yml'
            - 'default{{ python_suffix }}.yml'
            - 'default.yml'
          paths: '../vars'

    - name: Upgrade setuptools python2 module
      pip:
        name: setuptools<45
        extra_args: --upgrade
        state: present
      when: python_suffix == ''

    - name: Install etcd3 python modules
      pip:
        name: "{{ etcd3_pip_module }}"
        extra_args: --only-binary grpcio
        state: present

    # Check if re-installing etcd3 is required
    - name: Check if etcd3ctl exists for reuse.
      shell: "ETCDCTL_API=3 {{ etcd3_path }}/etcdctl --endpoints=localhost:2379 get foo"
      args:
        executable: /bin/bash
      changed_when: false
      failed_when: false
      register: _testetcd3ctl

    - block:
      # Installing etcd3
        - name: If can't reuse, prepare download folder
          file:
            path: "{{ etcd3_download_location }}"
            state: directory
          register: _etcddownloadexists
          when:
            - _testetcd3ctl.rc != 0

        - name: Delete download folder if already exists (to start clean)
          file:
            path: "{{ etcd3_download_location }}"
            state: absent
          when:
            - _etcddownloadexists is not changed

        - name: Recreate download folder if purged
          file:
            path: "{{ etcd3_download_location }}"
            state: directory
          when:
            - _etcddownloadexists is not changed

        - name: Download etcd3
          unarchive:
            src: "{{ etcd3_download_url }}"
            dest: "{{ etcd3_download_location }}"
            remote_src: true

        # Running etcd3 and kill afterwards if it wasn't running before.
        - name: Run etcd3
          shell: "{{ etcd3_path }}/etcd &"
          register: _etcd3run
          changed_when: true

      #    - name: kill etcd3
      #      command: "pkill etcd"

      when:
        - _testetcd3ctl.rc != 0

  when:
    - ansible_distribution | lower ~ "-" ~ ansible_distribution_major_version | lower != 'centos-6'
