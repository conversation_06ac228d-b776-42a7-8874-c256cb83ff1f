---
# setup etcd3 for integration tests on module/lookup
# (c) 2017,  <PERSON><PERSON><PERSON> <<EMAIL>>
# 2020, SCC France, <PERSON> <<EMAIL>>
#
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later
# # Copyright (c) 2018, Ansible Project
#
etcd3_ver: "v3.2.14"
etcd3_download_server: "https://storage.googleapis.com/etcd"
# etcd3_download_server: "https://github.com/coreos/etcd/releases/download"
etcd3_download_url: "{{ etcd3_download_server }}/{{ etcd3_ver }}/etcd-{{ etcd3_ver }}-linux-amd64.tar.gz"
etcd3_download_location: /tmp/etcd-download-test
etcd3_path: "{{ etcd3_download_location }}/etcd-{{ etcd3_ver }}-linux-amd64"

etcd3_pip_module: etcd3>=0.12
