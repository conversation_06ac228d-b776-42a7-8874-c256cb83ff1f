---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Pre-test setup
- name: create a vpc
  hwc_network_vpc:
    cidr: "*************/24"
    name: "ansible_network_vpc_test"
    state: present
  register: vpc
- name: create a subnet
  hwc_vpc_subnet:
    gateway_ip: "**************"
    name: "ansible_network_subnet_test"
    dhcp_enable: true
    vpc_id: "{{ vpc.id }}"
    cidr: "*************/26"
    state: present
  register: subnet
- name: delete a private ip
  hwc_vpc_private_ip:
    subnet_id: "{{ subnet.id }}"
    ip_address: "**************"
    state: absent
# ----------------------------------------------------------
- name: create a private ip (check mode)
  hwc_vpc_private_ip:
    subnet_id: "{{ subnet.id }}"
    ip_address: "**************"
    state: present
  check_mode: true
  register: result
- name: assert changed is true
  assert:
    that:
      - result is changed
# ----------------------------------------------------------
- name: create a private ip
  hwc_vpc_private_ip:
    subnet_id: "{{ subnet.id }}"
    ip_address: "**************"
    state: present
  register: result
- name: assert changed is true
  assert:
    that:
      - result is changed
# ----------------------------------------------------------
- name: create a private ip (idemponent)
  hwc_vpc_private_ip:
    subnet_id: "{{ subnet.id }}"
    ip_address: "**************"
    state: present
  check_mode: true
  register: result
- name: idemponent
  assert:
    that:
      - not result.changed
# ----------------------------------------------------------------------------
- name: create a private ip that already exists
  hwc_vpc_private_ip:
    subnet_id: "{{ subnet.id }}"
    ip_address: "**************"
    state: present
  register: result
- name: assert changed is false
  assert:
    that:
      - result is not failed
      - result is not changed
# ----------------------------------------------------------
- name: delete a private ip (check mode)
  hwc_vpc_private_ip:
    subnet_id: "{{ subnet.id }}"
    ip_address: "**************"
    state: absent
  check_mode: true
  register: result
- name: assert changed is true
  assert:
    that:
      - result is changed
# ----------------------------------------------------------
- name: delete a private ip
  hwc_vpc_private_ip:
    subnet_id: "{{ subnet.id }}"
    ip_address: "**************"
    state: absent
  register: result
- name: assert changed is true
  assert:
    that:
      - result is changed
# ----------------------------------------------------------
- name: delete a private ip (idemponent)
  hwc_vpc_private_ip:
    subnet_id: "{{ subnet.id }}"
    ip_address: "**************"
    state: absent
  check_mode: true
  register: result
- name: idemponent
  assert:
    that:
      - not result.changed
# ----------------------------------------------------------------------------
- name: delete a private ip that does not exist
  hwc_vpc_private_ip:
    subnet_id: "{{ subnet.id }}"
    ip_address: "**************"
    state: absent
  register: result
- name: assert changed is false
  assert:
    that:
      - result is not failed
      - result is not changed
# ---------------------------------------------------------
# Post-test teardown
- name: delete a subnet
  hwc_vpc_subnet:
    gateway_ip: "**************"
    name: "ansible_network_subnet_test"
    dhcp_enable: true
    vpc_id: "{{ vpc.id }}"
    cidr: "*************/26"
    state: absent
  register: subnet
- name: delete a vpc
  hwc_network_vpc:
    cidr: "*************/24"
    name: "ansible_network_vpc_test"
    state: absent
  register: vpc
