---
# Test code for the iso_extract module.
# Copyright (c) 2017, <PERSON> <<EMAIL>>
# Copyright (c) 2017, <PERSON><PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Extract the iso
  iso_extract:
    image: '{{ output_test_dir }}/test.iso'
    dest: '{{ output_test_dir }}'
    files:
      - 1.txt
      - 2.txt
  register: iso_extract_test0

- assert:
    that:
      - iso_extract_test0 is changed

- name: Extract the iso again
  iso_extract:
    image: '{{ output_test_dir }}/test.iso'
    dest: '{{ output_test_dir }}'
    files:
      - 1.txt
      - 2.txt
  register: iso_extract_test0_again

- name: Test iso_extract_test0_again (normal mode)
  assert:
    that:
      - iso_extract_test0_again is not changed
  when: not in_check_mode

- name: Test iso_extract_test0_again (check-mode)
  assert:
    that:
      - iso_extract_test0_again is changed
  when: in_check_mode
