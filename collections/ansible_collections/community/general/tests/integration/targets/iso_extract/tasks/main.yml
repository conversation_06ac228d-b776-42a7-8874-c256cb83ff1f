---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Test code for the iso_extract module.
# Copyright (c) 2017, <PERSON> <<EMAIL>>
# Copyright (c) 2017, Dag <PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- set_fact:
    output_test_dir: '{{ remote_tmp_dir }}/test_iso_extract'

- name: Install EPEL repository (RHEL only)
  include_role:
    name: setup_epel
  when:
    - ansible_distribution in ['RedHat', 'CentOS']
    - ansible_distribution_major_version is version('9', '<')

- name: Install 7zip
  import_tasks: 7zip.yml

- name: Prepare environment
  import_tasks: prepare.yml

- name: Test in normal mode
  import_tasks: tests.yml
  vars:
    in_check_mode: false

- name: Prepare environment
  import_tasks: prepare.yml

- name: Test in check-mode
  import_tasks: tests.yml
  vars:
    in_check_mode: true
  check_mode: true

# FIXME - fill this in after figuring out how to allow mounts
