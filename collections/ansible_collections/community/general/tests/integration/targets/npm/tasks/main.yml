---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# test code for the npm module
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# -------------------------------------------------------------
# Setup steps

- when:
    - not (ansible_os_family == 'Alpine')  # TODO
  block:

    # expand remote path
    - command: 'echo {{ remote_tmp_dir }}'
      register: echo
    - set_fact:
        remote_dir: '{{ echo.stdout }}'

    - include_tasks: run.yml
      vars:
        nodejs_version: '{{ item }}'
        nodejs_path: 'node-v{{ nodejs_version }}-{{ ansible_system|lower }}-x{{ ansible_userspace_bits }}'
      with_items:
        - 7.10.1 # provides npm 4.2.0 (last npm < 5 released)
        - 8.0.0 # provides npm 5.0.0
        - 8.2.0 # provides npm 5.3.0 (output change with this version)
