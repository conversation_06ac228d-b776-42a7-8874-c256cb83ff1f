---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) 2014, <PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Bail out if not supported
  ansible.builtin.meta: end_play
  when: ansible_distribution not in ('Ubuntu', 'Debian', 'Archlinux')

- name: Run tests auto-detecting mechanism
  ansible.builtin.include_tasks: basic.yml
  loop: "{{ locale_list_basic }}"
  loop_control:
    loop_var: locale_basic
