---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- pause:
    # ufw creates backups of the rule files with a timestamp; if reset is called
    # twice in a row fast enough (so that both timestamps are taken in the same second),
    # the second call will notice that the backup files are already there and fail.
    # Waiting one second fixes this problem.
    seconds: 1
- name: Reset ufw to factory defaults
  ufw:
    state: reset
- name: Disable ufw
  ufw:
    # Some versions of ufw have a bug which won't disable on reset.
    # That's why we explicitly deactivate here. See
    # https://bugs.launchpad.net/ufw/+bug/1810082
    state: disabled
- name: "Loading tasks from {{ item }}"
  include_tasks: "{{ item }}"
- name: Reset to factory defaults
  ufw:
    state: reset
