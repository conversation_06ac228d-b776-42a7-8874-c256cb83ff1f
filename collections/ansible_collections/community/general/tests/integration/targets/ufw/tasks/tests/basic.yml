---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# ############################################
- name: Make sure it is off
  ufw:
    state: disabled
- name: Enable (check mode)
  ufw:
    state: enabled
  check_mode: true
  register: enable_check
- name: Enable
  ufw:
    state: enabled
  register: enable
- name: Enable (idempotency)
  ufw:
    state: enabled
  register: enable_idem
- name: Enable (idempotency, check mode)
  ufw:
    state: enabled
  check_mode: true
  register: enable_idem_check
- assert:
    that:
      - enable_check is changed
      - enable is changed
      - enable_idem is not changed
      - enable_idem_check is not changed

# ############################################
- name: ipv4 allow (check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
  check_mode: true
  register: ipv4_allow_check
- name: ipv4 allow
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
  register: ipv4_allow
- name: ipv4 allow (idempotency)
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
  register: ipv4_allow_idem
- name: ipv4 allow (idempotency, check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
  check_mode: true
  register: ipv4_allow_idem_check
- assert:
    that:
      - ipv4_allow_check is changed
      - ipv4_allow is changed
      - ipv4_allow_idem is not changed
      - ipv4_allow_idem_check is not changed

# ############################################
- name: delete ipv4 allow (check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
    delete: true
  check_mode: true
  register: delete_ipv4_allow_check
- name: delete ipv4 allow
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
    delete: true
  register: delete_ipv4_allow
- name: delete ipv4 allow (idempotency)
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
    delete: true
  register: delete_ipv4_allow_idem
- name: delete ipv4 allow (idempotency, check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
    delete: true
  check_mode: true
  register: delete_ipv4_allow_idem_check
- assert:
    that:
      - delete_ipv4_allow_check is changed
      - delete_ipv4_allow is changed
      - delete_ipv4_allow_idem is not changed
      - delete_ipv4_allow_idem_check is not changed

# ############################################
- name: ipv6 allow (check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
  check_mode: true
  register: ipv6_allow_check
- name: ipv6 allow
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
  register: ipv6_allow
- name: ipv6 allow (idempotency)
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
  register: ipv6_allow_idem
- name: ipv6 allow (idempotency, check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
  check_mode: true
  register: ipv6_allow_idem_check
- assert:
    that:
      - ipv6_allow_check is changed
      - ipv6_allow is changed
      - ipv6_allow_idem is not changed
      - ipv6_allow_idem_check is not changed

# ############################################
- name: delete ipv6 allow (check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
    delete: true
  check_mode: true
  register: delete_ipv6_allow_check
- name: delete ipv6 allow
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
    delete: true
  register: delete_ipv6_allow
- name: delete ipv6 allow (idempotency)
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
    delete: true
  register: delete_ipv6_allow_idem
- name: delete ipv6 allow (idempotency, check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
    delete: true
  check_mode: true
  register: delete_ipv6_allow_idem_check
- assert:
    that:
      - delete_ipv6_allow_check is changed
      - delete_ipv6_allow is changed
      - delete_ipv6_allow_idem is not changed
      - delete_ipv6_allow_idem_check is not changed


# ############################################
- name: ipv4 allow (check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
  check_mode: true
  register: ipv4_allow_check
- name: ipv4 allow
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
  register: ipv4_allow
- name: ipv4 allow (idempotency)
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
  register: ipv4_allow_idem
- name: ipv4 allow (idempotency, check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
  check_mode: true
  register: ipv4_allow_idem_check
- assert:
    that:
      - ipv4_allow_check is changed
      - ipv4_allow is changed
      - ipv4_allow_idem is not changed
      - ipv4_allow_idem_check is not changed

# ############################################
- name: delete ipv4 allow (check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
    delete: true
  check_mode: true
  register: delete_ipv4_allow_check
- name: delete ipv4 allow
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
    delete: true
  register: delete_ipv4_allow
- name: delete ipv4 allow (idempotency)
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
    delete: true
  register: delete_ipv4_allow_idem
- name: delete ipv4 allow (idempotency, check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: 0.0.0.0
    delete: true
  check_mode: true
  register: delete_ipv4_allow_idem_check
- assert:
    that:
      - delete_ipv4_allow_check is changed
      - delete_ipv4_allow is changed
      - delete_ipv4_allow_idem is not changed
      - delete_ipv4_allow_idem_check is not changed

# ############################################
- name: ipv6 allow (check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
  check_mode: true
  register: ipv6_allow_check
- name: ipv6 allow
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
  register: ipv6_allow
- name: ipv6 allow (idempotency)
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
  register: ipv6_allow_idem
- name: ipv6 allow (idempotency, check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
  check_mode: true
  register: ipv6_allow_idem_check
- assert:
    that:
      - ipv6_allow_check is changed
      - ipv6_allow is changed
      - ipv6_allow_idem is not changed
      - ipv6_allow_idem_check is not changed

# ############################################
- name: delete ipv6 allow (check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
    delete: true
  check_mode: true
  register: delete_ipv6_allow_check
- name: delete ipv6 allow
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
    delete: true
  register: delete_ipv6_allow
- name: delete ipv6 allow (idempotency)
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
    delete: true
  register: delete_ipv6_allow_idem
- name: delete ipv6 allow (idempotency, check mode)
  ufw:
    rule: allow
    port: 23
    to_ip: "::"
    delete: true
  check_mode: true
  register: delete_ipv6_allow_idem_check
- assert:
    that:
      - delete_ipv6_allow_check is changed
      - delete_ipv6_allow is changed
      - delete_ipv6_allow_idem is not changed
      - delete_ipv6_allow_idem_check is not changed

# ############################################
- name: Reload ufw
  ufw:
    state: reloaded
  register: reload
- name: Reload ufw (check mode)
  ufw:
    state: reloaded
  check_mode: true
  register: reload_check
- assert:
    that:
      - reload is changed
      - reload_check is changed

# ############################################
- name: Disable (check mode)
  ufw:
    state: disabled
  check_mode: true
  register: disable_check
- name: Disable
  ufw:
    state: disabled
  register: disable
- name: Disable (idempotency)
  ufw:
    state: disabled
  register: disable_idem
- name: Disable (idempotency, check mode)
  ufw:
    state: disabled
  check_mode: true
  register: disable_idem_check
- assert:
    that:
      - disable_check is changed
      - disable is changed
      - disable_idem is not changed
      - disable_idem_check is not changed

# ############################################
- name: Re-enable
  ufw:
    state: enabled
- name: Reset (check mode)
  ufw:
    state: reset
  check_mode: true
  register: reset_check
- pause:
    # Should not be needed, but since ufw is ignoring --dry-run for reset
    # (https://bugs.launchpad.net/ufw/+bug/1810082) we have to wait here as well.
    seconds: 1
- name: Reset
  ufw:
    state: reset
  register: reset
- pause:
    # ufw creates backups of the rule files with a timestamp; if reset is called
    # twice in a row fast enough (so that both timestamps are taken in the same second),
    # the second call will notice that the backup files are already there and fail.
    # Waiting one second fixes this problem.
    seconds: 1
- name: Reset (idempotency)
  ufw:
    state: reset
  register: reset_idem
- pause:
    # Should not be needed, but since ufw is ignoring --dry-run for reset
    # (https://bugs.launchpad.net/ufw/+bug/1810082) we have to wait here as well.
    seconds: 1
- name: Reset (idempotency, check mode)
  ufw:
    state: reset
  check_mode: true
  register: reset_idem_check
- assert:
    that:
      - reset_check is changed
      - reset is changed
      - reset_idem is changed
      - reset_idem_check is changed
