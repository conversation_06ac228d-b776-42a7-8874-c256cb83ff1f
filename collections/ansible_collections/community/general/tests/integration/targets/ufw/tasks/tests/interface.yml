---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Enable
  ufw:
    state: enabled

- name: Route with interface in and out
  ufw:
    rule: allow
    route: true
    interface_in: foo
    interface_out: bar
    proto: tcp
    from_ip: *******
    to_ip: *******
    from_port: 1111
    to_port: 2222

- name: Route with interface in
  ufw:
    rule: allow
    route: true
    interface_in: foo
    proto: tcp
    from_ip: *******
    from_port: 1111

- name: Route with interface out
  ufw:
    rule: allow
    route: true
    interface_out: bar
    proto: tcp
    from_ip: *******
    from_port: 1111

- name: Non-route with interface in
  ufw:
    rule: allow
    interface_in: foo
    proto: tcp
    from_ip: *******
    from_port: 3333

- name: Non-route with interface out
  ufw:
    rule: allow
    interface_out: bar
    proto: tcp
    from_ip: *******
    from_port: 4444

- name: Check result
  shell: ufw status |grep -E '(ALLOW|DENY|REJECT|LIMIT)' |sed -E 's/[ \t]+/ /g'
  register: ufw_status

- assert:
    that:
      - '"******* 2222/tcp on bar ALLOW FWD ******* 1111/tcp on foo " in stdout'
      - '"Anywhere ALLOW FWD ******* 1111/tcp on foo " in stdout'
      - '"Anywhere on bar ALLOW FWD ******* 1111/tcp " in stdout'
      - '"Anywhere on foo ALLOW ******* 3333/tcp " in stdout'
      - '"Anywhere ALLOW OUT ******* 4444/tcp on bar " in stdout'
  vars:
    stdout: '{{ ufw_status.stdout_lines }}'

- name: Non-route with interface_in and interface_out
  ufw:
    rule: allow
    interface_in: foo
    interface_out: bar
    proto: tcp
    from_ip: *******
    from_port: 1111
    to_ip: *******
    to_port: 2222
  ignore_errors: true
  register: ufw_non_route_iface

- assert:
    that:
      - ufw_non_route_iface is failed
      - '"Only route rules" in ufw_non_route_iface.msg'
