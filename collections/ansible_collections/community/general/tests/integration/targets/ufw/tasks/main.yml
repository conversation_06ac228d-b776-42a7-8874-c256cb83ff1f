---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Make sure ufw is installed
- name: Install EPEL repository (RHEL only)
  include_role:
    name: setup_epel
  when:
    - ansible_distribution in ['RedHat', 'CentOS']
    - ansible_distribution_major_version is version('9', '<')
- name: Install iptables (SuSE only)
  package:
    name: iptables
  become: true
  when: ansible_os_family == 'Suse'
- name: Install ufw
  become: true
  package:
    name: ufw

# Run the tests
- block:
    - include_tasks: run-test.yml
      with_fileglob:
        - "tests/*.yml"
  become: true

  # Cleanup
  always:
    - pause:
        # ufw creates backups of the rule files with a timestamp; if reset is called
        # twice in a row fast enough (so that both timestamps are taken in the same second),
        # the second call will notice that the backup files are already there and fail.
        # Waiting one second fixes this problem.
        seconds: 1
    - name: Reset ufw to factory defaults and disable
      ufw:
        state: reset
