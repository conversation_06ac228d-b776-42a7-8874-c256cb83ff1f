####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# test code for the hg module
# Copyright (c) 2014, <PERSON> <<EMAIL>>
#
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: determine if mercurial is already installed
  command: which hg
  register: has_hg
  ignore_errors: true

- name: warn if the underlying system is not capable of running these tests
  debug:
    msg: >-
      The mercurial client is not able to check out Bitbucket repositories as per the changes mentioned here:
      https://bitbucket.org/blog/deprecating-tlsv1-tlsv1-1-2018-12-01 . Therefore these tests are skipped.
  when: (ansible_distribution == "Ubuntu" and ansible_distribution_version == "14.04") or ansible_python_version is version("2.7.9", "<")

- block:
    - name: install mercurial
      include_tasks: install.yml
      when: has_hg is failed

    - name: test mercurial
      include_tasks: run-tests.yml

    - name: uninstall mercurial
      include_tasks: uninstall.yml
      when: has_hg is failed

  # As per the bitbucket changes in https://bitbucket.org/blog/deprecating-tlsv1-tlsv1-1-2018-12-01 , this
  #  test will fail under certain circumstances, to avoid false positives, we skip these tests under the following
  #  circumstances:
  #
  # - The ubuntu 14.04 image used on shippable runs python 2.7.6, so we skip explicitly for this image.
  # - When ansible_python_version is not 2.7.9 or higher, mercurial is likely to also run using this same (old)
  #   python version, which causes issues as per the link above.
  when:
    - not (ansible_distribution == "Ubuntu" and ansible_distribution_version == "14.04")
    - ansible_python_version is version("2.7.9", ">=")
