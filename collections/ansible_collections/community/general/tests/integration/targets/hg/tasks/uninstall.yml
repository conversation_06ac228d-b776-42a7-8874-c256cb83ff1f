---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: restore the updated python
  command: mv "{{ which_python.stdout }}.updated" "{{ which_python.stdout }}"

- name: restore the updated pip
  command: mv "{{ which_pip.stdout }}.updated" "{{ which_pip.stdout }}"

- name: restore the mercurial python interpreter symlink (if needed)
  lineinfile:
    path: "{{ which_hg.stdout }}"
    regexp: "^#!.*$"
    line: "#!{{ stat_hg_interpreter.stat.path }}"
  when: stat_hg_interpreter.stat.islnk

- name: uninstall packages which were not originally installed (apk)
  package:
    name: mercurial
    state: absent
  when: ansible_facts.pkg_mgr in ['apk', 'community.general.apk']

# using the apt module prevents autoremove from working, so call apt-get via shell instead
- name: uninstall packages which were not originally installed (apt)
  shell: apt-get -y remove mercurial && apt-get -y autoremove
  when: ansible_facts.pkg_mgr == 'apt'

- name: uninstall packages which were not originally installed (dnf)
  dnf:
    name: mercurial
    state: absent
    autoremove: true
  when: ansible_facts.pkg_mgr == 'dnf'

# the yum module does not have an autoremove parameter
- name: uninstall packages which were not originally installed (yum)
  shell: yum -y autoremove mercurial
  when: ansible_facts.pkg_mgr == 'yum'

- name: uninstall packages which were not originally installed (pacman)
  package:
    name: mercurial
    state: absent
  when: ansible_facts.pkg_mgr in ['pacman', 'community.general.pacman']

- name: uninstall packages which were not originally installed (pkgng)
  package:
    name: mercurial
    state: absent
    autoremove: true
  when: ansible_facts.pkg_mgr in ['pkgng', 'community.general.pkgng']

- name: uninstall packages which were not originally installed (zypper)
  package:
    name: mercurial
    state: absent
  when: ansible_facts.pkg_mgr in ['zypper', 'community.general.zypper']

- name: restore the default python
  raw: mv "{{ which_python.stdout }}.default" "{{ which_python.stdout }}"

- name: restore the default pip
  raw: mv "{{ which_pip.stdout }}.default" "{{ which_pip.stdout }}"
