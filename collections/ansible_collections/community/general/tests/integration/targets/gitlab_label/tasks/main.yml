---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Install required libs
  pip:
    name: python-gitlab
    state: present

- block:
  ###
  ### Group label
  ###

    - name: Create {{ gitlab_project_group }}
      gitlab_group:
        api_url: "{{ gitlab_host }}"
        validate_certs: true
        api_token: "{{ gitlab_api_token }}"
        name: "{{ gitlab_project_group }}"
        state: present

    - name: Purge all group labels for check_mode test
      gitlab_label:
        api_url: "{{ gitlab_host }}"
        api_token: "{{ gitlab_api_token }}"
        group: "{{ gitlab_project_group }}"
        purge: true

    - name: Group label - Add a label in check_mode
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        group: "{{ gitlab_project_group }}"
        labels:
          - name: "{{ gitlab_second_label }}"
            color: "{{ gitlab_second_label_color }}"
      check_mode: true
      register: gitlab_group_label_state

    - name: Group label - Check_mode state must be changed
      assert:
        that:
          - gitlab_group_label_state is changed

    - name: Group label - Create label {{ gitlab_first_label }} and {{ gitlab_second_label }}
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        group: "{{ gitlab_project_group }}"
        labels:
          - name: "{{ gitlab_first_label }}"
            color: "{{ gitlab_first_label_color }}"
            description: "{{ gitlab_first_label_description }}"
            priority: "{{ gitlab_first_label_priority }}"
          - name: "{{ gitlab_second_label }}"
            color: "{{ gitlab_second_label_color }}"
        state: present
      register: gitlab_group_label_create

    - name: Group label - Test Label Created
      assert:
        that:
          - gitlab_group_label_create is changed
          - gitlab_group_label_create.labels.added|length == 2
          - gitlab_group_label_create.labels.untouched|length == 0
          - gitlab_group_label_create.labels.removed|length == 0
          - gitlab_group_label_create.labels.updated|length == 0
          - gitlab_group_label_create.labels.added[0] == "{{ gitlab_first_label }}"
          - gitlab_group_label_create.labels.added[1] == "{{ gitlab_second_label }}"

    - name: Group label - Create Label ( Idempotency test )
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        group: "{{ gitlab_project_group }}"
        labels:
          - name: "{{ gitlab_first_label }}"
            color: "{{ gitlab_first_label_color }}"
            description: "{{ gitlab_first_label_description }}"
            priority: "{{ gitlab_first_label_priority }}"
        state: present
      register: gitlab_group_label_create_idempotence

    - name: Group label - Test Create Label is Idempotent
      assert:
        that:
          - gitlab_group_label_create_idempotence is not changed

    - name: Group label - Update Label {{ gitlab_first_label }} changing color
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        group: "{{ gitlab_project_group }}"
        labels:
          - name: "{{ gitlab_first_label }}"
            color: "{{ gitlab_second_label_color }}"
        state: present
      register: gitlab_group_label_update

    - name: Group label - Test Label Updated
      assert:
        that:
          - gitlab_group_label_update.labels.added|length == 0
          - gitlab_group_label_update.labels.untouched|length == 0
          - gitlab_group_label_update.labels.removed|length == 0
          - gitlab_group_label_update.labels.updated|length == 1
          - gitlab_group_label_update.labels.updated[0] == "{{ gitlab_first_label }}"

    - name: Group label - Change label {{ gitlab_second_label }} name to {{ gitlab_second_label_new_name }}
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        group: "{{ gitlab_project_group }}"
        labels:
          - name: "{{ gitlab_second_label }}"
            new_name: "{{ gitlab_second_label_new_name }}"
        state: present
      register: gitlab_group_label_new_name

    - name: Group label - Test Label name changed
      assert:
        that:
          - gitlab_group_label_new_name.labels.added|length == 0
          - gitlab_group_label_new_name.labels.untouched|length == 0
          - gitlab_group_label_new_name.labels.removed|length == 0
          - gitlab_group_label_new_name.labels.updated|length == 1
          - gitlab_group_label_new_name.labels.updated[0] == "{{ gitlab_second_label }}"

    - name: Group label - Change label name back to {{ gitlab_second_label }}
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        group: "{{ gitlab_project_group }}"
        labels:
          - name: "{{ gitlab_second_label_new_name }}"
            new_name: "{{ gitlab_second_label }}"
        state: present
      register: gitlab_group_label_orig_name

    - name: Group label - Test Label name changed back
      assert:
        that:
          - gitlab_group_label_orig_name.labels.added|length == 0
          - gitlab_group_label_orig_name.labels.untouched|length == 0
          - gitlab_group_label_orig_name.labels.removed|length == 0
          - gitlab_group_label_orig_name.labels.updated|length == 1
          - gitlab_group_label_orig_name.labels.updated[0] == "{{ gitlab_second_label_new_name }}"

    - name: Group label - Update Label Test ( Additions )
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        group: "{{ gitlab_project_group }}"
        labels:
          - name: "{{ gitlab_second_label }}"
            description: "{{ gitlab_first_label_description }}"
            priority: "{{ gitlab_first_label_priority }}"
        state: present
      register: gitlab_group_label_update_additions

    - name: Group label - Test Label Updated ( Additions )
      assert:
        that:
          - gitlab_group_label_update_additions.labels.added|length == 0
          - gitlab_group_label_update_additions.labels.untouched|length == 0
          - gitlab_group_label_update_additions.labels.removed|length == 0
          - gitlab_group_label_update_additions.labels.updated|length == 1
          - gitlab_group_label_update_additions.labels.updated[0] == "{{ gitlab_second_label }}"

    - name: Group label - Delete Label {{ gitlab_second_label }}
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        group: "{{ gitlab_project_group }}"
        labels:
          - name: "{{ gitlab_second_label }}"
        state: absent
      register: gitlab_group_label_delete

    - name: Group label - Test label is deleted
      assert:
        that:
          - gitlab_group_label_delete is changed
          - gitlab_group_label_delete.labels.added|length == 0
          - gitlab_group_label_delete.labels.untouched|length == 0
          - gitlab_group_label_delete.labels.removed|length == 1
          - gitlab_group_label_delete.labels.updated|length == 0
          - gitlab_group_label_delete.labels.removed[0] == "{{ gitlab_second_label }}"

    - name: Group label - Create label {{ gitlab_second_label }} again purging the other
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        group: "{{ gitlab_project_group }}"
        purge: true
        labels:
          - name: "{{ gitlab_second_label }}"
            color: "{{ gitlab_second_label_color }}"
        state: present
      register: gitlab_group_label_create_purging

    - name: Group label - Test Label Created again
      assert:
        that:
          - gitlab_group_label_create_purging is changed
          - gitlab_group_label_create_purging.labels.added|length == 1
          - gitlab_group_label_create_purging.labels.untouched|length == 0
          - gitlab_group_label_create_purging.labels.removed|length == 1
          - gitlab_group_label_create_purging.labels.updated|length == 0
          - gitlab_group_label_create_purging.labels.added[0] == "{{ gitlab_second_label }}"
          - gitlab_group_label_create_purging.labels.removed[0] == "{{ gitlab_first_label }}"

  ###
  ### Project label
  ###

    - name: Create {{ gitlab_project_name }}
      gitlab_project:
        api_url: "{{ gitlab_host }}"
        validate_certs: true
        api_token: "{{ gitlab_api_token }}"
        name: "{{ gitlab_project_name }}"
        group: "{{ gitlab_project_group }}"
        default_branch: "{{ gitlab_branch }}"
        initialize_with_readme: true
        state: present

    - name: Purge all labels for check_mode test
      gitlab_label:
        api_url: "{{ gitlab_host }}"
        api_token: "{{ gitlab_api_token }}"
        project: "{{ gitlab_project_group }}/{{ gitlab_project_name }}"
        purge: true

    - name: Add a label in check_mode
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        project: "{{ gitlab_project_group }}/{{ gitlab_project_name }}"
        labels:
          - name: "{{ gitlab_second_label }}"
            color: "{{ gitlab_second_label_color }}"
      check_mode: true
      register: gitlab_first_label_state

    - name: Check_mode state must be changed
      assert:
        that:
          - gitlab_first_label_state is changed

    - name: Create label {{ gitlab_first_label }} and {{ gitlab_second_label }}
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        project: "{{ gitlab_project_group }}/{{ gitlab_project_name }}"
        labels:
          - name: "{{ gitlab_first_label }}"
            color: "{{ gitlab_first_label_color }}"
            description: "{{ gitlab_first_label_description }}"
            priority: "{{ gitlab_first_label_priority }}"
          - name: "{{ gitlab_second_label }}"
            color: "{{ gitlab_second_label_color }}"
        state: present
      register: gitlab_first_label_create

    - name: Test Label Created
      assert:
        that:
          - gitlab_first_label_create is changed
          - gitlab_first_label_create.labels.added|length == 2
          - gitlab_first_label_create.labels.untouched|length == 0
          - gitlab_first_label_create.labels.removed|length == 0
          - gitlab_first_label_create.labels.updated|length == 0
          - gitlab_first_label_create.labels.added[0] == "{{ gitlab_first_label }}"
          - gitlab_first_label_create.labels.added[1] == "{{ gitlab_second_label }}"

    - name: Create Label ( Idempotency test )
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        project: "{{ gitlab_project_group }}/{{ gitlab_project_name }}"
        labels:
          - name: "{{ gitlab_first_label }}"
            color: "{{ gitlab_first_label_color }}"
            description: "{{ gitlab_first_label_description }}"
            priority: "{{ gitlab_first_label_priority }}"
        state: present
      register: gitlab_first_label_create_idempotence

    - name: Test Create Label is Idempotent
      assert:
        that:
          - gitlab_first_label_create_idempotence is not changed

    - name: Update Label {{ gitlab_first_label }} changing color
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        project: "{{ gitlab_project_group }}/{{ gitlab_project_name }}"
        labels:
          - name: "{{ gitlab_first_label }}"
            color: "{{ gitlab_second_label_color }}"
        state: present
      register: gitlab_first_label_update

    - name: Test Label Updated
      assert:
        that:
          - gitlab_first_label_update.labels.added|length == 0
          - gitlab_first_label_update.labels.untouched|length == 0
          - gitlab_first_label_update.labels.removed|length == 0
          - gitlab_first_label_update.labels.updated|length == 1
          - gitlab_first_label_update.labels.updated[0] == "{{ gitlab_first_label }}"

    - name: Change label {{ gitlab_second_label }} name to {{ gitlab_second_label_new_name }}
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        project: "{{ gitlab_project_group }}/{{ gitlab_project_name }}"
        labels:
          - name: "{{ gitlab_second_label }}"
            new_name: "{{ gitlab_second_label_new_name }}"
        state: present
      register: gitlab_first_label_new_name

    - name: Test Label name changed
      assert:
        that:
          - gitlab_first_label_new_name.labels.added|length == 0
          - gitlab_first_label_new_name.labels.untouched|length == 0
          - gitlab_first_label_new_name.labels.removed|length == 0
          - gitlab_first_label_new_name.labels.updated|length == 1
          - gitlab_first_label_new_name.labels.updated[0] == "{{ gitlab_second_label }}"

    - name: Change label name back to {{ gitlab_second_label }}
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        project: "{{ gitlab_project_group }}/{{ gitlab_project_name }}"
        labels:
          - name: "{{ gitlab_second_label_new_name }}"
            new_name: "{{ gitlab_second_label }}"
        state: present
      register: gitlab_first_label_orig_name

    - name: Test Label name changed back
      assert:
        that:
          - gitlab_first_label_orig_name.labels.added|length == 0
          - gitlab_first_label_orig_name.labels.untouched|length == 0
          - gitlab_first_label_orig_name.labels.removed|length == 0
          - gitlab_first_label_orig_name.labels.updated|length == 1
          - gitlab_first_label_orig_name.labels.updated[0] == "{{ gitlab_second_label_new_name }}"

    - name: Update Label Test ( Additions )
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        project: "{{ gitlab_project_group }}/{{ gitlab_project_name }}"
        labels:
          - name: "{{ gitlab_second_label }}"
            description: "{{ gitlab_first_label_description }}"
            priority: "{{ gitlab_first_label_priority }}"
        state: present
      register: gitlab_first_label_update_additions

    - name: Test Label Updated ( Additions )
      assert:
        that:
          - gitlab_first_label_update_additions.labels.added|length == 0
          - gitlab_first_label_update_additions.labels.untouched|length == 0
          - gitlab_first_label_update_additions.labels.removed|length == 0
          - gitlab_first_label_update_additions.labels.updated|length == 1
          - gitlab_first_label_update_additions.labels.updated[0] == "{{ gitlab_second_label }}"

    - name: Delete Label {{ gitlab_second_label }}
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        project: "{{ gitlab_project_group }}/{{ gitlab_project_name }}"
        labels:
          - name: "{{ gitlab_second_label }}"
        state: absent
      register: gitlab_first_label_delete

    - name: Test label is deleted
      assert:
        that:
          - gitlab_first_label_delete is changed
          - gitlab_first_label_delete.labels.added|length == 0
          - gitlab_first_label_delete.labels.untouched|length == 0
          - gitlab_first_label_delete.labels.removed|length == 1
          - gitlab_first_label_delete.labels.updated|length == 0
          - gitlab_first_label_delete.labels.removed[0] == "{{ gitlab_second_label }}"

    - name: Create label {{ gitlab_second_label }} again purging the other
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        project: "{{ gitlab_project_group }}/{{ gitlab_project_name }}"
        purge: true
        labels:
          - name: "{{ gitlab_second_label }}"
            color: "{{ gitlab_second_label_color }}"
        state: present
      register: gitlab_first_label_create_purging

    - name: Test Label Created again
      assert:
        that:
          - gitlab_first_label_create_purging is changed
          - gitlab_first_label_create_purging.labels.added|length == 1
          - gitlab_first_label_create_purging.labels.untouched|length == 0
          - gitlab_first_label_create_purging.labels.removed|length == 1
          - gitlab_first_label_create_purging.labels.updated|length == 0
          - gitlab_first_label_create_purging.labels.added[0] == "{{ gitlab_second_label }}"
          - gitlab_first_label_create_purging.labels.removed[0] == "{{ gitlab_first_label }}"

  always:
    - name: Delete Labels
      gitlab_label:
        api_token: "{{ gitlab_api_token }}"
        api_url: "{{ gitlab_host }}"
        project: "{{ gitlab_project_group }}/{{ gitlab_project_name }}"
        purge: true
        labels:
          - name: "{{ gitlab_first_label }}"
          - name: "{{ gitlab_second_label }}"
        state: absent
      register: gitlab_first_label_always_delete

    - name: Test label are deleted
      assert:
        that:
          - gitlab_first_label_always_delete is changed
          - gitlab_first_label_always_delete.labels.added|length == 0
          - gitlab_first_label_always_delete.labels.untouched|length == 0
          - gitlab_first_label_always_delete.labels.removed|length > 0
          - gitlab_first_label_always_delete.labels.updated|length == 0

    - name: Clean up {{ gitlab_project_name }}
      gitlab_project:
        api_url: "{{ gitlab_host }}"
        validate_certs: false
        api_token: "{{ gitlab_api_token }}"
        name: "{{ gitlab_project_name }}"
        group: "{{ gitlab_project_group }}"
        state: absent

    - name: Clean up {{ gitlab_project_group }}
      gitlab_group:
        api_url: "{{ gitlab_host }}"
        validate_certs: true
        api_token: "{{ gitlab_api_token }}"
        name: "{{ gitlab_project_group }}"
        state: absent
