---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- when: has_java_keytool
  block:
    - name: prep pkcs12 file
      ansible.builtin.copy:
        src: "{{ test_pkcs12_path }}"
        dest: "{{ remote_tmp_dir }}/{{ test_pkcs12_path }}"

    - name: import pkcs12
      community.general.java_cert:
        pkcs12_path: "{{ remote_tmp_dir }}/{{ test_pkcs12_path }}"
        pkcs12_password: changeit
        pkcs12_alias: default
        cert_alias: default
        keystore_path: "{{ remote_tmp_dir }}/{{ test_keystore_path }}"
        keystore_pass: changeme_keystore
        keystore_create: true
        state: present
      register: result_success

    - name: verify success
      ansible.builtin.assert:
        that:
          - result_success is successful

    - name: import pkcs12 without alias params
      community.general.java_cert:
        pkcs12_path: "{{ remote_tmp_dir }}/{{ test_pkcs12_path }}"
        pkcs12_password: changeit
        keystore_path: "{{ remote_tmp_dir }}/{{ test_keystore_path }}"
        keystore_pass: changeme_keystore
        keystore_create: true
        state: present
      register: result_success_excl_aliases

    - name: verify success
      ansible.builtin.assert:
        that:
          - result_success_excl_aliases is successful

    - name: import pkcs12 with wrong password
      community.general.java_cert:
        pkcs12_path: "{{ remote_tmp_dir }}/{{ test_pkcs12_path }}"
        pkcs12_password: wrong_pass
        pkcs12_alias: default
        cert_alias: default_new
        keystore_path: "{{ remote_tmp_dir }}/{{ test_keystore_path }}"
        keystore_pass: changeme_keystore
        keystore_create: true
        state: present
      ignore_errors: true
      register: result_wrong_pass

    - name: verify fail with wrong import password
      ansible.builtin.assert:
        that:
          - result_wrong_pass is failed

    - name: test fail on mutually exclusive params
      community.general.java_cert:
        cert_path: ca.crt
        pkcs12_path: "{{ remote_tmp_dir }}/{{ test_pkcs12_path }}"
        cert_alias: default
        keystore_path: "{{ remote_tmp_dir }}/{{ test_keystore_path }}"
        keystore_pass: changeme_keystore
        keystore_create: true
        state: present
      ignore_errors: true
      register: result_excl_params

    - name: verify failed exclusive params
      ansible.builtin.assert:
        that:
          - result_excl_params is failed

    - name: test fail on missing required params
      community.general.java_cert:
        keystore_path: "{{ remote_tmp_dir }}/{{ test_keystore_path }}"
        keystore_pass: changeme_keystore
        state: absent
      ignore_errors: true
      register: result_missing_required_param

    - name: verify failed missing required params
      ansible.builtin.assert:
        that:
          - result_missing_required_param is failed

    - name: delete object based on cert_alias parameter
      community.general.java_cert:
        keystore_path: "{{ remote_tmp_dir }}/{{ test_keystore_path }}"
        keystore_pass: changeme_keystore
        cert_alias: default
        state: absent
      ignore_errors: true
      register: result_alias_deleted

    - name: verify object successfully deleted
      ansible.builtin.assert:
        that:
          - result_alias_deleted is successful

    - name: include extended test suite
      import_tasks: state_change.yml

    - name: cleanup environment
      ansible.builtin.file:
        path: "{{ item }}"
        state: absent
      loop:
        - "{{ remote_tmp_dir }}/{{ test_pkcs12_path }}"
        - "{{ remote_tmp_dir }}/{{ test_keystore_path }}"
        - "{{ test_keystore2_path }}"
        - "{{ test_cert_path }}"
        - "{{ test_key_path }}"
        - "{{ test_csr_path }}"
        - "{{ test_cert2_path }}"
        - "{{ test_key2_path }}"
        - "{{ test_csr2_path }}"
        - "{{ test_pkcs_path }}"
        - "{{ test_pkcs2_path }}"
