---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Get security group information and register it in a variable
  scaleway_security_group_info:
    region: par1
  register: security_groups

- name: Display security_groups variable
  debug:
    var: security_groups

- name: Ensure retrieval of security groups info is success
  assert:
    that:
      - security_groups is success

- name: Get security group information and register it in a variable (AMS1)
  scaleway_security_group_info:
    region: ams1
  register: ams1_security_groups

- name: Display security_groups variable (AMS1)
  debug:
    var: ams1_security_groups

- name: Ensure retrieval of security groups info is success (AMS1)
  assert:
    that:
      - ams1_security_groups is success
