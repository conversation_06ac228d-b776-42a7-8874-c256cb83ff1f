<!--
Copyright (c) Ansible Project
GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
SPDX-License-Identifier: GPL-3.0-or-later
-->
# Running keycloak module authentication integration test

To run the Keycloak module authentication integration test, start a keycloak server using <PERSON>er or Podman:

```sh
    podman|docker run -d --rm --name mykeycloak -p 8080:8080 -e KEYCLOAK_ADMIN=admin -e KEYCLOAK_ADMIN_PASSWORD=password quay.io/keycloak/keycloak:latest start-dev --http-relative-path /auth
```

Source Ansible env-setup from ansible github repository.

Run the integration tests:

```sh
    ansible-test integration keycloak_role --python 3.10 --allow-unsupported
```

To cleanup, run:

```sh
    podman|docker stop mykeycloak
```
