---
# Copyright (c) 2022, <PERSON><PERSON><PERSON> (@bratwurzt)
# GNU General Public License v3.0+ (see COPYING or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

url: http://localhost:8080/auth
admin_realm: master
admin_user: admin
admin_password: password
realm: myrealm
client_id: myclient
role: myrole
description_1: desc 1
description_2: desc 2

keycloak_username: test
keycloak_service_account_client_id: "{{ client_id }}"
keycloak_user_realm_roles:
  - name: offline_access
  - name: "{{ role }}"
keycloak_client_role: test
keycloak_user_client_roles:
  - client_id: "{{ client_id }}"
    roles:
      - name: "{{ keycloak_client_role }}"
  - client_id: "{{ realm }}-realm"
    roles:
      - name: view-users
      - name: query-users
keycloak_user_attributes:
  - name: attr1
    values:
      - value1s
    state: present
  - name: attr2
    values:
      - value2s
    state: present
  - name: attr3
    values:
      - value3s
    state: present
keycloak_user_groups:
  - name: test
    state: present
  - name: test2
