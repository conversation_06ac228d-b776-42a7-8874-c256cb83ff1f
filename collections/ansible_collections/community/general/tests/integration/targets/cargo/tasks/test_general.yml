---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Ensure application helloworld is uninstalled
  community.general.cargo:
    state: absent
    name: helloworld
  register: uninstall_absent_helloworld

- name: Install application helloworld
  community.general.cargo:
    name: helloworld
  register: install_absent_helloworld

- name: Install application helloworld again
  community.general.cargo:
    name: helloworld
  register: install_present_helloworld
  ignore_errors: true

- name: Uninstall application helloworld
  community.general.cargo:
    state: absent
    name: helloworld
  register: uninstall_present_helloworld

- name: Check assertions helloworld
  assert:
    that:
      - uninstall_absent_helloworld is not changed
      - install_absent_helloworld is changed
      - install_present_helloworld is not changed
      - uninstall_present_helloworld is changed
