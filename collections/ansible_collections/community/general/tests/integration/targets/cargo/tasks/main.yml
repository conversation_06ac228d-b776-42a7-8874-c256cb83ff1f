---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- import_tasks: setup.yml
- name: Set default environment
  set_fact:
    cargo_environment: {}
- name: Set special environment to work around cargo bugs
  set_fact:
    cargo_environment:
      # See https://github.com/rust-lang/cargo/issues/10230#issuecomment-**********:
      CARGO_NET_GIT_FETCH_WITH_CLI: "true"
  when: has_cargo | default(false) and ansible_distribution == 'Alpine'
- block:
    - import_tasks: test_general.yml
    - import_tasks: test_version.yml
    - import_tasks: test_directory.yml
  environment: "{{ cargo_environment }}"
  when: has_cargo | default(false)
- import_tasks: test_rustup_cargo.yml
  when: (rustup_cargo_bin | default(false)) is truthy
