---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Install application helloworld-yliu 0.1.0
  community.general.cargo:
    name: helloworld-yliu
    version: 0.1.0
  register: install_helloworld_010

- name: Install application helloworld-yliu 0.1.0 (idempotent)
  community.general.cargo:
    name: helloworld-yliu
    version: 0.1.0
  register: install_helloworld_010_idem

- name: Upgrade helloworld-yliu 0.1.0
  community.general.cargo:
    name: helloworld-yliu
    state: latest
  register: upgrade_helloworld_010

- name: Upgrade helloworld-yliu 0.1.0 (idempotent)
  community.general.cargo:
    name: helloworld-yliu
    state: latest
  register: upgrade_helloworld_010_idem

- name: Downgrade helloworld-yliu 0.1.0
  community.general.cargo:
    name: helloworld-yliu
    version: 0.1.0
  register: downgrade_helloworld_010

- name: Downgrade helloworld-yliu 0.1.0 (idempotent)
  community.general.cargo:
    name: helloworld-yliu
    version: 0.1.0
  register: downgrade_helloworld_010_idem

- name: Check assertions helloworld-yliu
  assert:
    that:
      - install_helloworld_010 is changed
      - install_helloworld_010_idem is not changed
      - upgrade_helloworld_010 is changed
      - upgrade_helloworld_010_idem is not changed
      - downgrade_helloworld_010 is changed
      - downgrade_helloworld_010_idem is not changed
