---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# <AUTHOR> <EMAIL>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Create container_namespace
  community.general.scaleway_container_namespace:
    state: present
    name: '{{ container_namespace_name }}'
    region: '{{ scaleway_region }}'
    project_id: '{{ scw_project }}'
    description: '{{ description }}'
  register: integration_container_namespace

- name: Create container
  community.general.scaleway_container:
    state: present
    name: '{{ name }}'
    region: '{{ scaleway_region }}'
    namespace_id: '{{ integration_container_namespace.container_namespace.id }}'
    registry_image: '{{ image }}'
    description: '{{ description }}'
    environment_variables: '{{ environment_variables }}'
    secret_environment_variables: '{{ secret_environment_variables }}'
    port: '{{ port }}'

- name: Get container info
  community.general.scaleway_container_info:
    name: '{{ name }}'
    region: '{{ scaleway_region }}'
    namespace_id: '{{ integration_container_namespace.container_namespace.id }}'
  register: cn_info_task

- ansible.builtin.debug:
    var: cn_info_task

- name: Check module call result
  ansible.builtin.assert:
    that:
      - cn_info_task is success
      - cn_info_task is not changed

- name: Delete container
  community.general.scaleway_container:
    state: absent
    name: '{{ name }}'
    region: '{{ scaleway_region }}'
    description: '{{ description }}'
    namespace_id: '{{ integration_container_namespace.container_namespace.id }}'
    registry_image: '{{ image }}'

- name: Delete container namespace
  community.general.scaleway_container_namespace:
    state: absent
    name: '{{ container_namespace_name }}'
    region: '{{ scaleway_region }}'
    description: '{{ description }}'
    project_id: '{{ scw_project }}'
