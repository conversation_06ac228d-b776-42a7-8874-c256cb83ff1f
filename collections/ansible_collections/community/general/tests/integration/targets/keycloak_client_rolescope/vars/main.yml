---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

url: http://localhost:8080/auth
admin_realm: master
admin_user: admin
admin_password: password
realm: myrealm


client_name_private: backend-client-private
client_role_admin: client-role-admin
client_role_user: client-role-user
client_role_not_exists: client-role-missing

client_name_public: frontend-client-public


realm_role_admin: realm-role-admin
realm_role_user: realm-role-user
realm_role_not_exists: client-role-missing


client_attributes1: {"backchannel.logout.session.required": true, "backchannel.logout.revoke.offline.tokens": false, "client.secret.creation.time": 0}
