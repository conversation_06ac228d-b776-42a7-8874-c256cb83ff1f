---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later
- name: Wait for Keycloak
  uri:
    url: "{{ url }}/admin/"
    status_code: 200
    validate_certs: false
  register: result
  until: result.status == 200
  retries: 10
  delay: 10

- name: Delete realm if exists
  community.general.keycloak_realm:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    state: absent

- name: Create realm
  community.general.keycloak_realm:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    id: "{{ realm }}"
    realm: "{{ realm }}"
    state: present

- name: Create a Keycloak realm role
  community.general.keycloak_role:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    name: "{{ item }}"
    realm: "{{ realm }}"
  with_items:
    - "{{ realm_role_admin }}"
    - "{{ realm_role_user }}"

- name: Client private
  community.general.keycloak_client:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_private }}"
    state: present
    redirect_uris:
      - "https://my-backend-api.c.org/"
    fullScopeAllowed: true
    attributes: '{{client_attributes1}}'
    public_client: false

- name: Create a Keycloak client role
  community.general.keycloak_role:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    name: "{{ item }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_private }}"
  with_items:
    - "{{ client_role_admin }}"
    - "{{ client_role_user }}"

- name: Client public
  community.general.keycloak_client:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_public }}"
    redirect_uris:
      - "https://my-onepage-app-frontend.c.org/"
    attributes: '{{client_attributes1}}'
    full_scope_allowed: false
    public_client: true


- name: Map roles to public client
  community.general.keycloak_client_rolescope:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_public }}"
    client_scope_id: "{{ client_name_private }}"
    role_names:
      - "{{ client_role_admin }}"
      - "{{ client_role_user }}"
  register: result

- name: Assert mapping created
  assert:
    that:
      - result is changed
      - result.end_state | length == 2

- name: remap role user to public client
  community.general.keycloak_client_rolescope:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_public }}"
    client_scope_id: "{{ client_name_private }}"
    role_names:
      - "{{ client_role_user }}"
  register: result

- name: Assert mapping created
  assert:
    that:
      - result is not changed
      - result.end_state | length == 2

- name: Remove Map role admin to public client
  community.general.keycloak_client_rolescope:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_public }}"
    client_scope_id: "{{ client_name_private }}"
    role_names:
      - "{{ client_role_admin }}"
    state: absent
  register: result

- name: Assert mapping deleted
  assert:
    that:
      - result is changed
      - result.end_state | length == 1
      - result.end_state[0].name == client_role_user

- name: Map missing roles to public client
  community.general.keycloak_client_rolescope:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_public }}"
    client_scope_id: "{{ client_name_private }}"
    role_names:
      - "{{ client_role_admin }}"
      - "{{ client_role_not_exists }}"
  ignore_errors: true
  register: result

- name: Assert failed mapping missing role
  assert:
    that:
      - result is failed

- name: Map roles duplicate
  community.general.keycloak_client_rolescope:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_public }}"
    client_scope_id: "{{ client_name_private }}"
    role_names:
      - "{{ client_role_admin }}"
      - "{{ client_role_admin }}"
  register: result

- name: Assert result
  assert:
    that:
      - result is changed
      - result.end_state | length == 2

- name: Map roles to private client
  community.general.keycloak_client_rolescope:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_private }}"
    role_names:
      - "{{ realm_role_admin }}"
  ignore_errors: true
  register: result

- name: Assert failed mapping role to full scope client
  assert:
    that:
      - result is failed

- name: Map realm role to public client
  community.general.keycloak_client_rolescope:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_public }}"
    role_names:
      - "{{ realm_role_admin }}"
  register: result

- name: Assert result
  assert:
    that:
      - result is changed
      - result.end_state | length == 1

- name: Map two realm roles to public client
  community.general.keycloak_client_rolescope:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_public }}"
    role_names:
      - "{{ realm_role_admin }}"
      - "{{ realm_role_user }}"
  register: result

- name: Assert result
  assert:
    that:
      - result is changed
      - result.end_state | length == 2

- name: Unmap all realm roles to public client
  community.general.keycloak_client_rolescope:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_public }}"
    role_names:
      - "{{ realm_role_admin }}"
      - "{{ realm_role_user }}"
    state: absent
  register: result

- name: Assert result
  assert:
    that:
      - result is changed
      - result.end_state | length == 0

- name: Map missing realm role to public client
  community.general.keycloak_client_rolescope:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_public }}"
    role_names:
      - "{{ realm_role_not_exists }}"
  ignore_errors: true
  register: result

- name: Assert failed mapping missing realm role
  assert:
    that:
      - result is failed

- name: Check-mode try to Map realm roles to public client
  community.general.keycloak_client_rolescope:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_public }}"
    role_names:
      - "{{ realm_role_admin }}"
      - "{{ realm_role_user }}"
  check_mode: true
  register: result

- name: Assert result
  assert:
    that:
      - result is changed
      - result.end_state | length == 2

- name: Check-mode step two, check if change where applied
  community.general.keycloak_client_rolescope:
    auth_keycloak_url: "{{ url }}"
    auth_realm: "{{ admin_realm }}"
    auth_username: "{{ admin_user }}"
    auth_password: "{{ admin_password }}"
    realm: "{{ realm }}"
    client_id: "{{ client_name_public }}"
    role_names: []
  register: result

- name: Assert result
  assert:
    that:
      - result is not changed
      - result.end_state | length == 0
