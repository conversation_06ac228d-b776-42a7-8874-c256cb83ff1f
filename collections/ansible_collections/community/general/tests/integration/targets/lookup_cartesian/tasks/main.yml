---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Test cartesian lookup
  debug: var=item
  register: product
  with_community.general.cartesian:
    - - A
      - B
      - C
    - - '1'
      - '2'
      - '3'
- name: Verify cartesian lookup
  assert:
    that:
      - product.results[0]['item'] == ["A", "1"]
      - product.results[1]['item'] == ["A", "2"]
      - product.results[2]['item'] == ["A", "3"]
      - product.results[3]['item'] == ["B", "1"]
      - product.results[4]['item'] == ["B", "2"]
      - product.results[5]['item'] == ["B", "3"]
      - product.results[6]['item'] == ["C", "1"]
      - product.results[7]['item'] == ["C", "2"]
      - product.results[8]['item'] == ["C", "3"]
