---
# Copyright (c) 2016, Da<PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: install requirements for RHEL
  package:
    name: policycoreutils-python
  when:
    - ansible_distribution == 'RedHat'
    - ansible_distribution_major_version|int < 8

- name: install requirements for rhel8 beta
  package:
    name: python3-policycoreutils
  when:
    - ansible_distribution == 'RedHat'
    - ansible_distribution_major_version|int >= 8

- name: Ensure we start with a clean state
  sefcontext:
    path: '/tmp/foo/bar(/.*)?'
    setype: httpd_sys_content_t
    state: absent

- name: Ensure we start with a clean state
  sefcontext:
    path: /tmp/foo
    state: absent

- name: Set SELinux file context of foo/bar
  sefcontext:
    path: '/tmp/foo/bar(/.*)?'
    setype: httpd_sys_content_t
    state: present
    reload: false
  register: first

- assert:
    that:
      - first is changed
      - first.setype == 'httpd_sys_content_t'

- name: Set SELinux file context of foo/bar (again)
  sefcontext:
    path: '/tmp/foo/bar(/.*)?'
    setype: httpd_sys_content_t
    state: present
    reload: false
  register: second

- assert:
    that:
      - second is not changed
      - second.setype == 'httpd_sys_content_t'

- name: Change SELinux file context of foo/bar
  sefcontext:
    path: '/tmp/foo/bar(/.*)?'
    setype: unlabeled_t
    state: present
    reload: false
  register: third

- assert:
    that:
      - third is changed
      - third.setype == 'unlabeled_t'

- name: Change SELinux file context of foo/bar (again)
  sefcontext:
    path: '/tmp/foo/bar(/.*)?'
    setype: unlabeled_t
    state: present
    reload: false
  register: fourth

- assert:
    that:
      - fourth is not changed
      - fourth.setype == 'unlabeled_t'

- name: Delete SELinux file context of foo/bar
  sefcontext:
    path: '/tmp/foo/bar(/.*)?'
    setype: httpd_sys_content_t
    state: absent
    reload: false
  register: fifth

- assert:
    that:
      - fifth is changed
      - fifth.setype == 'httpd_sys_content_t'

- name: Delete SELinux file context of foo/bar (again)
  sefcontext:
    path: '/tmp/foo/bar(/.*)?'
    setype: unlabeled_t
    state: absent
    reload: false
  register: sixth

- assert:
    that:
      - sixth is not changed
      - sixth.setype == 'unlabeled_t'

- name: Set SELinux file context path substitution of foo
  sefcontext:
    path: /tmp/foo
    substitute: /home
    state: present
    reload: false
  register: subst_first

- assert:
    that:
      - subst_first is changed
      - subst_first.substitute == '/home'

- name: Set SELinux file context path substitution of foo (again)
  sefcontext:
    path: /tmp/foo
    substitute: /home
    state: present
    reload: false
  register: subst_second

- assert:
    that:
      - subst_second is not changed
      - subst_second.substitute == '/home'

- name: Change SELinux file context path substitution of foo
  sefcontext:
    path: /tmp/foo
    substitute: /boot
    state: present
    reload: false
  register: subst_third

- assert:
    that:
      - subst_third is changed
      - subst_third.substitute == '/boot'

- name: Change SELinux file context path substitution of foo (again)
  sefcontext:
    path: /tmp/foo
    substitute: /boot
    state: present
    reload: false
  register: subst_fourth

- assert:
    that:
      - subst_fourth is not changed
      - subst_fourth.substitute == '/boot'

- name: Try to delete non-existing SELinux file context path substitution of foo
  sefcontext:
    path: /tmp/foo
    substitute: /dev
    state: absent
    reload: false
  register: subst_fifth

- assert:
    that:
      - subst_fifth is not changed
      - subst_fifth.substitute == '/dev'

- name: Delete SELinux file context path substitution of foo
  sefcontext:
    path: /tmp/foo
    substitute: /boot
    state: absent
    reload: false
  register: subst_sixth

- assert:
    that:
      - subst_sixth is changed
      - subst_sixth.substitute == '/boot'

- name: Delete SELinux file context path substitution of foo (again)
  sefcontext:
    path: /tmp/foo
    substitute: /boot
    state: absent
    reload: false
  register: subst_seventh

- assert:
    that:
      - subst_seventh is not changed
      - subst_seventh.substitute == '/boot'

- name: Set SELinux file context path substitution of foo
  sefcontext:
    path: /tmp/foo
    substitute: /home
    state: present
    reload: false
  register: subst_eighth

- assert:
    that:
      - subst_eighth is changed
      - subst_eighth.substitute == '/home'

- name: Delete SELinux file context path substitution of foo
  sefcontext:
    path: /tmp/foo
    state: absent
    reload: false
  register: subst_ninth

- assert:
    that:
      - subst_ninth is changed

- name: Delete SELinux file context path substitution of foo (again)
  sefcontext:
    path: /tmp/foo
    state: absent
    reload: false
  register: subst_tenth

- assert:
    that:
      - subst_tenth is not changed
