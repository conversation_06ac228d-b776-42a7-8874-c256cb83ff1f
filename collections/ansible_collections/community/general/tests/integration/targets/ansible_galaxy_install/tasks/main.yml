---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

###################################################
- name: Make directory install_c
  ansible.builtin.file:
    path: "{{ remote_tmp_dir }}/install_c"
    state: directory

- name: Install collection netbox.netbox
  community.general.ansible_galaxy_install:
    type: collection
    name: netbox.netbox
    dest: "{{ remote_tmp_dir }}/install_c"
  register: install_c0

- name: Assert collection netbox.netbox was installed
  assert:
    that:
      - install_c0 is changed
      - '"netbox.netbox" in install_c0.new_collections'

- name: Install collection netbox.netbox (again)
  community.general.ansible_galaxy_install:
    type: collection
    name: netbox.netbox
    dest: "{{ remote_tmp_dir }}/install_c"
  register: install_c1

- name: Assert collection was not installed
  assert:
    that:
      - install_c1 is not changed

###################################################
- name: Make directory install_r
  ansible.builtin.file:
    path: "{{ remote_tmp_dir }}/install_r"
    state: directory

- name: Install role ansistrano.deploy
  community.general.ansible_galaxy_install:
    type: role
    name: ansistrano.deploy
    dest: "{{ remote_tmp_dir }}/install_r"
  register: install_r0

- name: Assert collection ansistrano.deploy was installed
  assert:
    that:
      - install_r0 is changed
      - '"ansistrano.deploy" in install_r0.new_roles'

- name: Install role ansistrano.deploy (again)
  community.general.ansible_galaxy_install:
    type: role
    name: ansistrano.deploy
    dest: "{{ remote_tmp_dir }}/install_r"
  register: install_r1

- name: Assert role was not installed
  assert:
    that:
      - install_r1 is not changed

###################################################
- name: Set requirements file path
  set_fact:
    reqs_file: '{{ remote_tmp_dir }}/reqs.yaml'

- name: Copy requirements file
  copy:
    src: 'files/test.yml'
    dest: '{{ reqs_file }}'

- name: Install from requirements file
  community.general.ansible_galaxy_install:
    type: both
    requirements_file: "{{ reqs_file }}"
  register: install_rq0
  ignore_errors: true

- name: Assert requirements file was installed
  assert:
    that:
      - install_rq0 is changed
      - '"geerlingguy.java" in install_rq0.new_roles'
      - '"geerlingguy.php_roles" in install_rq0.new_collections'

- name: Install from requirements file (again)
  community.general.ansible_galaxy_install:
    type: both
    requirements_file: "{{ reqs_file }}"
  register: install_rq1
  ignore_errors: true

- name: Assert requirements file was not installed
  assert:
    that:
      - install_rq1 is not changed

###################################################
- name: Make directory upgrade_c
  ansible.builtin.file:
    path: "{{ remote_tmp_dir }}/upgrade_c"
    state: directory

- name: Install collection netbox.netbox 3.17.0
  community.general.ansible_galaxy_install:
    type: collection
    name: netbox.netbox:3.17.0
    dest: "{{ remote_tmp_dir }}/upgrade_c"
  register: upgrade_c0

- name: Assert collection netbox.netbox was installed
  assert:
    that:
      - upgrade_c0 is changed
      - '"netbox.netbox" in upgrade_c0.new_collections'

- name: Upgrade collection netbox.netbox
  community.general.ansible_galaxy_install:
    state: latest
    type: collection
    name: netbox.netbox
    dest: "{{ remote_tmp_dir }}/upgrade_c"
  register: upgrade_c1

- name: Upgrade collection netbox.netbox (again)
  community.general.ansible_galaxy_install:
    state: latest
    type: collection
    name: netbox.netbox
    dest: "{{ remote_tmp_dir }}/upgrade_c"
  register: upgrade_c2

- name: Assert collection was not installed
  assert:
    that:
      - upgrade_c1 is changed
      - upgrade_c2 is not changed
