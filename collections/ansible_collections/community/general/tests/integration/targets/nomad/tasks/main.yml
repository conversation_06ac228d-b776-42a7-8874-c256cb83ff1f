---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Skip unsupported platforms
  meta: end_play
  # TODO: figure out why Alpine does not work!
  when: |
    ansible_distribution == 'CentOS' and ansible_distribution_major_version is not version('7', '>=')
    or ansible_distribution == 'Alpine'

- name: Install Nomad and test
  vars:
    nomad_version: 0.12.4
    nomad_uri: https://releases.hashicorp.com/nomad/{{ nomad_version }}/nomad_{{ nomad_version }}_{{ ansible_system | lower }}_{{ nomad_arch }}.zip
    nomad_cmd: '{{ remote_tmp_dir }}/nomad'
  block:

    - name: Install requests<2.20 (CentOS/RHEL 6)
      pip:
        name: requests<2.20
        extra_args: "-c {{ remote_constraints }}"
      register: result
      until: result is success
      when: ansible_distribution_file_variety|default() == 'RedHat' and ansible_distribution_major_version is version('6', '<=')

    - name: Install python-nomad
      pip:
        name: python-nomad
        extra_args: "-c {{ remote_constraints }}"
      register: result
      until: result is success

    - name: Install jmespath
      pip:
        name: jmespath
        extra_args: "-c {{ remote_constraints }}"
      register: result
      until: result is success

    - name: Generate privatekey
      community.crypto.openssl_privatekey:
        path: '{{ remote_tmp_dir }}/privatekey.pem'

    - name: Generate CSR
      community.crypto.openssl_csr:
        path: '{{ remote_tmp_dir }}/csr.csr'
        privatekey_path: '{{ remote_tmp_dir }}/privatekey.pem'
        subject:
          commonName: localhost

    - name: Generate selfsigned certificate
      register: selfsigned_certificate
      community.crypto.x509_certificate:
        path: '{{ remote_tmp_dir }}/cert.pem'
        csr_path: '{{ remote_tmp_dir }}/csr.csr'
        privatekey_path: '{{ remote_tmp_dir }}/privatekey.pem'
        provider: selfsigned
        selfsigned_digest: sha256

    - name: Install unzip
      package:
        name: unzip
      register: result
      until: result is success
      when: ansible_distribution != "MacOSX"

    - assert:
        that: ansible_architecture in ['i386', 'x86_64', 'amd64']

    - set_fact:
        nomad_arch: '386'
      when: ansible_architecture == 'i386'

    - set_fact:
        nomad_arch: amd64
      when: ansible_architecture in ['x86_64', 'amd64']

    - name: Download nomad binary
      unarchive:
        src: '{{ nomad_uri }}'
        dest: '{{ remote_tmp_dir }}'
        remote_src: true
      register: result
      until: result is success

    - vars:
        remote_dir: '{{ echo_remote_tmp_dir.stdout }}'
      block:

        - command: echo {{ remote_tmp_dir }}
          register: echo_remote_tmp_dir

        - name: Run tests integration
          block:
            - name: Start nomad (dev mode enabled)
              shell: nohup {{ nomad_cmd }} agent -dev </dev/null >/dev/null 2>&1 &

            - name: wait nomad up
              wait_for:
                host: localhost
                port: 4646
                delay: 10
                timeout: 60

            - import_tasks: nomad_job.yml
          always:

            - name: kill nomad
              shell: pkill nomad
