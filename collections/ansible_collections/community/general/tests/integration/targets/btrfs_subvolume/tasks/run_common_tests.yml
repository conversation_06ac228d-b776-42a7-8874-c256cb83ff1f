---
# Copyright (c) 2022, <PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see COPYING or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- ansible.builtin.include_tasks: 'test_subvolume_simple.yml'
- ansible.builtin.include_tasks: 'test_subvolume_nested.yml'
- ansible.builtin.include_tasks: 'test_subvolume_recursive.yml'
- ansible.builtin.include_tasks: 'test_subvolume_default.yml'

- ansible.builtin.include_tasks: 'test_snapshot_skip.yml'
- ansible.builtin.include_tasks: 'test_snapshot_clobber.yml'
- ansible.builtin.include_tasks: 'test_snapshot_error.yml'

- ansible.builtin.include_tasks: 'test_subvolume_whitespace.yml'
