---
# Copyright (c) 2022, <PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see COPYING or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Create a subvolume named 'container'
  community.general.btrfs_subvolume:
    automount: true
    filesystem_label: "{{ btrfs_subvolume_target_label }}"
    name: "/container"
    state: "present"

- name: Create a subvolume with whitespace in the name
  block:
    - name: Create a subvolume named 'container/my data'
      community.general.btrfs_subvolume:
        automount: true
        filesystem_label: "{{ btrfs_subvolume_target_label }}"
        name: "/container/my data"
        state: "present"
      register: result
    - name: Subvolume named 'container/my data' created
      ansible.builtin.assert:
        that:
          - result is changed
    - name: Create a subvolume named 'container/my data' (idempotency)
      community.general.btrfs_subvolume:
        automount: true
        filesystem_label: "{{ btrfs_subvolume_target_label }}"
        name: "/container/my data"
        state: "present"
      register: result
    - name: Subvolume named 'container/my data' created (idempotency)
      ansible.builtin.assert:
        that:
          - result is not changed

- name: Remove a subvolume with whitespace in the name
  block:
    - name: Remove a subvolume named 'container/my data'
      community.general.btrfs_subvolume:
        automount: true
        filesystem_label: "{{ btrfs_subvolume_target_label }}"
        name: "/container/my data"
        state: "absent"
      register: result
    - name: Subvolume named 'container/my data' removed
      ansible.builtin.assert:
        that:
          - result is changed

    - name: Remove a subvolume named 'container/my data' (idempotency)
      community.general.btrfs_subvolume:
        automount: true
        filesystem_label: "{{ btrfs_subvolume_target_label }}"
        name: "/container/my data"
        state: "absent"
      register: result
    - name: Subvolume named 'container/my data' removed (idempotency)
      ansible.builtin.assert:
        that:
          - result is not changed
