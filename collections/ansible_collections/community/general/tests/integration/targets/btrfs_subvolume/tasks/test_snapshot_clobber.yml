---
# Copyright (c) 2022, <PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see COPYING or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Create a snapshot, overwriting if one already exists at path
  block:
    - name: Create a snapshot named 'snapshot_clobber'
      community.general.btrfs_subvolume:
        automount: true
        filesystem_label: "{{ btrfs_subvolume_target_label }}"
        name: "/snapshot_clobber"
        snapshot_source: "/"
        snapshot_conflict: "clobber"
        state: "present"
      register: result
    - name: Snapshot 'snapshot_clobber' created
      ansible.builtin.assert:
        that:
          - result is changed

    - name: Create a snapshot named 'snapshot_clobber' (no idempotency)
      community.general.btrfs_subvolume:
        automount: true
        filesystem_label: "{{ btrfs_subvolume_target_label }}"
        name: "/snapshot_clobber"
        snapshot_source: "/"
        snapshot_conflict: "clobber"
        state: "present"
      register: result
    - name: Snapshot 'snapshot_clobber' created (no idempotency)
      ansible.builtin.assert:
        that:
          - result is changed

- name: Cleanup created snapshot
  community.general.btrfs_subvolume:
    automount: true
    filesystem_label: "{{ btrfs_subvolume_target_label }}"
    name: "/snapshot_clobber"
    state: "absent"
