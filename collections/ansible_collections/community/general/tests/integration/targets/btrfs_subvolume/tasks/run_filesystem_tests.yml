---
# Copyright (c) 2022, <PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see COPYING or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- ansible.builtin.include_tasks: 'test_filesystem_matching.yml'

- name: "Execute all test scenario for unmounted filesystem"
  ansible.builtin.include_tasks: 'run_common_tests.yml'

- name: "Execute test scenarios where non-root subvolume is mounted"
  block:
    - name: Create subvolume '/nonroot'
      community.general.btrfs_subvolume:
        automount: true
        name: "/nonroot"
        filesystem_label: "{{ btrfs_subvolume_target_label }}"
        state: "present"
      register: nonroot
    - name: "Mount subvolume '/nonroot'"
      ansible.posix.mount:
        src: "{{ nonroot.filesystem.devices | first }}"
        path: /mnt
        opts: "subvolid={{ nonroot.target_subvolume_id }}"
        fstype: btrfs
        state: mounted
    - name: "Run tests for explicit, mounted single device configuration"
      ansible.builtin.include_tasks: 'run_common_tests.yml'
    - name: "Unmount subvolume /nonroot"
      ansible.posix.mount:
        path: /mnt
        state: absent
