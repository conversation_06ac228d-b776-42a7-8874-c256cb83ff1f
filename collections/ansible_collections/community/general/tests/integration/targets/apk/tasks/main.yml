####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) 2024, <PERSON> <<EMAIL>>
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later
- name: Run apk tests on Alpine
  when: ansible_distribution in ['Alpine']
  block:
    - name: Ensure vim is not installed
      community.general.apk:
        name: vim
        state: absent

    - name: Install vim
      community.general.apk:
        name: vim
        state: present
      register: results

    - name: Ensure vim was installed
      ansible.builtin.assert:
        that:
          - results is changed
          - (results.packages | length) >= 1 # vim has dependencies, so depending on the base image this number may vary

    - name: Install vim again
      community.general.apk:
        name: vim
        state: present
      register: results

    - name: Ensure vim was not installed again
      ansible.builtin.assert:
        that:
          - results is not changed
          - (results.packages | default([]) | length) == 0

    - name: Ensure vim is not installed
      community.general.apk:
        name: vim
        state: absent
      register: results

    - name: Ensure vim was uninstalled
      ansible.builtin.assert:
        that:
          - results is changed
          - (results.packages | length) >= 1

    - name: Install vim without cache
      community.general.apk:
        name: vim
        state: present
        no_cache: true
      register: results

    - name: Ensure vim was installed without cache
      ansible.builtin.assert:
        that:
          - results is changed

    - name: Install vim again without cache
      community.general.apk:
        name: vim
        state: present
        no_cache: true
      register: results

    - name: Ensure vim was not installed again without cache
      ansible.builtin.assert:
        that:
          - results is not changed
          - (results.packages | default([]) | length) == 0

    - name: Ensure a bunch of packages aren't installed
      community.general.apk:
        name:
          - less
          - nano
          - vim
        state: absent

    - name: Install a bunch of packages
      community.general.apk:
        name:
          - less
          - nano
          - vim
        state: present
      register: results

    - name: Ensure a bunch of packages were installed
      ansible.builtin.assert:
        that:
          - results is changed
          - (results.packages | length) >= 3

    - name: Install a bunch of packages again
      community.general.apk:
        name:
          - less
          - nano
          - vim
        state: present
      register: results

    - name: Ensure a bunch of packages were not installed again
      ansible.builtin.assert:
        that:
          - results is not changed
          - (results.packages | default([]) | length) == 0

    - name: Ensure a bunch of packages are not installed
      community.general.apk:
        name:
          - less
          - nano
          - vim
        state: absent
      register: results

    - name: Ensure a bunch of packages were uninstalled
      ansible.builtin.assert:
        that:
          - results is changed
          - (results.packages | length) >= 3

    - name: Install a bunch of packages without cache
      community.general.apk:
        name:
          - less
          - nano
          - vim
        state: present
        no_cache: true
      register: results

    - name: Ensure a bunch of packages were installed without cache
      ansible.builtin.assert:
        that:
          - results is changed

    - name: Install a bunch of packages again without cache
      community.general.apk:
        name:
          - less
          - nano
          - vim
        state: present
        no_cache: true
      register: results

    - name: Ensure a bunch of packages were not installed again without cache
      ansible.builtin.assert:
        that:
          - results is not changed
          - (results.packages | default([]) | length) == 0
