---
# Copyright (c) 2024, <PERSON><PERSON><PERSON> (@apollo13)
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Create an auth method
  community.general.consul_auth_method:
    name: test
    type: jwt
    config:
      jwt_validation_pubkeys:
        - |
          -----B<PERSON>IN PUBLIC KEY-----
          MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu1SU1LfVLPHCozMxH2Mo
          4lgOEePzNm0tRgeLezV6ffAt0gunVTLw7onLRnrq0/IzW7yWR7QkrmBL7jTKEn5u
          +qKhbwKfBstIs+bMY2Zkp18gnTxKLxoS2tFczGkPLPgizskuemMghRniWaoLcyeh
          kd3qqGElvW/VDL5AaWTg0nLVkjRo9z+40RQzuVaE8AkAFmxZzow3x+VJYKdjykkJ
          0iT9wCS0DRTXu269V264Vf/3jvredZiKRkgwlL9xNAwxXFg0x/XFw005UWVRIkdg
          cKWTjpBP2dPwVZ4WWC+9aGVd+Gyn1o0CLelf4rEjGoXbAAEgAqeGUxrcIlbjXfbc
          mwIDAQAB
          -----END PUBLIC KEY-----

- name: Create a binding rule
  community.general.consul_binding_rule:
    name: test-binding
    description: my description
    auth_method: test
    bind_type: service
    bind_name: yolo
  register: result

- assert:
    that:
      - result is changed
      - result.binding_rule.AuthMethod == 'test'
      - "result.binding_rule.Description == 'test-binding: my description'"
      - result.operation == 'create'

- name: Update a binding rule
  community.general.consul_binding_rule:
    name: test-binding
    auth_method: test
    bind_name: yolo2
  register: result

- assert:
    that:
      - result is changed
      - "result.binding_rule.Description == 'test-binding: my description'"
      - result.operation == 'update'

- name: Update a binding rule (noop)
  community.general.consul_binding_rule:
    name: test-binding
    auth_method: test
  register: result

- assert:
    that:
      - result is not changed
      - "result.binding_rule.Description == 'test-binding: my description'"
      - result.operation is not defined

- name: Delete a binding rule
  community.general.consul_binding_rule:
    name: test-binding
    auth_method: test
    state: absent
  register: result
- assert:
    that:
      - result is changed
      - result.operation == 'remove'