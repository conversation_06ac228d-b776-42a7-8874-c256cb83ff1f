---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: list sessions
  consul_session:
    state: list
  register: result

- assert:
    that:
      - result is changed
      - "'sessions' in result"

- name: create a session
  consul_session:
    state: present
    name: testsession
  register: result

- assert:
    that:
      - result is changed
      - result['name'] == 'testsession'
      - "'session_id' in result"

- set_fact:
    session_id: "{{ result['session_id'] }}"

- name: list sessions after creation
  consul_session:
    state: list
  register: result

- set_fact:
    session_count: "{{ result['sessions'] | length }}"

- assert:
    that:
      - result is changed
      # selectattr not available on Jinja 2.2 provided by CentOS 6
      # hence the two following tasks (set_fact/assert) are used
      # - (result['sessions'] | selectattr('ID', 'match', '^' ~ session_id ~ '$') | first)['Name'] == 'testsession'

- name: search created session
  set_fact:
    test_session_found: true
  loop: "{{ result['sessions'] }}"
  when: "item.get('ID') == session_id and item.get('Name') == 'testsession'"

- name: ensure session was created
  assert:
    that:
      - test_session_found|default(false)

- name: fetch info about a session
  consul_session:
    state: info
    id: '{{ session_id }}'
  register: result

- assert:
    that:
      - result is changed

- name: ensure 'id' parameter is required when state=info
  consul_session:
    state: info
    name: test
  register: result
  ignore_errors: true

- assert:
    that:
      - result is failed

- name: delete a session
  consul_session:
    state: absent
    id: '{{ session_id }}'
  register: result

- assert:
    that:
      - result is changed

- name: list sessions after deletion
  consul_session:
    state: list
  register: result

- assert:
    that:
      - result is changed
      # selectattr and equalto not available on Jinja 2.2 provided by CentOS 6
      # hence the two following tasks (command/assert) are used
      # - (result['sessions'] | selectattr('ID', 'equalto', session_id) | list | length) == 0

- name: search deleted session
  command: echo 'session found'
  loop: "{{ result['sessions'] }}"
  when: "item.get('ID') == session_id and item.get('Name') == 'testsession'"
  register: search_deleted

- name: ensure session was deleted
  assert:
    that:
      - search_deleted is skipped  # each iteration is skipped
      - search_deleted is not changed  # and then unchanged

- name: ensure session can be created with a ttl
  consul_session:
    state: present
    name: session-with-ttl
    ttl: 180  # sec
  register: result

- assert:
    that:
      - result is changed
      - result['ttl'] == 180
