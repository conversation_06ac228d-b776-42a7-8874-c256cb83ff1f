---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# Pre-test setup
- name: create a vpc
  hwc_network_vpc:
    cidr: "*************/24"
    name: "ansible_network_vpc_test"
    state: present
  register: vpc
- name: delete a subnet
  hwc_vpc_subnet:
    vpc_id: "{{ vpc.id }}"
    cidr: "*************/26"
    gateway_ip: "**************"
    name: "ansible_network_subnet_test"
    dhcp_enable: true
    state: absent
# ---------------------------------------------------------
- name: create a subnet (check mode)
  hwc_vpc_subnet:
    vpc_id: "{{ vpc.id }}"
    cidr: "*************/26"
    gateway_ip: "**************"
    name: "ansible_network_subnet_test"
    dhcp_enable: true
    state: present
  check_mode: true
  register: result
- name: assert changed is true
  assert:
    that:
      - not result.id
      - result.changed
# ---------------------------------------------------------
- name: create a subnet
  hwc_vpc_subnet:
    vpc_id: "{{ vpc.id }}"
    cidr: "*************/26"
    gateway_ip: "**************"
    name: "ansible_network_subnet_test"
    dhcp_enable: true
    state: present
  register: result
- name: assert changed is true
  assert:
    that:
      result is changed
# ---------------------------------------------------------
- name: create a subnet (idemponent)
  hwc_vpc_subnet:
    vpc_id: "{{ vpc.id }}"
    cidr: "*************/26"
    gateway_ip: "**************"
    name: "ansible_network_subnet_test"
    dhcp_enable: true
    state: present
  check_mode: true
  register: result
- name: idemponent
  assert:
    that:
      - not result.changed
# ----------------------------------------------------------------------------
- name: create a subnet that already exists
  hwc_vpc_subnet:
    vpc_id: "{{ vpc.id }}"
    cidr: "*************/26"
    gateway_ip: "**************"
    name: "ansible_network_subnet_test"
    dhcp_enable: true
    state: present
  register: result
- name: assert changed is false
  assert:
    that:
      - result is not failed
      - result is not changed
# ---------------------------------------------------------
- name: delete a subnet (check mode)
  hwc_vpc_subnet:
    vpc_id: "{{ vpc.id }}"
    cidr: "*************/26"
    gateway_ip: "**************"
    name: "ansible_network_subnet_test"
    dhcp_enable: true
    state: absent
  check_mode: true
  register: result
- name: assert changed is true
  assert:
    that:
      - result is changed
# ---------------------------------------------------------
- name: delete a subnet
  hwc_vpc_subnet:
    vpc_id: "{{ vpc.id }}"
    cidr: "*************/26"
    gateway_ip: "**************"
    name: "ansible_network_subnet_test"
    dhcp_enable: true
    state: absent
  register: result
- name: assert changed is true
  assert:
    that:
      - result is changed
# ---------------------------------------------------------
- name: delete a subnet (idemponent)
  hwc_vpc_subnet:
    vpc_id: "{{ vpc.id }}"
    cidr: "*************/26"
    gateway_ip: "**************"
    name: "ansible_network_subnet_test"
    dhcp_enable: true
    state: absent
  check_mode: true
  register: result
- name: idemponent
  assert:
    that:
      - not result.changed
# ----------------------------------------------------------------------------
- name: delete a subnet that does not exist
  hwc_vpc_subnet:
    vpc_id: "{{ vpc.id }}"
    cidr: "*************/26"
    gateway_ip: "**************"
    name: "ansible_network_subnet_test"
    dhcp_enable: true
    state: absent
  register: result
- name: assert changed is false
  assert:
    that:
      - result is not failed
      - result is not changed
# --------------------------------------------------------
# Post-test teardown
- name: delete a vpc
  hwc_network_vpc:
    cidr: "*************/24"
    name: "ansible_network_vpc_test"
    state: absent
  register: vpc
