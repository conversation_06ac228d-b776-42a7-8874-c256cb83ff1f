{#
Copyright (c) Ansible Project
GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
SPDX-License-Identifier: GPL-3.0-or-later
#}

[supervisord]
pidfile={{ remote_dir }}/supervisord.pid
logfile={{ remote_dir }}/supervisord.log

[program:py1]
command={{ ansible_python.executable }} -i -u -
user={{ ansible_user_id }}
autostart=false
autorestart=false
stdout_logfile={{ remote_dir }}/py1.log
redirect_stderr=yes

[program:py2]
command={{ ansible_python.executable }} -i -u -
user={{ ansible_user_id }}
autostart=false
autorestart=false
stdout_logfile={{ remote_dir }}/py2.log
redirect_stderr=yes

[group:pys]
programs=py1,py2

[unix_http_server]
file={{ supervisord_sock_path.path }}/supervisord.sock
{% if credentials.username is defined and credentials.username|default(false, boolean=true) %}
username = {{ credentials.username }}
password = {{ credentials.password }}
{% endif %}

[inet_http_server]
port=127.0.0.1:9001
{% if credentials.username is defined and credentials.username|default(false, boolean=true) %}
username = {{ credentials.username }}
password = {{ credentials.password }}
{% endif %}

[supervisorctl]
serverurl=unix://{{ supervisord_sock_path.path }}/supervisord.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface
