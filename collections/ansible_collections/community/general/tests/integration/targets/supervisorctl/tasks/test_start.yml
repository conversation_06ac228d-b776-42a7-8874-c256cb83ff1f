---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: start py1 service (without auth)
  supervisorctl:
    name: 'pys:py1'
    state: started
    config: '{{ remote_dir }}/supervisord.conf'
  register: result
  when: credentials.username == ''

- name: start py1 service (with auth)
  supervisorctl:
    name: 'pys:py1'
    state: started
    server_url: http://127.0.0.1:9001
    username: '{{ credentials.username }}'
    password: '{{ credentials.password }}'
  register: result_with_auth
  when: credentials.username != ''

- command: "supervisorctl -c {{ remote_dir }}/supervisord.conf {% if credentials.username %}-u {{ credentials.username }} -p {{ credentials.password }}{% endif %} status"
  register: result_cmd
  failed_when: result_cmd.rc not in [0, 3]

- name: check that service is started
  assert:
    that:
      - (result is success and result_with_auth is skip) or (result is skip and result_with_auth is changed)
      - (result is changed and result_with_auth is skip) or (result is skip and result_with_auth is changed)

- name: check that service is running (part1) # py1.log content is checked below
  script:
    cmd: "files/sendProcessStdin.py 'pys:py1' 2 '{{ credentials.username }}' '{{ credentials.password }}'"
    executable: "{{ ansible_facts.python.executable }}"

- name: try again to start py1 service (without auth)
  supervisorctl:
    name: pys:py1
    state: started
    config: '{{ remote_dir }}/supervisord.conf'
  register: result
  when: credentials.username == ''

- name: try again to start py1 service (with auth)
  supervisorctl:
    name: pys:py1
    state: started
    server_url: http://127.0.0.1:9001
    username: '{{ credentials.username }}'
    password: '{{ credentials.password }}'
  register: result_with_auth
  when: credentials.username != ''

- name: check that service is already running
  assert:
    that:
      - (result is success and result_with_auth is skip) or (result is skip and result_with_auth is success)
      - (result is not changed and result_with_auth is skip) or (result is skip and result_with_auth is not changed)

- import_tasks: stop_supervisord.yml

# supervisord has been stopped, check logfile
- name: check that service has done what it was expected (part 2)
  shell: 'test "$(tail -2 {{ remote_dir }}/py1.log | head -1)" = ">>> 2"'

# restart supervisord and py1 service for next tasks
- import_tasks: start_supervisord.yml

- name: start py1 service (without auth)
  supervisorctl:
    name: 'pys:py1'
    state: started
    config: '{{ remote_dir }}/supervisord.conf'
  register: result
  when: credentials.username == ''

- name: start py1 service (with auth)
  supervisorctl:
    name: 'pys:py1'
    state: started
    server_url: http://127.0.0.1:9001
    username: '{{ credentials.username }}'
    password: '{{ credentials.password }}'
  register: result_with_auth
  when: credentials.username != ''

- name: check that service is started
  assert:
    that:
      - (result is success and result_with_auth is skip) or (result is skip and result_with_auth is changed)
      - (result is changed and result_with_auth is skip) or (result is skip and result_with_auth is changed)

#############################################################

- name: Check an error occurs when wrong credentials are used
  supervisorctl:
    name: pys:py1
    state: started
    server_url: http://127.0.0.1:9001
    username: '{{ credentials.username }}wrong_creds'
    password: '{{ credentials.password }}same_here'
  register: result
  failed_when: result is not skip and (result is success or result is not failed)
  when: credentials.username != ''

- name: Check an error occurs when wrong URL is used
  supervisorctl:
    name: pys:py1
    state: started
    server_url: http://127.0.0.1:9002
  register: result
  failed_when: result is success or result is not failed

- name: Check an error occurs when wrong config path is used
  supervisorctl:
    name: 'pys:py1'
    state: started
    config: '{{ remote_dir }}/supervisord_not_here.conf'
  register: result
  failed_when: result is success or result is not failed

- name: Check an error occurs wrong name is used (without auth)
  supervisorctl:
    name: 'invalid'
    state: started
    config: '{{ remote_dir }}/supervisord.conf'
  register: result
  failed_when: result is skip or (result is success or result is not failed)
  when: credentials.username == ''

- name: Check an error occurs wrong name is used (with auth)
  supervisorctl:
    name: 'invalid'
    state: started
    config: '{{ remote_dir }}/supervisord.conf'
    username: '{{ credentials.username }}wrong_creds'
    password: '{{ credentials.password }}same_here'
  register: result
  failed_when: result is skip or (result is success or result is not failed)
  when: credentials.username != ''
