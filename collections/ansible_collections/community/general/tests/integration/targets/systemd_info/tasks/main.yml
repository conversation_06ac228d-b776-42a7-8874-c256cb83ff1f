---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: skip Alpine
  meta: end_host
  when: ansible_distribution == 'Alpine'

- name: check ansible_service_mgr
  ansible.builtin.assert:
    that: ansible_service_mgr == 'systemd'

- name: Test systemd_facts
  block:

    - name: Run tests
      import_tasks: tests.yml

  when: >
    (ansible_distribution in ['RedHat', 'CentOS', 'ScientificLinux'] and ansible_distribution_major_version is version('7', '>=')) or
    ansible_distribution == 'Fedora' or
    (ansible_distribution == 'Ubuntu' and ansible_distribution_version is version('15.04', '>=')) or
    (ansible_distribution == 'Debian' and ansible_distribution_version is version('8', '>=')) or
    ansible_os_family == 'Suse' or
    ansible_distribution == 'Archlinux'