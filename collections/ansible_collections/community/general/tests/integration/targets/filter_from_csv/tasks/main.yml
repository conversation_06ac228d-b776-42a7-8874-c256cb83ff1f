---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Parse valid csv input
  assert:
    that:
      - "valid_comma_separated | community.general.from_csv  ==  expected_result"

- name: Parse valid csv input containing spaces with/without skipinitialspace=True
  assert:
    that:
      - "valid_comma_separated_spaces | community.general.from_csv(skipinitialspace=True)  ==  expected_result"
      - "valid_comma_separated_spaces | community.general.from_csv  !=  expected_result"

- name: Parse valid csv input with no headers with/without specifying fieldnames
  assert:
    that:
      - "valid_comma_separated_no_headers | community.general.from_csv(fieldnames=['id','name','role'])  ==  expected_result"
      - "valid_comma_separated_no_headers | community.general.from_csv  !=  expected_result"

- name: Parse valid pipe-delimited csv input with/without delimiter=|
  assert:
    that:
      - "valid_pipe_separated | community.general.from_csv(delimiter='|')  ==  expected_result"
      - "valid_pipe_separated | community.general.from_csv  !=  expected_result"

- name: Register result of invalid csv input when strict=False
  debug:
    var: "invalid_comma_separated | community.general.from_csv"
  register: _invalid_csv_strict_false

- name: Test invalid csv input when strict=False is successful
  assert:
    that:
      - _invalid_csv_strict_false is success

- name: Register result of invalid csv input when strict=True
  debug:
    var: "invalid_comma_separated | community.general.from_csv(strict=True)"
  register: _invalid_csv_strict_true
  ignore_errors: true

- name: Test invalid csv input when strict=True is failed
  assert:
    that:
      - _invalid_csv_strict_true is failed
      - _invalid_csv_strict_true.msg is search('Unable to process file:.*')
