---
####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Scan new devices.
  aix_devices:
    device: all
    state: present

- name: Scan new virtual devices (vio0).
  aix_devices:
    device: vio0
    state: present

- name: Removing IP alias to en0
  aix_devices:
    device: en0
    attributes:
      delalias4: **********,*************

- name: Removes ent2.
  aix_devices:
    device: ent2
    state: absent

- name: Put device en2 in Defined
  aix_devices:
    device: en2
    state: defined

- name: Removes ent4 (inexistent).
  aix_devices:
    device: ent4
    state: absent

- name: Put device en4 in Defined (inexistent)
  aix_devices:
    device: en4
    state: defined

- name: Put vscsi1 and children devices in Defined state.
  aix_devices:
    device: vscsi1
    recursive: true
    state: defined

- name: Removes vscsi1 and children devices.
  aix_devices:
    device: vscsi1
    recursive: true
    state: absent

- name: Changes en1 mtu to 9000 and disables arp.
  aix_devices:
    device: en1
    attributes:
      mtu: 900
      arp: 'off'
    state: present

- name: Configure IP, netmask and set en1 up.
  aix_devices:
    device: en1
    attributes:
      netaddr: *************
      netmask: *************
      state: up
    state: present

- name: Adding IP alias to en0
  aix_devices:
    device: en0
    attributes:
      alias4: **********,*************
    state: present
