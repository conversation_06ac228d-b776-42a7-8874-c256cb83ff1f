---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: 'Create a "disk" file'
  community.general.filesize:
    path: '{{ image_file }}'
    size: '{{ fssize }}M'
    force: true

- vars:
    dev: '{{ image_file }}'
  block:
    - when: fstype == 'lvm'
      block:
        - name: 'Show next free loop device'
          ansible.builtin.command:
            cmd: 'losetup -f'
          register: loop_device_cmd

        - name: 'Create a loop device for LVM'
          ansible.builtin.command:
            cmd: 'losetup -f {{ dev }}'

        - name: 'Switch to loop device target for further tasks'
          ansible.builtin.set_fact:
            dev: "{{ loop_device_cmd.stdout }}"

    - when: fstype == 'ufs'
      block:
        - name: 'Create a memory disk for UFS'
          ansible.builtin.command:
            cmd: 'mdconfig -a -f {{ dev }}'
          register: memory_disk_cmd

        - name: 'Switch to memory disk target for further tasks'
          ansible.builtin.set_fact:
            dev: "/dev/{{ memory_disk_cmd.stdout }}"

    - include_tasks: '{{ action }}.yml'

  always:
    - name: 'Detach loop device used for LVM'
      ansible.builtin.command:
        cmd: 'losetup -d {{ dev }}'
        removes: '{{ dev }}'
      when: fstype == 'lvm'

    - name: 'Detach memory disk used for UFS'
      ansible.builtin.command:
        cmd: 'mdconfig -d -u {{ dev }}'
        removes: '{{ dev }}'
      when: fstype == 'ufs'

    - name: 'Clean correct device for LVM and UFS'
      ansible.builtin.set_fact:
        dev: '{{ image_file }}'
      when: fstype in ['lvm', 'ufs']

    - name: 'Remove disk image file'
      ansible.builtin.file:
        name: '{{ image_file }}'
        state: absent
