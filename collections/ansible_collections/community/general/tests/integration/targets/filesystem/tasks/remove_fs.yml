---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

# We assume 'create_fs' tests have passed.

- name: "Create filesystem"
  community.general.filesystem:
    dev: '{{ dev }}'
    fstype: '{{ fstype }}'

- name: "Get filesystem UUID with 'blkid'"
  ansible.builtin.shell:
    cmd: "{{ get_uuid_cmd }}"
  changed_when: false
  register: blkid_ref

- name: "Assert that a filesystem exists on top of the device"
  ansible.builtin.assert:
    that:
      - blkid_ref.stdout | length > 0


# Test check_mode first
- name: "Remove filesystem (check mode)"
  community.general.filesystem:
    dev: '{{ dev }}'
    state: absent
  register: wipefs
  check_mode: true

- name: "Get filesystem UUID with 'blkid' (should remain the same)"
  ansible.builtin.shell:
    cmd: "{{ get_uuid_cmd }}"
  changed_when: false
  register: blkid

- name: "Assert that the state changed but the filesystem still exists"
  ansible.builtin.assert:
    that:
      - wipefs is changed
      - blkid.stdout == blkid_ref.stdout

# Do it
- name: "Remove filesystem"
  community.general.filesystem:
    dev: '{{ dev }}'
    state: absent
  register: wipefs

- name: "Get filesystem UUID with 'blkid' (should be empty)"
  ansible.builtin.shell:
    cmd: "{{ get_uuid_cmd }}"
  changed_when: false
  failed_when: false
  register: blkid

- name: "Assert that the state changed and the device has no filesystem"
  ansible.builtin.assert:
    that:
      - wipefs is changed
      - blkid.stdout | length == 0
      - blkid.rc == 2

# Do it again
- name: "Remove filesystem (idempotency)"
  community.general.filesystem:
    dev: '{{ dev }}'
    state: absent
  register: wipefs

- name: "Assert that the state did not change"
  ansible.builtin.assert:
    that:
      - wipefs is not changed

# and again
- name: "Remove filesystem (idempotency, check mode)"
  community.general.filesystem:
    dev: '{{ dev }}'
    state: absent
  register: wipefs
  check_mode: true

- name: "Assert that the state did not change"
  ansible.builtin.assert:
    that:
      - wipefs is not changed


# By the way, test removal of a filesystem on unexistent device
- name: "Remove filesystem (unexistent device)"
  community.general.filesystem:
    dev: '/dev/unexistent_device'
    state: absent
  register: wipefs

- name: "Assert that the state did not change"
  ansible.builtin.assert:
    that:
      - wipefs is not changed
