---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

trigger:
  batch: true
  branches:
    include:
      - main
      - stable-*

pr:
  autoCancel: true
  branches:
    include:
      - main
      - stable-*

schedules:
  - cron: 0 8 * * *
    displayName: Nightly (main)
    always: true
    branches:
      include:
        - main
  - cron: 0 10 * * *
    displayName: Nightly (active stable branches)
    always: true
    branches:
      include:
        - stable-11
        - stable-10
  - cron: 0 11 * * 0
    displayName: Weekly (old stable branches)
    always: true
    branches:
      include:
        - stable-9

variables:
  - name: checkoutPath
    value: ansible_collections/community/general
  - name: coverageBranches
    value: main
  - name: entryPoint
    value: tests/utils/shippable/shippable.sh
  - name: fetchDepth
    value: 0

resources:
  containers:
    - container: default
      image: quay.io/ansible/azure-pipelines-test-container:6.0.0

pool: Standard

stages:
### Sanity
  - stage: Sanity_devel
    displayName: Sanity devel
    dependsOn: []
    jobs:
      - template: templates/matrix.yml
        parameters:
          nameFormat: Test {0}
          testFormat: devel/sanity/{0}
          targets:
            - test: 1
            - test: 2
            - test: 3
            - test: 4
  - stage: Sanity_2_18
    displayName: Sanity 2.18
    dependsOn: []
    jobs:
      - template: templates/matrix.yml
        parameters:
          nameFormat: Test {0}
          testFormat: 2.18/sanity/{0}
          targets:
            - test: 1
            - test: 2
            - test: 3
            - test: 4
  - stage: Sanity_2_17
    displayName: Sanity 2.17
    dependsOn: []
    jobs:
      - template: templates/matrix.yml
        parameters:
          nameFormat: Test {0}
          testFormat: 2.17/sanity/{0}
          targets:
            - test: 1
            - test: 2
            - test: 3
            - test: 4
### Units
  - stage: Units_devel
    displayName: Units devel
    dependsOn: []
    jobs:
      - template: templates/matrix.yml
        parameters:
          nameFormat: Python {0}
          testFormat: devel/units/{0}/1
          targets:
            - test: 3.8
            - test: 3.9
            - test: '3.10'
            - test: '3.11'
            - test: '3.12'
            - test: '3.13'
  - stage: Units_2_18
    displayName: Units 2.18
    dependsOn: []
    jobs:
      - template: templates/matrix.yml
        parameters:
          nameFormat: Python {0}
          testFormat: 2.18/units/{0}/1
          targets:
            - test: 3.8
            - test: "3.13"
  - stage: Units_2_17
    displayName: Units 2.17
    dependsOn: []
    jobs:
      - template: templates/matrix.yml
        parameters:
          nameFormat: Python {0}
          testFormat: 2.17/units/{0}/1
          targets:
            - test: 3.7
            - test: "3.12"

## Remote
  - stage: Remote_devel_extra_vms
    displayName: Remote devel extra VMs
    dependsOn: []
    jobs:
      - template: templates/matrix.yml
        parameters:
          testFormat: devel/{0}
          targets:
            - name: Alpine 3.21
              test: alpine/3.21
            # - name: Fedora 41
            #   test: fedora/41
            - name: Ubuntu 22.04
              test: ubuntu/22.04
            - name: Ubuntu 24.04
              test: ubuntu/24.04
          groups:
            - vm
  - stage: Remote_devel
    displayName: Remote devel
    dependsOn: []
    jobs:
      - template: templates/matrix.yml
        parameters:
          testFormat: devel/{0}
          targets:
            - name: macOS 15.3
              test: macos/15.3
            - name: RHEL 10.0
              test: rhel/10.0
            - name: RHEL 9.5
              test: rhel/9.5
            - name: FreeBSD 14.2
              test: freebsd/14.2
            - name: FreeBSD 13.5
              test: freebsd/13.5
          groups:
            - 1
            - 2
            - 3
  - stage: Remote_2_18
    displayName: Remote 2.18
    dependsOn: []
    jobs:
      - template: templates/matrix.yml
        parameters:
          testFormat: 2.18/{0}
          targets:
            - name: macOS 14.3
              test: macos/14.3
            - name: RHEL 9.4
              test: rhel/9.4
            - name: FreeBSD 14.1
              test: freebsd/14.1
          groups:
            - 1
            - 2
            - 3
  - stage: Remote_2_17
    displayName: Remote 2.17
    dependsOn: []
    jobs:
      - template: templates/matrix.yml
        parameters:
          testFormat: 2.17/{0}
          targets:
            - name: FreeBSD 13.3
              test: freebsd/13.3
            - name: RHEL 9.3
              test: rhel/9.3
          groups:
            - 1
            - 2
            - 3

### Docker
  - stage: Docker_devel
    displayName: Docker devel
    dependsOn: []
    jobs:
      - template: templates/matrix.yml
        parameters:
          testFormat: devel/linux/{0}
          targets:
            - name: Fedora 41
              test: fedora41
            - name: Alpine 3.21
              test: alpine321
            - name: Ubuntu 22.04
              test: ubuntu2204
            - name: Ubuntu 24.04
              test: ubuntu2404
          groups:
            - 1
            - 2
            - 3
  - stage: Docker_2_18
    displayName: Docker 2.18
    dependsOn: []
    jobs:
      - template: templates/matrix.yml
        parameters:
          testFormat: 2.18/linux/{0}
          targets:
            - name: Fedora 40
              test: fedora40
            - name: Alpine 3.20
              test: alpine320
            - name: Ubuntu 24.04
              test: ubuntu2404
          groups:
            - 1
            - 2
            - 3
  - stage: Docker_2_17
    displayName: Docker 2.17
    dependsOn: []
    jobs:
      - template: templates/matrix.yml
        parameters:
          testFormat: 2.17/linux/{0}
          targets:
            - name: Fedora 39
              test: fedora39
            - name: Alpine 3.19
              test: alpine319
            - name: Ubuntu 20.04
              test: ubuntu2004
          groups:
            - 1
            - 2
            - 3

### Community Docker
  - stage: Docker_community_devel
    displayName: Docker (community images) devel
    dependsOn: []
    jobs:
      - template: templates/matrix.yml
        parameters:
          testFormat: devel/linux-community/{0}
          targets:
            - name: Debian Bullseye
              test: debian-bullseye/3.9
            - name: Debian Bookworm
              test: debian-bookworm/3.11
            - name: ArchLinux
              test: archlinux/3.13
          groups:
            - 1
            - 2
            - 3

### Generic
# Right now all generic tests are disabled. Uncomment when at least one of them is re-enabled.
#  - stage: Generic_devel
#    displayName: Generic devel
#    dependsOn: []
#    jobs:
#      - template: templates/matrix.yml
#        parameters:
#          nameFormat: Python {0}
#          testFormat: devel/generic/{0}/1
#          targets:
#            - test: '3.8'
#            - test: '3.11'
#            - test: '3.13'
#  - stage: Generic_2_18
#    displayName: Generic 2.18
#    dependsOn: []
#    jobs:
#      - template: templates/matrix.yml
#        parameters:
#          nameFormat: Python {0}
#          testFormat: 2.18/generic/{0}/1
#          targets:
#            - test: '3.8'
#            - test: '3.13'
#  - stage: Generic_2_17
#    displayName: Generic 2.17
#    dependsOn: []
#    jobs:
#      - template: templates/matrix.yml
#        parameters:
#          nameFormat: Python {0}
#          testFormat: 2.17/generic/{0}/1
#          targets:
#            - test: '3.7'
#            - test: '3.12'

  - stage: Summary
    condition: succeededOrFailed()
    dependsOn:
      - Sanity_devel
      - Sanity_2_18
      - Sanity_2_17
      - Units_devel
      - Units_2_18
      - Units_2_17
      - Remote_devel_extra_vms
      - Remote_devel
      - Remote_2_18
      - Remote_2_17
      - Docker_devel
      - Docker_2_18
      - Docker_2_17
      - Docker_community_devel
# Right now all generic tests are disabled. Uncomment when at least one of them is re-enabled.
#      - Generic_devel
#      - Generic_2_18
#      - Generic_2_17
    jobs:
      - template: templates/coverage.yml
